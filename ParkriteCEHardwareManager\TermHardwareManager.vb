Imports Parkrite.SystemShared

Public Class TermHardwareManager

#Region "Private variables"
    Private mID As Short
    Private mName As String
    Private mPort As String
    Private mProtocol As String
    Private mAndID As Byte
    Private mLEDEnabled As Boolean = False
    Private mShutterEnabled As Boolean = False
    Private mAntenna As AdvancedAnt
    Private mRemotePort As String

    Private mHardwareLog As New ErrlogEx("HardwareMgr", True, True, 31)
#End Region

#Region "Public properties"
    Public Property LEDEnabled() As Boolean
        Get
            Return mLEDEnabled
        End Get
        Set(ByVal Value As Boolean)
            mLEDEnabled = Value
        End Set
    End Property

    Public Property ShutterEnabled() As Boolean
        Get
            Return mShutterEnabled
        End Get
        Set(ByVal Value As Boolean)
            mShutterEnabled = Value
        End Set
    End Property

    Public ReadOnly Property VloopStatus() As LoopState
        Get
            If mAntenna Is Nothing Then
                Return LoopState.LOOPOFF
            Else
                If mAntenna.IsVloopOn = True Then
                    Return LoopState.LOOPON
                Else
                    Return LoopState.LOOPOFF
                End If
            End If
        End Get
    End Property

    Public ReadOnly Property NloopStatus() As LoopState
        Get
            If mAntenna Is Nothing Then
                Return LoopState.LOOPOFF
            Else
                If mAntenna.IsNloopOn = True Then
                    Return LoopState.LOOPON
                Else
                    Return LoopState.LOOPOFF
                End If
            End If
        End Get
    End Property

    Public ReadOnly Property BarrierIsClose() As Boolean
        Get
            Return Not mAntenna.IsBarrierOpen
        End Get
    End Property

    Public WriteOnly Property DetectInterval() As Integer
        Set(ByVal value As Integer)
            mAntenna.DetectInterval = value
        End Set
    End Property


#End Region

#Region "All events"
    Public Event VloopIsOn(ByVal iu As String)
    Public Event NloopIsOn()
    Public Event VloopIsOff()
    Public Event NloopIsOff()
    Public Event EvtBarrierIsOpen()
    Public Event IUDetected(ByVal iu As String)

#End Region

#Region "Constructor"
    Public Sub New(ByVal id As Short, _
                ByVal nm As String, _
                ByVal portnm As String, _
                ByVal antid As Byte, _
                ByVal devicetype As String, _
                ByVal remote As String)
        mID = id
        mName = nm
        mPort = portnm
        mAndID = antid + &HB0
        mProtocol = devicetype
        mRemotePort = remote
    End Sub
#End Region

#Region "Public member functions"

    Public Function SelfTest() As SelfTestResult
        Console.WriteLine("------ Hardware Selftest ------")
        mAntenna = New AdvancedAnt(mID, mName, mPort, mAndID, mProtocol, mRemotePort)
        If mAntenna Is Nothing Then
            Return SelfTestResult.HARDWAREERROR
        Else
            AddHandler mAntenna.IUDetected, AddressOf ProcessIU
            AddHandler mAntenna.VloopON, AddressOf ProcessVloopON
            AddHandler mAntenna.VloopOFF, AddressOf ProcessVloopOFF
            AddHandler mAntenna.BarrierOpen, AddressOf ProcessBarrierOpen
            AddHandler mAntenna.OnLine, AddressOf ProcessOnline
            AddHandler mAntenna.OffLine, AddressOf ProcessOffline
            AddHandler mAntenna.FaultyIUDetected, AddressOf ProcessFaultyIU
            AddHandler mAntenna.NloopOFF, AddressOf ProcessNLoopff
            AddHandler mAntenna.BarrierClose, AddressOf ProcessBarrierClose
            mAntenna.BeginRun()
        End If
        Return SelfTestResult.FINE
    End Function

    Public Function CreateLED() As Boolean
        'If mLEDEnabled = True Then
        '    If mTerminalType = Enumeration.TERMINALOPTION.ENTRYTERM Then
        '        'mLED = New clsLED(4)
        '        mLED = New agateLED(4)
        '    Else
        '        'mLED = New clsLED(6)
        '        mLED = New agateLED(6)
        '    End If

        '    If mLED Is Nothing Then
        '        RaiseEvent HardwareError(ComCodes.TransactionTypeSelections.LEDCreationError, "LED Creation Failed!")
        '        Return False
        '    Else
        '        mLED.clearall()
        '        Return True
        '    End If
        'End If
    End Function

    Public Sub DisplayLCD(ByVal str As String, ByVal row As Byte)
        'If mLCDEnabled = True Then
        '    If row = 1 Then
        '        mLCD.write_row1(str)
        '    Else
        '        mLCD.write_row2(str)
        '        If str = "Pls insert cashcard" Then
        '            'Console.WriteLine(True)
        '        End If
        '    End If
        'End If

        'mLCD.write_all(str)
    End Sub

    Public Sub DisplayLED(ByVal str As String, ByVal str2 As String)
        'If mLEDEnabled = True Then
        '    If row = 1 Then
        '        mLED.write_row1(str, acolor.Red, aeffect.Simple)
        '    Else
        '        mLED.write_row2(str, acolor.Red, aeffect.Simple)
        '    End If
        'End If
    End Sub

    Public Sub DisplayLCD(ByVal str As String)
        'If mLCDEnabled = True Then
        '    mLCD.write_all(str)
        'End If
    End Sub


    Public Sub TurnOnFullSign()
        'mDigitalIO.TurnOnCarParkFullSign()
    End Sub

    Public Sub TurnOffFullSign()
        'mDigitalIO.TurnOffCarParkFullSign()
    End Sub

    Public Sub RestartAntenna()

    End Sub

    Public Function OpenBarrier() As Boolean
        Return mAntenna.OpenBarrier()
    End Function

    Public Sub CloseBarrier()
        mAntenna.CloseBarrier()
    End Sub
#End Region

#Region "Private member functions"
    Private Sub ProcessIU(ByVal sender As BasicAnt, ByVal iu As String, ByVal vlpon As Boolean)
        If vlpon = True Then
            SyncLock sender
                Try
                    Console.WriteLine(">>> Antenna IU Detected :" & iu)
                    RaiseEvent IUDetected(iu)
                Catch ex As Exception
                    mHardwareLog.Log("ProcessIU:" & ex.Message)
                    mHardwareLog.Log("ProcessIU:" & ex.StackTrace)
                End Try
            End SyncLock
        End If
    End Sub

    Private Sub ProcessVloopON(ByVal sender As BasicAnt, ByVal iu As String)
        SyncLock sender
            Try
                Console.WriteLine(">>> Antenna VLoop ON :" & iu)
                RaiseEvent VloopIsOn(iu)
            Catch ex As Exception
                mHardwareLog.Log("ProcessVloopON:" & ex.Message)
                mHardwareLog.Log("ProcessVloopON:" & ex.StackTrace)
            End Try
        End SyncLock
    End Sub

    Private Sub ProcessVloopOFF(ByVal sender As BasicAnt)
        Try
            RaiseEvent VloopIsOff()
        Catch ex As Exception
            mHardwareLog.Log("ProcessVloopOFF:" & ex.Message)
            mHardwareLog.Log("ProcessVloopOFF:" & ex.StackTrace)
        End Try
    End Sub

    Private Sub ProcessBarrierOpen(ByVal sender As BasicAnt)
        Try
            RaiseEvent EvtBarrierIsOpen()
        Catch ex As Exception
            mHardwareLog.Log("ProcessBarrierOpen:" & ex.Message)
            mHardwareLog.Log("ProcessBarrierOpen:" & ex.StackTrace)
        End Try
    End Sub

    Private Sub ProcessBarrierClose(ByVal sender As BasicAnt)

    End Sub

    Private Sub ProcessNLoopff(ByVal sender As BasicAnt)
        Try
            RaiseEvent NloopIsOff()
        Catch ex As Exception
            mHardwareLog.Log("ProcessNLoopff:" & ex.Message)
            mHardwareLog.Log("ProcessNLoopff:" & ex.StackTrace)
        End Try
    End Sub

    Private Sub ProcessFaultyIU(ByVal sender As BasicAnt, ByVal iu As String)

    End Sub

    Private Sub ProcessOnline(ByVal sender As BasicAnt)

    End Sub

    Private Sub ProcessOffline(ByVal sender As BasicAnt)

    End Sub
#End Region

End Class
