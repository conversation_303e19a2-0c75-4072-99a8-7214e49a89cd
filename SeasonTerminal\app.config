<?xml version="1.0" encoding="utf-8" ?>
<configuration>
  <configSections>
    <section name="Term" type="TermConfig, SeasonTerminal" />
    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" >
      <section name="My.MySettings" type="System.Configuration.ClientSettingsSection, System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    </sectionGroup>
  </configSections>
  <system.diagnostics>
    <sources>
      <!-- This section defines the logging configuration for My.Application.Log -->
      <source name="DefaultSource" switchName="DefaultSwitch">
        <listeners>
          <add name="FileLog"/>
          <!-- Uncomment the below section to write to the Application Event Log -->
          <!--<add name="EventLog"/>-->
        </listeners>
      </source>
    </sources>
    <switches>
      <add name="DefaultSwitch" value="Information" />
    </switches>
    <sharedListeners>
      <add name="FileLog"
           type="Microsoft.VisualBasic.Logging.FileLogTraceListener, Microsoft.VisualBasic, Version=8.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
           initializeData="FileLogWriter"/>
      <!-- Uncomment the below section and replace APPLICATION_NAME with the name of your application to write to the Application Event Log -->
      <!--<add name="EventLog" type="System.Diagnostics.EventLogTraceListener" initializeData="APPLICATION_NAME"/> -->
    </sharedListeners>
  </system.diagnostics>

  <Term>
    <serialports>
      <!-- Type can be "serial" or "udp" -->
      <!-- 
      "serial" is used for RS232. 
      "udp" is used for network.
	  "lpr"
      -->
      <!--
      Attributes can be id, name, type, port, host, remoteport
      id can be any short value
      name is the terminal name
      port: For type serial, it should be com port no. like "COM1"; For type udp, it should be 
            in the format of "IP:PortNo" like "**************:50000" which used to connect to remote network
            converter.
      host: The terminal IP address
      remoteport: For type serial it is not required; For type udp, it is set as the endpoint of the network converter,
                  in the format of  "IP:PortNo" like "***************:50000"   
                  
                   <add id="1" name="B1 Entry" type="udp" port="**************:50000" host="*************" remoteport="***************:50000"/>
      -->
      <!--<add id="1" name="B1 Entry" type="udp" port="**************:50000" host="************" remoteport="***************:50004"/>-->
   <add id="1" name="B1 Entry" type="lpr" port="**************:50000" host="***********" remoteport="***************:50004" lprip="**********" username="admin" password="Mecomb2663$"/>
    </serialports>
	  <!--<lprcameras>
		  <add id="LPR001" name="Entry LPR Camera" terminal="1" ip="**********"/>
	  </lprcameras>-->
  </Term>

  <applicationSettings>
    <My.MySettings>
      <setting name="DetectInterval" serializeAs="String">
        <value>1000</value>
      </setting>
      <setting name="NoExits" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="DebugMode" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="IsSentosaIntegration" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="CheckSeasonCarIUfromSMC" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="PartialSeasonDownload" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="CheckSeasonMotorCycleIUfromSMC" serializeAs="String">
        <value>True</value>
      </setting>
    </My.MySettings>
  </applicationSettings>

  <appSettings>
    <add key="TimeSync" value="False"/>
    <add key="DBConnection" value="Server=.\SQLEXPRESS;Database={0};User ID=mecomb;Password=*********;Trusted_Connection=False;Encrypt=False;Max Pool Size=100;Min Pool Size=10;Connect Timeout=200;APP=ParkritrTerminalsGUI;MultipleActiveResultSets=True;" />
  </appSettings >
</configuration>
