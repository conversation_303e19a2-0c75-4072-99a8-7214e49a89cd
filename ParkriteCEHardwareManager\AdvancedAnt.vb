Imports System.Threading
Public Class AdvancedAnt
    Inherits BasicAnt

    Delegate Sub ProcDEL()

#Region "Private variables"
    Private mOpenBarCmd1 As New List(Of Byte)
    Private mOpenBarCmd2 As New List(Of Byte)
    Private mCloseBarCmd1 As New List(Of Byte)
    Private mCloseBarCmd2 As New List(Of Byte)
    Private mOpenBarCmd3 As New List(Of Byte)
    Private mSetAntDataCmd As New List(Of Byte)
    Private mGetAntDataCmd As New List(Of Byte)
    Private mWaitHandlesGetAntData() As AutoResetEvent = {New AutoResetEvent(False), New AutoResetEvent(False)}
    Private mIsEntry As Boolean
    Private mHasLPR As Boolean
#End Region

#Region "Constructor"
    Public Sub New(ByVal id As Short, _
                    ByVal nm As String, _
                    ByVal portname As String, _
                    ByVal antid As Byte, _
                    ByVal protocol As String, _
                    ByVal remote As String)
        MyBase.New(id, nm, portname, antid, protocol, remote)
        Dim a() As Byte = {&H10, &H2, antid, &H0, &H32, &H31, &H0, &H0, &H2, &H1, &H1, &H10, &H3}
        mOpenBarCmd1.AddRange(a)
        BuildSendCmd(mOpenBarCmd1)
        mOpenBarCmd2.AddRange(a)
        mOpenBarCmd2.Item(10) = 0
        mOpenBarCmd2.Item(6) = 1
        BuildSendCmd(mOpenBarCmd2)
        mOpenBarCmd3.AddRange(a)
        mOpenBarCmd3.Item(10) = 0
        mOpenBarCmd3.Item(6) = 22
        BuildSendCmd(mOpenBarCmd3)

        mCloseBarCmd1.AddRange(a)
        mCloseBarCmd1.Item(6) = 9
        mCloseBarCmd1.Item(9) = 2
        mCloseBarCmd1.Item(10) = 2
        BuildSendCmd(mCloseBarCmd1)
        mCloseBarCmd2.AddRange(a)
        mCloseBarCmd2.Item(6) = 10
        mCloseBarCmd2.Item(9) = 2
        mCloseBarCmd2.Item(10) = 0
        BuildSendCmd(mCloseBarCmd2)
        Dim a1() As Byte = {&H10, &H2, antid, &H0, &H32, &H41, &H5, &H0, &H0, &H10, &H3}
        mGetAntDataCmd.AddRange(a1)
        BuildSendCmd(mGetAntDataCmd)
        Dim a2() As Byte = {&H10, &H2, antid, &H0, &H32, &H40, &H6, &H0, &HB, &H1, &H1, &H0, &H1, &H11, &H0, &H19, &H2C, &H1, &H0, &H3C, &H10, &H3}
        mSetAntDataCmd.AddRange(a2)
        BuildSendCmd(mSetAntDataCmd)
    End Sub
#End Region

#Region "Protected methods"
    Protected Overrides Sub InitBeforeRun()
        AddHandler MyBase.GetAntDataOK, AddressOf ProcessGetAntDataOK
        AddHandler MyBase.GetAntDataNAK, AddressOf ProcessGetAntDataNAK
        AddHandler MyBase.SetAntDataOK, AddressOf ProcessSetAntDataOK
        AddHandler MyBase.SetAntDataNAK, AddressOf ProcessSetAntDataNAK
        AddHandler MyBase.SetDONAK, AddressOf ProcessSetDoNAK
        AddHandler MyBase.OnLine, AddressOf ProcessOnline
        GetAntennaData()
        Dim r As Integer = WaitHandle.WaitAny(mWaitHandlesGetAntData, 5000, False)

        If r = WaitHandle.WaitTimeout Then ' Failed
            Console.WriteLine("Time out : Requesting Antenna Data")
            gLogger.Log("Time out : Requesting Antenna Data")
            'Throw New ApplicationException("Can not get antenna data due to timeout")
            'mJustStart = False
            'Return

            ''zaw added for this again if antenna time out,
            Console.WriteLine("Resend : Antenna Data")
            gLogger.Log("Resend : Antenna Data")
            GetAntennaData()
            r = WaitHandle.WaitAny(mWaitHandlesGetAntData, 5000, False)

            If r = WaitHandle.WaitTimeout Then ' Failed
                'Throw New ApplicationException("Can not set antenna data due to timeout")
                Console.WriteLine("Time out : Requesting Antenna Data")
                gLogger.Log("Time out : Requesting Antenna Data")
                mJustStart = False
                Return
            ElseIf r = 1 Then
                Console.WriteLine("NAK Reply : Requesting Antenna Data")
                gLogger.Log("NAK Reply : Requesting Antenna Data")
                Throw New ApplicationException("Can not set antenna data due to NAK")
            End If
            ''''''''''''''''''''''''''' end of setAntennaData''''''''''''''''''''''''''''''''''''''''''

        ElseIf r = 1 Then 'NAK
            Console.WriteLine("NAK Reply : Requesting Antenna Data")
            gLogger.Log("NAK Reply : Requesting Antenna Data")

            Console.WriteLine("Reset : Antenna Data")
            gLogger.Log("Reset : Antenna Data")
            SetAntennaData()
            r = WaitHandle.WaitAny(mWaitHandlesGetAntData, 5000, False)
            If r = WaitHandle.WaitTimeout Then ' Failed
                'Throw New ApplicationException("Can not set antenna data due to timeout")
                mJustStart = False
                Return
            ElseIf r = 1 Then
                Console.WriteLine("NAK Reply : Reset Antenna Data")
                gLogger.Log("NAK Reply : Reset Antenna Data")
                Throw New ApplicationException("Can not set antenna data due to NAK")
            End If
        End If
    End Sub

    Protected Overrides Sub PostHandle()
        gLogger.Log(String.Format("-----{0}: Enter PostHandle -----", Name))
        SendMsg(mOpenBarCmd2.ToArray)
        Dim aa As New ProcDEL(AddressOf SendLowLevel)
        aa.BeginInvoke(Nothing, Nothing)
    End Sub
#End Region

#Region "Public methods, properties"

    Public ReadOnly Property IsEntry()
        Get
            Return mIsEntry
        End Get
    End Property

    Public ReadOnly Property HasLPR()
        Get
            Return mHasLPR
        End Get
    End Property

    Public Function OpenBarrier(Optional ByVal checkloop As Boolean = True) As Boolean
        If checkloop = False OrElse Me.IsVloopOn = True Then
            SendMsg(mOpenBarCmd1.ToArray)
            Thread.Sleep(300)
            'mLevelIsLow = False
            SendMsg(mOpenBarCmd2.ToArray)
            Dim aa As New ProcDEL(AddressOf SendLowLevel)
            aa.BeginInvoke(Nothing, Nothing)

            Console.WriteLine("Opened Barrier!")
            Return True
        End If
        Return False
    End Function

    Public Sub CloseBarrier()
        SendMsg(mCloseBarCmd1.ToArray)
        Thread.Sleep(300)
        SendMsg(mCloseBarCmd2.ToArray)
    End Sub

    Public Sub testsend(ByVal b() As Byte)
        SendMsg(b)
    End Sub
#End Region

#Region "Private methods"

    Private Sub SendLowLevel()
        If mEvtLevelIsLow Is Nothing Then
            mEvtLevelIsLow = New AutoResetEvent(False)
            Dim i As Integer = 0
            'While mEvtLevelIsLow.WaitOne(500, False) = False
            While mEvtLevelIsLow.WaitOne(1000, False) = False 'zaw change to 1 sec
                If i Mod 2 = 0 Then
                    SendMsg(mOpenBarCmd3.ToArray)
                Else
                    SendMsg(mOpenBarCmd2.ToArray)
                End If
                i += 1
                If i >= 3 Then
                    gLogger.Log(String.Format("-----{0}: SendLowLevel tried {1} times and mLevelIsLow = {2} -----", Name, i + 1, mLevelIsLow))
                    Exit While
                End If
            End While
            SyncLock mLockEvtLevelLow
                mEvtLevelIsLow.Close()
                mEvtLevelIsLow = Nothing
            End SyncLock
            If i < 3 Then
                'gLogger.Log(String.Format("-----{0}: SendLowLevel succeeds after {1} tries -----", Name, i + 1))
            End If
        End If
        'Dim i As Integer = 0
        'While mLevelIsLow = False
        '    Thread.Sleep(60)
        '    If i Mod 2 = 0 Then
        '        SendMsg(mOpenBarCmd3.ToArray)
        '    Else
        '        SendMsg(mOpenBarCmd2.ToArray)
        '    End If
        '    i += 1
        '    If i >= 3 Then
        '        gLogger.Log(String.Format("----- SendLowLevel tried {0} times and mLevelIsLow = {1} -----", i + 1, mLevelIsLow))
        '        Exit While
        '    End If
        'End While
        
    End Sub

    Private Sub GetAntennaData()
        SendMsg(mGetAntDataCmd.ToArray)
    End Sub

    Private Sub SetAntennaData()
        SendMsg(mSetAntDataCmd.ToArray)
    End Sub

    Private Sub ProcessGetAntDataOK()
        Console.WriteLine("Get antenna data OK")
        mWaitHandlesGetAntData(0).Set()
    End Sub

    Private Sub ProcessGetAntDataNAK()
        Console.WriteLine("Get antenna data NAK")
        mWaitHandlesGetAntData(1).Set()
    End Sub

    Private Sub ProcessSetAntDataOK()
        Console.WriteLine("Set antenna data OK")
        mWaitHandlesGetAntData(0).Set()
    End Sub

    Private Sub ProcessSetAntDataNAK()
        Console.WriteLine("Set antenna data NAK")
        mWaitHandlesGetAntData(1).Set()
    End Sub

    Private Sub ProcessSetDoNAK()
        PauseQuery()
        ThreadPool.QueueUserWorkItem(AddressOf ReinitAnt, Nothing)
    End Sub

    Private Sub ProcessOnline(ByVal obj As BasicAnt)
        If mJustStart = False Then
            ProcessSetDoNAK()
        End If
    End Sub

    Private Sub ReinitAnt(ByVal obj As Object)
        gLogger.Log(String.Format("---- Enter ReinitAnt ----{0}", Name))
        Threading.Thread.Sleep(500)
        Try
            GetAntennaData()
            Dim r As Integer = WaitHandle.WaitAny(mWaitHandlesGetAntData, 5000, False)
            If r = WaitHandle.WaitTimeout Then ' Failed
                'Throw New ApplicationException("Can not get antenna data due to timeout")
                gLogger.Log(String.Format("Can not get antenna data due to timeout for {0}", Me.Name))
                Return
            ElseIf r = 1 Then 'NAK
                SetAntennaData()
                r = WaitHandle.WaitAny(mWaitHandlesGetAntData, 5000, False)
                If r = WaitHandle.WaitTimeout Then ' Failed
                    'Throw New ApplicationException("Can not set antenna data due to timeout")
                    gLogger.Log(String.Format("Can not set antenna data due to timeout for {0}", Name))
                    Return
                ElseIf r = 1 Then
                    gLogger.Log(String.Format("Can not set antenna data due to NAK for {0}", Name))
                    'Throw New ApplicationException("Can not set antenna data due to NAK")
                End If
            End If
        Catch ex As Exception
            gLogger.Log(ex.ToString & "in ReinitAnt for " & Name)
        Finally
            ResumeQuery()
            gLogger.Log(String.Format("---- Exit ReinitAnt ----{0}", Name))
        End Try

    End Sub
#End Region

End Class
