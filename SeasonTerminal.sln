﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.14.36401.2 d17.14
MinimumVisualStudioVersion = 10.0.40219.1
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "SeasonTerminal", "SeasonTerminal\SeasonTerminal.vbproj", "{B550F15F-DAFF-41C3-BA71-FF99289D0E56}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "ParkriteTermHardwareManager", "ParkriteCEHardwareManager\ParkriteTermHardwareManager.vbproj", "{93336638-0669-461A-B58B-25152F2E9380}"
EndProject
Project("{F184B08F-C81C-45F6-A57F-5ABD9991F28F}") = "ParkriteTermDatabaseManager", "ParkriteTermDatabaseManager\ParkriteTermDatabaseManager.vbproj", "{7ADCA0A1-EBF7-4981-A442-70F663F2B39A}"
EndProject
Project("{54435603-DBB4-11D2-8724-00A0C9A8B90C}") = "SeasonTerminalSetUp", "SEasonTerminalSetUp\SEasonTerminalSetUp.vdproj", "{F7D36A7D-9118-412D-AC3D-ACE6657E502B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "lib", "lib", "{02EA681E-C7D8-13C7-8484-4AC65E1B71E8}"
	ProjectSection(SolutionItems) = preProject
		dll\ParkriteSystemShared.dll = dll\ParkriteSystemShared.dll
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{B550F15F-DAFF-41C3-BA71-FF99289D0E56}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B550F15F-DAFF-41C3-BA71-FF99289D0E56}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B550F15F-DAFF-41C3-BA71-FF99289D0E56}.Debug|x86.ActiveCfg = Debug|x86
		{B550F15F-DAFF-41C3-BA71-FF99289D0E56}.Debug|x86.Build.0 = Debug|x86
		{B550F15F-DAFF-41C3-BA71-FF99289D0E56}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B550F15F-DAFF-41C3-BA71-FF99289D0E56}.Release|Any CPU.Build.0 = Release|Any CPU
		{B550F15F-DAFF-41C3-BA71-FF99289D0E56}.Release|x86.ActiveCfg = Release|x86
		{B550F15F-DAFF-41C3-BA71-FF99289D0E56}.Release|x86.Build.0 = Release|x86
		{93336638-0669-461A-B58B-25152F2E9380}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{93336638-0669-461A-B58B-25152F2E9380}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{93336638-0669-461A-B58B-25152F2E9380}.Debug|x86.ActiveCfg = Debug|Any CPU
		{93336638-0669-461A-B58B-25152F2E9380}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{93336638-0669-461A-B58B-25152F2E9380}.Release|Any CPU.Build.0 = Release|Any CPU
		{93336638-0669-461A-B58B-25152F2E9380}.Release|x86.ActiveCfg = Release|Any CPU
		{7ADCA0A1-EBF7-4981-A442-70F663F2B39A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7ADCA0A1-EBF7-4981-A442-70F663F2B39A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7ADCA0A1-EBF7-4981-A442-70F663F2B39A}.Debug|x86.ActiveCfg = Debug|Any CPU
		{7ADCA0A1-EBF7-4981-A442-70F663F2B39A}.Debug|x86.Build.0 = Debug|Any CPU
		{7ADCA0A1-EBF7-4981-A442-70F663F2B39A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7ADCA0A1-EBF7-4981-A442-70F663F2B39A}.Release|Any CPU.Build.0 = Release|Any CPU
		{7ADCA0A1-EBF7-4981-A442-70F663F2B39A}.Release|x86.ActiveCfg = Release|Any CPU
		{F7D36A7D-9118-412D-AC3D-ACE6657E502B}.Debug|Any CPU.ActiveCfg = Debug
		{F7D36A7D-9118-412D-AC3D-ACE6657E502B}.Debug|Any CPU.Build.0 = Debug
		{F7D36A7D-9118-412D-AC3D-ACE6657E502B}.Debug|x86.ActiveCfg = Debug
		{F7D36A7D-9118-412D-AC3D-ACE6657E502B}.Debug|x86.Build.0 = Debug
		{F7D36A7D-9118-412D-AC3D-ACE6657E502B}.Release|Any CPU.ActiveCfg = Release
		{F7D36A7D-9118-412D-AC3D-ACE6657E502B}.Release|Any CPU.Build.0 = Release
		{F7D36A7D-9118-412D-AC3D-ACE6657E502B}.Release|x86.ActiveCfg = Release
		{F7D36A7D-9118-412D-AC3D-ACE6657E502B}.Release|x86.Build.0 = Release
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {F963481B-3FAB-4CE7-9917-FEF4D46D1AF6}
	EndGlobalSection
EndGlobal
