﻿Imports System
Imports System.Reflection
Imports System.Runtime.InteropServices

' General Information about an assembly is controlled through the following 
' set of attributes. Change these attribute values to modify the information
' associated with an assembly.

' Review the values of the assembly attributes

<Assembly: AssemblyTitle("SeasonTerminal")> 
<Assembly: AssemblyDescription("Parkrite EPS Season Terminal")> 
<Assembly: AssemblyCompany("Mecomb Singapore Ltd")> 
<Assembly: AssemblyProduct("SeasonTerminal")> 
<Assembly: AssemblyCopyright("Copyright ©  2009")> 
<Assembly: AssemblyTrademark("Parkrite")> 

<Assembly: ComVisible(False)>

'The following GUID is for the ID of the typelib if this project is exposed to COM
<Assembly: Guid("a55dbe1b-5227-42fb-a317-d3167a5dcaeb")> 

' Version information for an assembly consists of the following four values:
'
'      Major Version
'      Minor Version 
'      Build Number
'      Revision
'
' You can specify all the values or you can default the Build and Revision Numbers 
' by using the '*' as shown below:
' <Assembly: AssemblyVersion("1.0.*")> 

<Assembly: AssemblyVersion("*******")> 
<Assembly: AssemblyFileVersion("*******")> 

' *******
' Programmer: Nay
' Date      : 201707
' Note      :
'   - Wait for SQL Database connection ready
'   - CheckSeasonCarIUFromSMC...

'*******
'Programmer: zaw
'Note :
' -  zaw 16/08/2018, solved the issue of 78 shelton way
' -  disable iu No sending to SMC while vloop starts time
' -  to avoid the last vehicle record sending to SMC while vloop start time and prevent to wrongly open the barrier for other non season vehicle.
' -  HandleVlpOn function at TerminalManager.vb