﻿<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="My" GeneratedClassName="MySettings" UseMySettingsClassName="true">
  <Profiles />
  <Settings>
    <Setting Name="DetectInterval" Type="System.Int32" Scope="Application">
      <Value Profile="(Default)">1000</Value>
    </Setting>
    <Setting Name="NoExits" Type="System.Boolean" Scope="Application">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="DebugMode" Type="System.Boolean" Scope="Application">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="IsSentosaIntegration" Type="System.Boolean" Scope="Application">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="CheckSeasonCarIUfromSMC" Type="System.Boolean" Scope="Application">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="PartialSeasonDownload" Type="System.Boolean" Scope="Application">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="CheckSeasonMotorCycleIUfromSMC" Type="System.Boolean" Scope="Application">
      <Value Profile="(Default)">True</Value>
    </Setting>
  </Settings>
</SettingsFile>