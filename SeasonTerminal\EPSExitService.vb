Imports Parkrite.ClientBackbone
Imports Parkrite.SystemShared
Imports Parkrite.SystemShared.Messaging
Imports System.Threading
Imports System.IO

Public Class EPSExitService
    Inherits EpsService
    Private mTimerDeducted As Timer
    'Private mDeductingIUs As New Hashtable
    Private mRequestReceipt As Boolean = False
    'Private mReceiptInfo As ReceiptInfo
    'Private mThreadPrint As Threading.Thread
    Private mStopPrintThread As Boolean = False
    Private mCurrentDetection As TransactionMsg
    Private mWaitforExtraProcess As New AutoResetEvent(False)
    Private mFullDebitTrys As Integer = 0
    Private mFullDebitLastTryTimer As Timer
    Private mIsHandledByCPT As Boolean = False
    Private mTemporyStop As New StopDetectContext
    'Private mLastDetecetdIU As String
    'Private mIUDetectionTimer As Threading.Timer
    'Private Event IUDetected(ByVal iu As String)
    Private LEDmsg As String
    Private LEDmsg1 As String
    Private mReceiptInfoLock As New Object
    Private mDeductLock As New Object
    Private mRetriedIU As New Hashtable

    Public Enum ContextStat
        WAITCHUDEBIT = 1
        CCARDNOTINSERTED = 2
        CHUDETECTFAILED = 3
        CHUDEBITFAILED = 4
        BALANCENOTENOUGH = 5
        WAITCPTDEBIT = 6
        WAITBOTHDEBIT = 7
        PROCESSINGBYCASHCARD = 8
        PROCESSINGBYCCFAILED = 9
        PROCESSEDBYCASHCARD = 10
        TICKETREDEMPTION = 11
    End Enum

    Private Class StopDetectContext
        Public TemporyStop As Boolean
        Public TimeStamp As DateTime
    End Class

    Private Class EntityContext
        Public NormalTranType As TransactionTypeSelections
        Public ReverseTranType As TransactionTypeSelections
        Public EntryTime As DateTime
        Public run As RunningStatus
        Public Status As ContextStat
        Public fee As Integer
        Public reserved As Object
        Public isFlexSeason As Boolean = False
    End Class

    Sub New(ByVal backboneMnger As ParkriteClient, ByVal termmgr As TerminalManager)
        MyBase.New(backboneMnger, termmgr)
        'AddHandler mTermMgr.MessageHandledByExitFullEPS, AddressOf HandleExitFullEpsMsg
        'AddHandler mTermMgr.EvtRequestReceipt, AddressOf handleRequestReceipt
        AddHandler termmgr.IUIsDetected, AddressOf IUdetectedhandler
        'Dim thrdDetection As New Threading.Thread(AddressOf keepIUDetectionEx)
        'thrdDetection.Start()
    End Sub

    Public ReadOnly Property FeeToDebit(ByVal iu As String) As Integer
        Get
            Dim objtmp As TerminalManager.EntityProcessing = mTermMgr.GetObjFromProcessing(iu)
            If IsNothing(objtmp) = False Then
                If IsNothing(objtmp.obj) = False Then
                    Dim contx As EntityContext = CType(objtmp.obj, EntityContext)
                    Select Case contx.Status
                        Case ContextStat.PROCESSINGBYCASHCARD
                            Return contx.fee
                        Case Else
                            Return -1
                    End Select
                End If
            End If
            Return -1
        End Get
    End Property

    'Public Property MyReceiptInfo() As ReceiptInfo
    '    Get
    '        Return mReceiptInfo
    '    End Get
    '    Set(ByVal Value As ReceiptInfo)
    '        SyncLock mReceiptInfoLock
    '            mReceiptInfo = Value
    '        End SyncLock
    '    End Set
    'End Property

    'Private Sub IUdetectedhandler(ByVal iu As String)
    '    SyncLock mDeductLock
    '        If mTermMgr.IsInProcessing(iu) = True Then
    '            Dim objtemp As TerminalManager.EntityProcessing = mTermMgr.GetObjFromProcessing(iu)
    '            If IsNothing(objtemp) = False Then
    '                If IsNothing(objtemp.obj) = False Then
    '                    Dim cntx As EntityContext = CType(objtemp.obj, EntityContext)
    '                    If HasCHU = True And Now.Subtract(objtemp.timelabel).TotalSeconds >= 7 Then
    '                        objtemp.timelabel = Now
    '                        Dim transmsg As New TransactionMsg
    '                        transmsg.TransactionType = ComCodes.TransactionTypeSelections.HPCRequestDeduct
    '                        transmsg.message1 = iu
    '                        transmsg.message2 = mTermMgr.CHUID
    '                        transmsg.message3 = mTermMgr.AntennaID
    '                        transmsg.message4 = cntx.fee
    '                        transmsg.message5 = -1
    '                        'mDeductingIUs.Add(iulabel, PrepareHourly(iulabel))
    '                        transmsg.mLog = False
    '                        transmsg.mToIP = backbone.HPCIPAddress
    '                        Try
    '                            mTermMgr.SendMsgToHPC(transmsg)
    '                            TerminalSite.TerminalLog.Log("DebitHourly - Has sent to hpc for debiting : " & iu)
    '                        Catch ex As Exception
    '                            mTermMgr.SendSimpleCmdToServer(ComCodes.TransactionTypeSelections.HPCOutOfService, _
    '                                    mTermMgr.TerminalType, backbone.SMCIPAddress)
    '                        End Try
    '                    ElseIf HasCHU = False Then  'for semieps or cashcard
    '                        If mTermMgr.HasCashCardSys = True Then
    '                            If mTermMgr.CashCardSys.StartIdentityAuthenticate() = True Then
    '                                mTermMgr.HDManager.DisplayLCD("Fee = $" & String.Format("{0:f}", CDbl(cntx.fee / 100)), 1)
    '                                mTermMgr.HDManager.DisplayLCD("Pls Insert CashCard", 2)
    '                                mTermMgr.HDManager.DisplayLED("Fee = $" & String.Format("{0:f}", CDbl(cntx.fee / 100)), "Pls Insert Card")
    '                                'mTermMgr.CashCardSys.StartIdentityAuthenticate()
    '                            End If
    '                        End If
    '                    End If
    '                End If
    '            End If
    '        Else    'newly detected iu, not in processing list
    '            ProcessDetectedIU(iu)
    '        End If
    '    End SyncLock
    'End Sub

    Private Sub IUdetectedhandler(ByVal iulabel As String)
        Try

            Console.WriteLine("Exit IU = " & iulabel) 'Malbinda 2014-10-27

            'Console.WriteLine("Exit IUdetectedhandler") 'Malbinda 2014-10-27
            'TerminalSite.TerminalLog.Log("Exit IUdetectedhandler") 'Malbinda 2015-05-20
            If mTermMgr.IsProcessed(iulabel) Then
                '************************************************************
                'Malbinda 2015-02-15
                Dim iuseas1 As ClsSeasonInfo
                Dim theresult1 As RunningStatus = IsSeason(iulabel, iuseas1)
                raiseIdentityAuthenticateSucceed(Me, iulabel, theresult1)
                AssemblyTransaction(iulabel, theresult1, iuseas1.SeasonType)
                '************************************************************
                HandleProcessedEntity(iulabel)
                Return
            ElseIf mTermMgr.IsInProcessing(iulabel) Then
                DoIfInProcessing(iulabel)
                Return
            End If

            TerminalSite.TerminalLog.Log("Exit Season IU: " & iulabel) 'Malbinda 2015-05-20

            Dim res As Boolean
            'If mIsAuthenticating = True Then
            mIULabel = iulabel
            Dim iuseeas As ClsSeasonInfo
            Dim HourlyRecord As New RunningHourly
            Dim SeasonRecord As New RunningSeason
            Dim theresult As RunningStatus = IsSeason(iulabel, iuseeas)
            Select Case theresult
                Case Enumeration.RunningStatus.ORIGINALSEASON, Enumeration.RunningStatus.SPECIALSEASON
                    'If iuseeas.SeasonType = -1 Then
                    raiseIdentityAuthenticateSucceed(Me, iulabel, Enumeration.RunningStatus.ORIGINALSEASON)
                    'mTermMgr.HDManager.DisplayLED("Season exit", Now.ToString("dd/MM/yyyy HH:mm:ss"))
                    'mTermMgr.HDManager.DisplayLCD("Season exit", 1)
                    'mTermMgr.HDManager.DisplayLCD(Now.ToString("dd/MM/yyyy HH:mm:ss"), 2)
                    If DateDiff(DateInterval.Day, Now, iuseeas.ValidTo) <= mTermMgr.expday Then
                        mTermMgr.HDManager.DisplayLED("Season exit", "Expire:" & iuseeas.ValidTo.ToString("dd/MM/yy"))
                        mTermMgr.HDManager.DisplayLCD("Season exit", 1)
                        mTermMgr.HDManager.DisplayLCD("Expire:" & iuseeas.ValidTo.ToString("dd/MM/yy"), 2)
                    Else
                        mTermMgr.HDManager.DisplayLED("Season exit", Now.ToString("dd/MM/yyyy HH:mm:ss"))
                        mTermMgr.HDManager.DisplayLCD("Season exit", 1)
                        mTermMgr.HDManager.DisplayLCD(Now.ToString("dd/MM/yyyy HH:mm:ss"), 2)
                    End If
                    res = mTermMgr.GetRunningSeasonRecord(iulabel, SeasonRecord)
                    If res = False Then
                        If iuseeas.IOCheck = True Then
                            mTermMgr.HDManager.DisplayLED("Double exit", "")
                            mTermMgr.HDManager.DisplayLCD("Double exit")
                            mTermMgr.SendSimpleErrorToSMC(iulabel, ComCodes.TransactionTypeSelections.ExitIUSeasonDoubleExit)
                            Return
                        Else
                            If IsNothing(SeasonRecord) Then
                                SeasonRecord = New RunningSeason
                            End If
                            SeasonRecord.SeasonNo = iulabel
                            SeasonRecord.EntryTime = Now
                        End If
                    End If
                    'AssembleSendSeasonExitTrans(mTermMgr, iuseeas, SeasonRecord)
                    If theresult = Enumeration.RunningStatus.ORIGINALSEASON Then
                        AssemblyTransaction(iulabel, Enumeration.RunningStatus.ORIGINALSEASON, SeasonRecord)
                    Else
                        AssemblyTransaction(iulabel, Enumeration.RunningStatus.SPECIALSEASON, SeasonRecord)
                    End If
                Case Else
                    mTermMgr.SendSimpleErrorToSMC(iulabel, ComCodes.TransactionTypeSelections.EntryIUNotAllowedDueToSeasonOnly) 'Malbinda 2015-02-14
            End Select
        Catch ex As Exception
            TerminalSite.ErrorLog.Log("Exit IUdetectedhandler : " & ex.Message)
        End Try

    End Sub


    Private Sub HandleFreePass(ByVal iu As String, _
        ByVal entrytime As DateTime, _
        ByVal runnstat As RunningStatus)
        raiseIdentityAuthenticateSucceed(Me, iu, runnstat)
        Try
            Dim trans As New TransactionMsg
            trans.TransactionType = ComCodes.TransactionTypeSelections.ExitIUHourlyFreePass
            trans.message1 = iu
            trans.message2 = mTermMgr.TermID
            trans.message3 = Now.ToString("dd/MM/yyyy HH:mm:ss")
            trans.message5 = runnstat
            trans.message6 = entrytime.ToString("dd/MM/yyyy HH:mm:ss")
            mTermMgr.AddTranAndSend(iu, trans, ComCodes.TransactionTypeSelections.ExitIUHourlyFreePassRemoved)
            mTermMgr.HDManager.DisplayLED("Free pass exit", trans.message3)
            mTermMgr.HDManager.DisplayLCD("Free pass exit", 1)
            mTermMgr.HDManager.DisplayLCD(trans.message3, 2)
            Dim runrdtermno As Integer = CType(trans.message2, String).Split(";")(0)
            If mTermMgr.IsTerminalInsideThisCarpark(runrdtermno) Then
                mTermMgr.RemoveRunning(trans.message1, True)
                mTermMgr.DBManager.DoRunningHourRecord(trans.message1, Parkrite.DatabaseManager.DBAction.DELETE)
            End If
        Catch ex As Exception
            TerminalSite.ErrorLog.Log("Exit HandleFreePass : " & ex.Message)
        End Try
    End Sub


    Private Sub HandleAuthorizedVehcle(ByVal iu As String, _
        ByVal entrytime As DateTime, _
        ByVal runnstat As RunningStatus)

        Try
            raiseIdentityAuthenticateSucceed(Me, iu, runnstat)
            Dim trans As New TransactionMsg
            trans.TransactionType = ComCodes.TransactionTypeSelections.ExitAuthorizedVehicle
            trans.message1 = iu
            trans.message2 = mTermMgr.TermID
            trans.message3 = Now.ToString("dd/MM/yyyy HH:mm:ss")
            trans.message5 = runnstat
            trans.message6 = entrytime.ToString("dd/MM/yyyy HH:mm:ss")
            mTermMgr.AddTranAndSend(iu, trans, ComCodes.TransactionTypeSelections.ExitAuthorizedVehicleRemoved)
            mTermMgr.HDManager.DisplayLED("Authorized Exit", trans.message3)
            mTermMgr.HDManager.DisplayLCD("Authorized Exit", 1)
            mTermMgr.HDManager.DisplayLCD(trans.message3, 2)
            Dim runrdtermno As Integer = CType(trans.message2, String).Split(";")(0)
            If mTermMgr.IsTerminalInsideThisCarpark(runrdtermno) Then
                mTermMgr.RemoveRunning(trans.message1, True)
                mTermMgr.DBManager.DoRunningHourRecord(trans.message1, Parkrite.DatabaseManager.DBAction.DELETE)
            End If
        Catch ex As Exception
            TerminalSite.ErrorLog.Log("Exit HandleAuthorizedVehcle : " & ex.Message)
        End Try

    End Sub


    Public Overrides Sub PauseIdentityAuthenticate()
        TerminalSite.TerminalLog.Log("PauseIdentityAuthenticate is being called in FullEPSExitService")

        MyBase.PauseIdentityAuthenticate()

        If Not mFullDebitLastTryTimer Is Nothing Then
            mFullDebitLastTryTimer.Dispose()
        End If


    End Sub


    Public Sub DisposePrintThread()
        mStopPrintThread = True
    End Sub


    Public Overrides Function selftest() As Parkrite.SystemShared.Enumeration.SelfTestResult

        If MyBase.selftest() <> Enumeration.SelfTestResult.FINE Then
            Return Enumeration.SelfTestResult.FATALEPSERROR
        End If

        Return SelfTestResult.FINE
    End Function

    Public Overrides ReadOnly Property ServiceType() As TypeOfService
        Get

        End Get
    End Property

    Public Overrides Sub init()

    End Sub

    Protected Overrides Sub AssemblyTransaction(ByVal ticket As String, ByVal runstat As RunningStatus, Optional ByVal runningInfo As Object = Nothing)
        Try
            Dim trans As New TransactionMsg
            trans.message1 = ticket
            trans.message2 = mTermMgr.TermID
            trans.message3 = Now.ToString("dd/MM/yyyy HH:mm:ss")
            trans.mMulticast = True
            trans.mLog = True
            Select Case runstat
                Case Enumeration.RunningStatus.ORIGINALSEASON, Enumeration.RunningStatus.SPECIALSEASON
                    trans.message6 = CType(runningInfo, RunningSeason).EntryTime.ToString("dd/MM/yyyy HH:mm:ss")
                    '***********************************************************
                    'Malbinda 2015-02-15
                    If runstat = RunningStatus.SPECIALSEASON Then
                        trans.TransactionType = ComCodes.TransactionTypeSelections.ExitIUSeasonFlexible
                        mTermMgr.AddTranAndSend(ticket, trans, ComCodes.TransactionTypeSelections.EntryIUSeasonFlexibleRemoved)
                        raiseIdentityAuthenticateSucceed(Me, ticket, Enumeration.RunningStatus.SPECIALSEASON)
                    Else
                        trans.TransactionType = ComCodes.TransactionTypeSelections.ExitIUSeason
                        mTermMgr.AddTranAndSend(ticket, trans, ComCodes.TransactionTypeSelections.ExitIUSeasonRemoved)
                        raiseIdentityAuthenticateSucceed(Me, ticket, Enumeration.RunningStatus.ORIGINALSEASON)
                    End If
                    '***********************************************************

                    Dim runrdtermno As Integer = CType(trans.message2, String).Split(";")(0)
                    If mTermMgr.IsTerminalInsideThisCarpark(runrdtermno) Then
                        '***********************************************************
                        'Malbinda 2014-10-27
                        'Put here the source code for the exit transaction to be process of the WebServiceInterface.exe
                        If runstat = RunningStatus.SPECIALSEASON Then
                            mTermMgr.DBManager.InsertExitSeasonRunningRecord(ticket, Now.ToString("yyyy-MM-dd HH:mm:ss"))
                        End If
                        '***********************************************************
                        mTermMgr.RemoveRunning(trans.message1, False)
                        mTermMgr.DBManager.DoRunningSeasonRecord(trans.message1, Parkrite.DatabaseManager.DBAction.DELETE)
                    End If

                Case Else

            End Select
        Catch ex As Exception
            TerminalSite.ErrorLog.Log("Exit AssemblyTransaction : " & ex.Message)
            '************************************************************************************
            'Malbinda 2015-08-21
            'Note: Call the RestartApp.exe to restart the SeasonTerminal Application
            '************************************************************************************
            Dim restfile As String = My.Computer.FileSystem.CurrentDirectory & "\RestartApp.exe"
            Dim thisprogram As String = Process.GetCurrentProcess.ProcessName
            If File.Exists(restfile) Then
                System.Diagnostics.Process.Start(restfile, thisprogram)
            End If
            '************************************************************************************
        End Try

    End Sub

    Private Function PrepareFlexiSeason(ByVal iulabel As String, ByVal seasrd As RunningSeason) As Integer
        'Dim typestr As String
        'Dim fee As Integer
        'Dim curtime As DateTime = Now
        'Dim transtype1, transtype2 As ComCodes.TransactionTypeSelections
        'Dim cntx As New EntityContext
        'transtype1 = ComCodes.TransactionTypeSelections.ExitIUSeasonFlexible
        'transtype2 = ComCodes.TransactionTypeSelections.ExitIUSeasonFlexibleRemoved
        'fee = mTermMgr.CalculateFee(seasrd.SeasonType, seasrd.EntryTime, curtime)
        'If fee = 0 Then
        '    raiseIdentityAuthenticateSucceed(Me, iulabel, Enumeration.RunningStatus.ORIGINALSEASON)
        '    mTermMgr.HDManager.DisplayLED("Season exit", Now.ToString("dd/MM/yyyy HH:mm:ss"))
        '    mTermMgr.HDManager.DisplayLCD("Season exit", 1)
        '    mTermMgr.HDManager.DisplayLCD(Now.ToString("dd/MM/yyyy HH:mm:ss"), 2)

        '    Dim trans As New TransactionMsg
        '    trans.TransactionType = ComCodes.TransactionTypeSelections.ExitIUSeasonFlexible
        '    trans.message1 = iulabel
        '    trans.message2 = mTermMgr.TermID
        '    trans.message3 = Now.ToString("dd/MM/yyyy HH:mm:ss")
        '    trans.message5 = RunningStatus.SPECIALSEASON
        '    trans.message6 = seasrd.EntryTime.ToString("dd/MM/yyyy HH:mm:ss")
        '    mTermMgr.AddTranAndSend(iulabel, trans, ComCodes.TransactionTypeSelections.ExitIUSeasonFlexibleRemoved)
        '    mTermMgr.HDManager.DisplayLED("Flex Season!", trans.message3)
        '    mTermMgr.HDManager.DisplayLCD("Flex Season!", 1)
        '    mTermMgr.HDManager.DisplayLCD(trans.message3, 2)
        '    Dim runrdtermno As Integer = CType(trans.message2, String).Split(";")(0)
        '    If mTermMgr.IsTerminalInsideThisCarpark(runrdtermno) Then
        '        mTermMgr.RemoveRunning(trans.message1, True)
        '        mTermMgr.DBManager.DoRunningSeasonRecord(trans.message1, Parkrite.DatabaseManager.DBAction.DELETE)
        '    End If
        'Else
        '    cntx.NormalTranType = transtype1
        '    cntx.ReverseTranType = transtype2
        '    cntx.EntryTime = seasrd.EntryTime
        '    If HasCHU = True Then
        '        cntx.Status = ContextStat.WAITCHUDEBIT
        '    Else
        '        cntx.Status = ContextStat.WAITCPTDEBIT
        '    End If

        '    cntx.run = Enumeration.RunningStatus.SPECIALSEASON
        '    cntx.fee = fee
        '    LEDmsg = "Fee = $" & String.Format("{0:f}", CDbl(fee / 100))
        '    mTermMgr.AddToInProcessing(iulabel, cntx)
        'End If

        'Return fee

    End Function




    'Protected Overrides Sub DoIfInProcessing(ByVal iu As String)
    '    Dim toProceed As Boolean
    '    SyncLock mRetriedIU.SyncRoot
    '        toProceed = Not mRetriedIU.Contains(iu)
    '    End SyncLock

    '    If toProceed = True Then
    '        If mTermMgr.mRecordNotFounds.Contains(iu) Then
    '            SyncLock mTermMgr.mRecordNotFounds.SyncRoot
    '                Dim info As TerminalManager.RecordNotFoundInfo = mTermMgr.mRecordNotFounds(iu)
    '                If info.Processing = False Then
    '                    info.Processing = True
    '                    'mLastDetecetdIU = iulabel
    '                    RaiseIUDetectedEvent(iu)
    '                    TerminalSite.TerminalLog.Log("DoIfInProcessing(RecordNotFound) - Will process detected IU : " & mIULabel)
    '                    'RaiseEPSIUDetected(mIULabel)
    '                    'DetectInterval = 100
    '                End If
    '            End SyncLock
    '        Else
    '            'Process iudetected
    '            'RaiseIUDetectedEvent(mIULabel)
    '            Dim objtmp As TerminalManager.EntityProcessing = mTermMgr.GetObjFromProcessing(iu)
    '            If IsNothing(objtmp) = False Then
    '                If IsNothing(objtmp.obj) = False Then
    '                    Dim contx As EntityContext = CType(objtmp.obj, EntityContext)
    '                    If contx.Status = ContextStat.WAITCHUDEBIT Then
    '                        If Now.Subtract(objtmp.timelabel).TotalSeconds >= 5 Then
    '                            If mTermMgr.HasCashCardSys Then
    '                                mTermMgr.CashCardSys.StartIdentityAuthenticate()
    '                                contx.Status = ContextStat.WAITBOTHDEBIT
    '                                mTermMgr.HDManager.DisplayLCD("Please insert", 1)
    '                                mTermMgr.HDManager.DisplayLCD("CashCard into reader", 2)
    '                                mTermMgr.HDManager.DisplayLED("Pls insert Cash-", "Card into reader")
    '                            End If
    '                        End If
    '                    ElseIf contx.Status = ContextStat.PROCESSINGBYCCFAILED Then
    '                        RaiseIUDetectedEvent(iu)
    '                        mTermMgr.CashCardSys.StartIdentityAuthenticate()
    '                        mTermMgr.HDManager.DisplayLCD("Fee = $" & String.Format("{0:f}", CDbl(contx.fee / 100)), 1)
    '                        mTermMgr.HDManager.DisplayLCD("Cannot debit by CPT", 2)
    '                        mTermMgr.HDManager.DisplayLED("Fee = $" & String.Format("{0:f}", CDbl(contx.fee / 100)), "Cannot debit")
    '                        TerminalSite.TerminalLog.Log("DoIfInProcessing(" & contx.Status.ToString & ") - Will process detected IU : " & mIULabel)
    '                    ElseIf contx.Status = ContextStat.TICKETREDEMPTION Then
    '                        RaiseIUDetectedEvent(iu)
    '                        mTermMgr.CashCardSys.StartIdentityAuthenticate()
    '                        mTermMgr.HDManager.DisplayLCD("Fee = $" & String.Format("{0:f}", CDbl(contx.fee / 100)), 1)
    '                        mTermMgr.HDManager.DisplayLCD("Insert into reader", 2)
    '                        mTermMgr.HDManager.DisplayLED("Fee = $" & String.Format("{0:f}", CDbl(contx.fee / 100)), "Insert into reader")
    '                        TerminalSite.TerminalLog.Log("DoIfInProcessing(" & contx.Status.ToString & ") - Will process detected IU : " & mIULabel)
    '                    ElseIf contx.Status = ContextStat.CCARDNOTINSERTED Then
    '                        'If Now.Subtract(objtmp.timelabel).TotalSeconds >= 5 Then
    '                        RaiseIUDetectedEvent(iu)
    '                        mTermMgr.CashCardSys.StartIdentityAuthenticate()
    '                        'objtmp.timelabel = Now
    '                        TerminalSite.TerminalLog.Log("DoIfInProcessing(CCARDNOTINSERTED) - Will process detected IU : " & mIULabel)
    '                        'End If
    '                    ElseIf contx.Status = ContextStat.WAITCPTDEBIT Then 'add on 11/04/08
    '                        RaiseIUDetectedEvent(iu)
    '                        mTermMgr.CashCardSys.StartIdentityAuthenticate()
    '                        TerminalSite.TerminalLog.Log("DoIfInProcessing(WAITCPTDEBIT) - Will process detected IU : " & mIULabel)
    '                    ElseIf contx.Status = ContextStat.PROCESSINGBYCASHCARD Then 'add on 11/04/08
    '                        'RaiseIUDetectedEvent(iu)
    '                        mTermMgr.CashCardSys.StartIdentityAuthenticate()
    '                        'contx.Status = ContextStat.WAITBOTHDEBIT
    '                        mTermMgr.HDManager.DisplayLCD("Fee = $" & String.Format("{0:f}", CDbl(contx.fee / 100)), 1)
    '                        mTermMgr.HDManager.DisplayLCD("Processing ...", 2)
    '                        mTermMgr.HDManager.DisplayLED("Fee = $" & String.Format("{0:f}", CDbl(contx.fee / 100)), "Processing ...")
    '                    ElseIf contx.Status = ContextStat.WAITBOTHDEBIT Then 'add on 14/04/08
    '                        mTermMgr.CashCardSys.StartIdentityAuthenticate()
    '                        mTermMgr.HDManager.DisplayLCD("Fee = $" & String.Format("{0:f}", CDbl(contx.fee / 100)), 1)
    '                        mTermMgr.HDManager.DisplayLCD("Insert into reader", 2)
    '                        mTermMgr.HDManager.DisplayLED("Fee = $" & String.Format("{0:f}", CDbl(contx.fee / 100)), "Insert into reader")
    '                        'RaiseIUDetectedEvent(iu)
    '                    End If
    '                End If
    '            End If
    '        End If
    '    End If
    'End Sub

    Protected Overrides Sub HandleProcessedEntity(ByVal ticket As String)
        Dim tmp As DateTime = mTermMgr.GetObjFromProcessed(ticket)
        If Now.Subtract(tmp).TotalMinutes <= 10 Then
            If mTermMgr.HDManager.BarrierIsClose = True Then
                mTermMgr.HDManager.OpenBarrier()
            Else
                mTermMgr.HDManager.OpenBarrier()    'add 24/06/08
            End If
        End If
    End Sub

    Public Sub UpdateProcessingEntityStatusByCashCard(ByVal iu As String, ByVal stat As ContextStat)
        Dim objtmp As TerminalManager.EntityProcessing = mTermMgr.GetObjFromProcessing(iu)
        If IsNothing(objtmp) = False Then
            If IsNothing(objtmp.obj) = False Then
                Dim contx As EntityContext = CType(objtmp.obj, EntityContext)
                contx.Status = stat
            End If
        End If
    End Sub

End Class
