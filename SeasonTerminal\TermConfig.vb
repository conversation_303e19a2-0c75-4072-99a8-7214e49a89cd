Imports System.Configuration
Public Class TermConfig
    Inherits ConfigurationSection

#Region "Static Fields"
    Private Shared mPropElement As ConfigurationProperty
    'Private Shared mLPRPropElement As ConfigurationProperty
    Private Shared mProperties As ConfigurationPropertyCollection
#End Region

#Region "Constructor"
    Shared Sub New()
        mPropElement = New ConfigurationProperty("serialports", GetType(TermElementCollection), Nothing, ConfigurationPropertyOptions.IsRequired)
        'mLPRPropElement = New ConfigurationProperty("lprcameras", GetType(LPRElementCollection), Nothing, ConfigurationPropertyOptions.IsRequired)
        mProperties = New ConfigurationPropertyCollection
        mProperties.Add(mPropElement)
        'mProperties.Add(mLPRPropElement)
    End Sub

#Region "Properties"
    '<ConfigurationProperty("lprcameras", IsRequired:=True)> _
    'Public ReadOnly Property LPRPropElement() As LPRElementCollection
    '    Get
    '        Return CType(MyBase.Item(mLPRPropElement), LPRElementCollection)
    '    End Get
    'End Property

    <ConfigurationProperty("serialports", IsRequired:=True)> _
    Public ReadOnly Property PropElement() As TermElementCollection
        Get
            Return CType(MyBase.Item(mPropElement), TermElementCollection)
        End Get
    End Property

    Protected Overrides ReadOnly Property Properties() As ConfigurationPropertyCollection
        Get
            Return mProperties
        End Get

    End Property
#End Region

#End Region

End Class

<ConfigurationCollection(GetType(TermElement), _
    CollectionType:=ConfigurationElementCollectionType.AddRemoveClearMap)> _
Public Class TermElementCollection
    Inherits ConfigurationElementCollection

#Region "Fields"
    Private Shared mProperties As ConfigurationPropertyCollection
#End Region

#Region "Constructors"
    Shared Sub New()
        mProperties = New ConfigurationPropertyCollection()
    End Sub

#End Region

#Region "Properties"
    Protected Overrides ReadOnly Property Properties() As ConfigurationPropertyCollection
        Get
            Return mProperties
        End Get
    End Property

    Public Overrides ReadOnly Property CollectionType() As System.Configuration.ConfigurationElementCollectionType
        Get
            Return ConfigurationElementCollectionType.AddRemoveClearMap
        End Get
    End Property

    Default Public Property This(ByVal i As Integer) As TermElement
        Get
            Return CType(MyBase.BaseGet(i), TermElement)
        End Get
        Set(ByVal value As TermElement)
            If MyBase.BaseGet(i) IsNot Nothing Then
                MyBase.BaseRemoveAt(i)
            End If
            MyBase.BaseAdd(i, value)
        End Set
    End Property

    Default Public ReadOnly Property This(ByVal nm As String) As TermElement
        Get
            Return CType(MyBase.BaseGet(nm), TermElement)
        End Get
    End Property

#End Region

    Protected Overloads Overrides Function CreateNewElement() As System.Configuration.ConfigurationElement
        'Try
        Return New TermElement()
        'Catch ex As Exception
        '    Console.WriteLine(True)
        'End Try

    End Function

    Protected Overrides Function GetElementKey(ByVal element As System.Configuration.ConfigurationElement) As Object
        Return CType(element, TermElement).NameValue
    End Function
End Class




Public Class TermElement
    Inherits ConfigurationElement


#Region "Static Fields"
    Private Shared mPropId As ConfigurationProperty
    Private Shared mPropName As ConfigurationProperty
    Private Shared mPropType As ConfigurationProperty
    Private Shared mPropPort As ConfigurationProperty
    'Private Shared mPropAntId As ConfigurationProperty
    'Private Shared mIsEntry As ConfigurationProperty
    'Private Shared mRelatedId As ConfigurationProperty
    'Private Shared mHasLPR As ConfigurationProperty
    'Private Shared mRelatedId1 As ConfigurationProperty
    Private Shared mPropHost As ConfigurationProperty
    Private Shared mPropRemotePort As ConfigurationProperty
    Private Shared mPropLprIP As ConfigurationProperty
    Private Shared mPropUsername As ConfigurationProperty
    Private Shared mPropPassword As ConfigurationProperty

    Private Shared mProperties As ConfigurationPropertyCollection
#End Region

#Region "Constructor"
    Shared Sub New()
        mPropId = New ConfigurationProperty("id", GetType(Short), CType(0, Short), ConfigurationPropertyOptions.IsRequired)
        mPropName = New ConfigurationProperty("name", GetType(String), Nothing, ConfigurationPropertyOptions.IsRequired)
        mPropType = New ConfigurationProperty("type", GetType(String), Nothing, ConfigurationPropertyOptions.IsRequired)
        mPropPort = New ConfigurationProperty("port", GetType(String), Nothing, ConfigurationPropertyOptions.IsRequired)
        'mPropAntId = New ConfigurationProperty("antenna", GetType(String), Nothing, ConfigurationPropertyOptions.None)
        'mIsEntry = New ConfigurationProperty("isentry", GetType(Boolean), True, ConfigurationPropertyOptions.None)
        'mRelatedId = New ConfigurationProperty("relatedid", GetType(Short), CType(0, Short), ConfigurationPropertyOptions.None)
        'mHasLPR = New ConfigurationProperty("haslpr", GetType(Boolean), False, ConfigurationPropertyOptions.None)
        'mRelatedId1 = New ConfigurationProperty("relatedid1", GetType(Short), CType(0, Short), ConfigurationPropertyOptions.None)
        mPropHost = New ConfigurationProperty("host", GetType(String), Nothing, ConfigurationPropertyOptions.IsRequired)
        mPropRemotePort = New ConfigurationProperty("remoteport", GetType(String), Nothing, ConfigurationPropertyOptions.None)
        mPropLprIP = New ConfigurationProperty("lprip", GetType(String), Nothing, ConfigurationPropertyOptions.None)
        mPropUsername = New ConfigurationProperty("username", GetType(String), Nothing, ConfigurationPropertyOptions.None)
        mPropPassword = New ConfigurationProperty("password", GetType(String), Nothing, ConfigurationPropertyOptions.None)

        mProperties = New ConfigurationPropertyCollection()
        mProperties.Add(mPropId)
        mProperties.Add(mPropName)
        mProperties.Add(mPropType)
        mProperties.Add(mPropPort)
        mProperties.Add(mPropHost)
        mProperties.Add(mPropRemotePort)
        mProperties.Add(mPropLprIP)
        mProperties.Add(mPropUsername)
        mProperties.Add(mPropPassword)
        'mProperties.Add(mPropAntId)
        'mProperties.Add(mIsEntry)
        'mProperties.Add(mRelatedId)
        'mProperties.Add(mHasLPR)
        'mProperties.Add(mRelatedId1)

    End Sub

    'Sub New()

    'End Sub

#End Region


#Region "Properties"
    <ConfigurationProperty("id", IsRequired:=True)> _
   Public ReadOnly Property IDValue() As Short
        Get
            Return CType(MyBase.Item(mPropId), Short)
        End Get
    End Property

    <ConfigurationProperty("name", IsRequired:=True)> _
       Public ReadOnly Property NameValue() As String
        Get
            Return CType(MyBase.Item(mPropName), String)
        End Get
    End Property

    <ConfigurationProperty("type", IsRequired:=True)> _
       Public ReadOnly Property TypeValue() As String
        Get
            Return CType(MyBase.Item(mPropType), String)
        End Get
    End Property

    <ConfigurationProperty("port", IsRequired:=True)> _
       Public ReadOnly Property PortValue() As String
        Get
            Return CType(MyBase.Item(mPropPort), String)
        End Get
    End Property

    '<ConfigurationProperty("antenna", IsRequired:=False)> _
    '   Public ReadOnly Property AntIdValue() As String
    '    Get
    '        Return CType(MyBase.Item(mPropAntId), String)
    '    End Get
    'End Property

    '<ConfigurationProperty("isentry", IsRequired:=False)> _
    '   Public ReadOnly Property IsEntryValue() As Boolean
    '    Get
    '        Return CType(MyBase.Item(mIsEntry), Boolean)
    '    End Get
    'End Property

    '<ConfigurationProperty("relatedid", IsRequired:=False)> _
    '   Public ReadOnly Property RelatedIDValue() As Short
    '    Get
    '        Return CType(MyBase.Item(mRelatedId), Short)
    '    End Get
    'End Property

    '<ConfigurationProperty("haslpr", IsRequired:=False)> _
    '   Public ReadOnly Property HasLPR() As Boolean
    '    Get
    '        Return CType(MyBase.Item(mHasLPR), Boolean)
    '    End Get
    'End Property

    '<ConfigurationProperty("relatedid1", IsRequired:=False)> _
    '  Public ReadOnly Property RelatedID1Value() As Short
    '    Get
    '        Return CType(MyBase.Item(mRelatedId1), Short)
    '    End Get
    'End Property

    <ConfigurationProperty("host", IsRequired:=True)> _
       Public ReadOnly Property HostValue() As String
        Get
            Return CType(MyBase.Item(mPropHost), String)
        End Get
    End Property

    <ConfigurationProperty("remoteport", IsRequired:=True)> _
       Public ReadOnly Property RemotePortValue() As String
        Get
            Return CType(MyBase.Item(mPropRemotePort), String)
        End Get
    End Property


    <ConfigurationProperty("lprip", IsRequired:=False)>
    Public ReadOnly Property LprIPValue() As String
        Get
            Return CType(MyBase.Item(mPropLprIP), String)
        End Get
    End Property

    <ConfigurationProperty("username", IsRequired:=False)>
    Public ReadOnly Property UsernameValue() As String
        Get
            Return CType(MyBase.Item(mPropUsername), String)
        End Get
    End Property

    <ConfigurationProperty("password", IsRequired:=False)>
    Public ReadOnly Property PasswordValue() As String
        Get
            Return CType(MyBase.Item(mPropPassword), String)
        End Get
    End Property

    Protected Overrides ReadOnly Property Properties() As ConfigurationPropertyCollection
        Get
            Return mProperties
        End Get

    End Property

#End Region

End Class


<ConfigurationCollection(GetType(LPRElement), _
    CollectionType:=ConfigurationElementCollectionType.AddRemoveClearMap)> _
Public Class LPRElementCollection
    Inherits ConfigurationElementCollection

#Region "Fields"
    Private Shared mProperties As ConfigurationPropertyCollection
#End Region

#Region "Constructors"
    Shared Sub New()
        mProperties = New ConfigurationPropertyCollection()
    End Sub

#End Region

#Region "Properties"
    Protected Overrides ReadOnly Property Properties() As ConfigurationPropertyCollection
        Get
            Return mProperties
        End Get
    End Property

    Public Overrides ReadOnly Property CollectionType() As System.Configuration.ConfigurationElementCollectionType
        Get
            Return ConfigurationElementCollectionType.AddRemoveClearMap
        End Get
    End Property

    Default Public Property This(ByVal i As Integer) As LPRElement
        Get
            Return CType(MyBase.BaseGet(i), LPRElement)
        End Get
        Set(ByVal value As LPRElement)
            If MyBase.BaseGet(i) IsNot Nothing Then
                MyBase.BaseRemoveAt(i)
            End If
            MyBase.BaseAdd(i, value)
        End Set
    End Property

    Default Public ReadOnly Property This(ByVal nm As String) As LPRElement
        Get
            Return CType(MyBase.BaseGet(nm), LPRElement)
        End Get
    End Property

#End Region

    Protected Overloads Overrides Function CreateNewElement() As System.Configuration.ConfigurationElement
        'Try
        Return New LPRElement()
        'Catch ex As Exception
        '    Console.WriteLine(True)
        'End Try

    End Function

    Protected Overrides Function GetElementKey(ByVal element As System.Configuration.ConfigurationElement) As Object
        Return CType(element, LPRElement).NameValue
    End Function
End Class


Public Class LPRElement
    Inherits ConfigurationElement


#Region "Static Fields"
    Private Shared mCameraId As ConfigurationProperty
    Private Shared mCameraName As ConfigurationProperty
    Private Shared mTerminalID As ConfigurationProperty
    Private Shared mCameraIP As ConfigurationProperty  ' Add this
    Private Shared mProperties As ConfigurationPropertyCollection
#End Region

#Region "Constructor"
    Shared Sub New()
        mCameraId = New ConfigurationProperty("id", GetType(String), Nothing, ConfigurationPropertyOptions.IsRequired)
        mCameraName = New ConfigurationProperty("name", GetType(String), Nothing, ConfigurationPropertyOptions.IsRequired)
        mTerminalID = New ConfigurationProperty("terminal", GetType(Short), Nothing, ConfigurationPropertyOptions.IsRequired)
        mCameraIP = New ConfigurationProperty("ip", GetType(String), Nothing, ConfigurationPropertyOptions.IsRequired)  ' Add this
        mProperties = New ConfigurationPropertyCollection()
        mProperties.Add(mCameraId)
        mProperties.Add(mCameraName)
        mProperties.Add(mTerminalID)
        mProperties.Add(mCameraIP)  ' Add this
    End Sub

    'Sub New()

    'End Sub
    <ConfigurationProperty("ip", IsRequired:=True)>
    Public ReadOnly Property IPAddress() As String
        Get
            Return CType(MyBase.Item(mCameraIP), String)
        End Get
    End Property
#End Region

#Region "Properties"
    <ConfigurationProperty("id", IsRequired:=True)> _
   Public ReadOnly Property IDValue() As String
        Get
            Return CType(MyBase.Item(mCameraId), String)
        End Get
    End Property

    <ConfigurationProperty("name", IsRequired:=True)> _
       Public ReadOnly Property NameValue() As String
        Get
            Return CType(MyBase.Item(mCameraName), String)
        End Get
    End Property

    <ConfigurationProperty("terminal", IsRequired:=True)> _
       Public ReadOnly Property Terminal() As Short
        Get
            Return CType(MyBase.Item(mTerminalID), Short)
        End Get
    End Property
#End Region
End Class

