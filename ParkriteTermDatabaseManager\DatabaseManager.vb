'Imports Microsoft.SqlServer.Management.Smo
Imports System.Reflection
Imports System.Data.SqlClient
Imports System.IO
Imports System.Data
Imports System.Data.Common
Imports Parkrite.SystemShared.Messaging
Imports Parkrite.SystemShared
Imports System.Configuration

Public Class DatabaseManager

    Private Shared dblog As New ErrlogEx("TermDB", True, True, 120)
    Private connectionstr As String

    'Private connectionstr = "data source=.\SQLEXPRESS;initial catalog=MyDatabase;Integrated Security=SSPI;"
    Private Shared appPath As String = My.Application.Info.DirectoryPath

    ' Private Shared appPath As String = Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly.GetName().CodeBase)
    'Private Shared connectionstr As String = "Data Source=" & appPath & "\myDatabases\Season.sdf;Password=4602687;" 'the connection string
    'Private Shared Hourlystr As String = "Data Source=" & appPath & "\myDatabases\Hourly.sdf;Password=4602687;" 'the connection string
    'Private Shared connectionstr As String = "Data Source=" & appPath & "\myDatabases\Carpark.sdf;Password=4602687;" 'the connection string
    'Private Shared connectionstr As String = "Data Source=" & appPath & "\myDatabases\SystemInformation.sdf; Password=4602687" 'the connection string
    'Private Shared connectionstr As String = "Data Source=" & appPath & "\myDatabases\Rate.sdf; Password=4602687" 'the connection string
    'Private Shared connectionstr As String = "Data Source=" & appPath & "\myDatabases\RunningHourlyRecords.sdf; Password=4602687"
    'Private Shared connectionstr As String = "Data Source=" & appPath & "\myDatabases\RunningSeasonRecords.sdf; Password=4602687"
    'Private Shared connectionstr As String = "Data Source=" & appPath & "\myDatabases\AccessViolationRecords.sdf; Password=4602687"
    'Private Shared connectionstr As String = "Data Source=" & appPath & "\myDatabases\AccessViolationDebit.sdf; Password=4602687"
    'Private Shared connectionstr As String = "Data Source=" & appPath & "\myDatabases\Integration.sdf; Password=4602687"
    'Private Shared connectionstr As String = "Data Source=" & appPath & "\myDatabases\Options.sdf; Password=4602687"
    'Private Shared cmd As SqlCommand 'This is the object to execute all sql queries.
    Private mIUseasonDatacolumns As DataColumn() = {New DataColumn("IULabel"), New DataColumn("CashCardNo"), New DataColumn("ValidFrom", GetType(DateTime)), New DataColumn("ValidTo", GetType(DateTime)), New DataColumn("LicenceNo"), _
    New DataColumn("Tenant"), New DataColumn("Price"), New DataColumn("Name"), New DataColumn("NRIC"), New DataColumn("SeasonType"), New DataColumn("GroupNo"), _
    New DataColumn("Freeze"), New DataColumn("IOCheck"), New DataColumn("Remark")}
    Private Shared mCCseasonDatacolumns As DataColumn() = {New DataColumn("IULabel"), New DataColumn("ValidFrom", GetType(DateTime)), New DataColumn("ValidTo", GetType(DateTime)), New DataColumn("LicenceNo"), _
    New DataColumn("Tenant"), New DataColumn("Price"), New DataColumn("Name"), New DataColumn("NRIC"), New DataColumn("SeasonType"), New DataColumn("GroupNo"), _
    New DataColumn("Freeze"), New DataColumn("IOCheck"), New DataColumn("Remark")}

    Private mDBRunningHourQUE As New Queue
    Private mDBRunningSeasQUE As New Queue
    Private mDBSeasonQUE As New Queue
    Private mDBRunningAccessViolation As New Queue

    Private thrdDoRunHour As New Threading.Thread(AddressOf ProcessRunHourProc)
    Private thrdDoRunSeas As New Threading.Thread(AddressOf ProcessRunSeasProc)
    Private thrdDoSeason As New Threading.Thread(AddressOf ProcessSeasonProc)
    Private thrdDoAccessViolation As New Threading.Thread(AddressOf ProcessRunningAccessViolation)
    Private thrdOpsStarted As Boolean = False
    Private mNeedCreateTable As Boolean = True

    Public Enum DBAction
        UPDATE = 1
        DELETE = 2
        DELETEBYTIME = 3
    End Enum

    Public Enum DBSeasonDataType
        IUSeason = 1
        CashCardSeason = 2
        SeasonGroup = 3
        IUSeasonPermit = 4
        CCSeasonPermit = 5
        SeasonGroupPermit = 6
        Redemption = 7
        Complementary = 8
    End Enum

    Private Class DBSeasonActionContext
        Public obj As Object
        Public action As DBAction
        Public seasondata As DBSeasonDataType
        Public licenceNo As String
    End Class

    Private Class DBActionContext
        Public obj As Object
        Public action As DBAction
    End Class

    Private Sub TryOpenConnection(Conn As SqlConnection)
        Dim opened As Boolean = False
        While Not opened
            Try
                Conn.Open()
                opened = True
            Catch ex As Exception
                Console.WriteLine("Error on connecting SQL database. Retry to connect..." & Now.ToString("mm:ss"))
                dblog.Log("Error on connecting SQL database: ")
                dblog.Log(ex.ToString())
                Threading.Thread.Sleep(1000)
            End Try
        End While
    End Sub

    ' nay20170919
    Private Sub CheckPermission(dir As String, account As String)
        Dim di As New IO.DirectoryInfo(dir)
        Dim ds As Security.AccessControl.DirectorySecurity = di.GetAccessControl()
        Dim found As Boolean = False
        For Each r As Security.AccessControl.FileSystemAccessRule In ds.GetAccessRules(True, True, GetType(System.Security.Principal.NTAccount))
            If r.IdentityReference.Value.ToLower = account.ToLower AndAlso r.FileSystemRights = Security.AccessControl.FileSystemRights.FullControl AndAlso r.AccessControlType = Security.AccessControl.AccessControlType.Allow Then
                found = True
                Exit For
            End If
        Next
        If Not found Then
            Dim fsa As New Security.AccessControl.FileSystemAccessRule(account, Security.AccessControl.FileSystemRights.FullControl, Security.AccessControl.AccessControlType.Allow)
            ds.AddAccessRule(fsa)
            di.SetAccessControl(ds)
        End If
    End Sub

    Public Sub New(ByVal dbname As String)

        Try

            ''zaw, Database creation without using Microsoft.SqlServer.Management.Smo 23/08/2016 (server 2008 10.0.x)
            Dim Conn As New SqlConnection("Data Source=.\SQLEXPRESS;Initial Catalog=master;Integrated Security=True")
            mNeedCreateTable = True
            Using Conn
                TryOpenConnection(Conn) ' Conn.Open()

                Dim cmd1 As SqlCommand = New SqlCommand("sp_databases", Conn)
                Dim strdbName As String = ""
                cmd1.CommandType = CommandType.StoredProcedure
                Dim reader As SqlDataReader = cmd1.ExecuteReader

                While (reader.Read())
                    strdbName = CType(reader("DATABASE_NAME"), String)
                    If strdbName.ToUpper.Equals(dbname.ToUpper) Then
                        mNeedCreateTable = False
                        Exit While
                    End If
                End While
                reader.Close()

                If mNeedCreateTable = True Then
                    Dim cmd As SqlCommand = Conn.CreateCommand
                    Dim serviceaccount As String = String.Empty
                    cmd.CommandText = "SELECT service_account FROM sys.dm_server_services WHERE servicename='SQL Server (SQLEXPRESS)'"
                    reader = cmd.ExecuteReader
                    While reader.Read
                        serviceaccount = reader("service_account")
                    End While
                    reader.Close()

                    Dim str As String = "CREATE DATABASE {1} ON PRIMARY " & _
                     "(NAME = MyDatabase_Data, " & _
                     " FILENAME = '{2}\{0}.mdf', " & _
                     " SIZE = 10MB, " & _
                     " MAXSIZE = 2048MB, " & _
                     " FILEGROWTH = 10%) " & _
                     " LOG ON " & _
                     "(NAME = MyDatabase_Log, " & _
                     " FILENAME = '{2}\{0}.ldf', " & _
                     " SIZE = 10MB, " & _
                     " MAXSIZE = 2048MB, " & _
                     " FILEGROWTH = 10%) "

                    Dim dbname1 As String = "[" & dbname & "]"
                    Dim dir As String = appPath & "\myDatabases\mssql\"
                    CreateDirectory(dir)
                    If Not String.IsNullOrEmpty(serviceaccount) Then
                        CheckPermission(dir, serviceaccount)
                    End If

                    cmd.CommandText = String.Format(str, dbname, dbname1, dir)
                    cmd.ExecuteNonQuery()
                    Console.WriteLine("New Database is created. " & dbname)
                    dblog.Log("New Database is created. " & dbname)
                Else
                    Console.WriteLine("Database found. " & dbname)
                End If

                Conn.Close()
                Dim strConn As String
                strConn = ConfigurationManager.AppSettings("DBConnection")
                'connectionstr = String.Format("data source=.\SQLEXPRESS;initial catalog={0};Integrated Security=SSPI;", dbname)
                connectionstr = String.Format(strConn, dbname)
            End Using


        Catch ex As Exception
            Console.WriteLine("Database Creation Error.")
            Console.WriteLine(ex.ToString())
            dblog.Log("Database Creation Error.")
            dblog.Log(ex.ToString())
        End Try
        ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''


        ''Original Code uses  Imports Microsoft.SqlServer.Management.Smo (9.0.242.0) to create database....  (server 2005) 
        'connectionstr = String.Format("data source=.\SQLEXPRESS;initial catalog={0};Integrated Security=SSPI;", dbname)
        'Dim bv As New Server(".\SQLEXPRESS")
        'If bv.Databases.Contains(dbname) = False Then
        '    Dim bm As Database = New Database(bv, dbname)
        '    bm.Create()
        '    mNeedCreateTable = True
        'Else
        '    mNeedCreateTable = False
        'End If
    End Sub

    Public Sub CreateDirectory(ByVal path As String)
        If Not Directory.Exists(path) Then
            Directory.CreateDirectory(path)
        End If
    End Sub

    Public Sub CreateLocalDatabase()
        If mNeedCreateTable = True Then
            CreateTables()
        End If

        'CreateRateDatabase()
        'CreateSystemInfoDatabase()
        'CreateCarparkDatabase()
        'CreateRunningHourlyDatabase()
        'CreateRunningSeasonDatabase()
        'CreateAccessViolationDatabase()
        'CreateAccessViolationDebitDatabase()
        'CreateIntegrationDatabase()
        'CreateOptionsDatabase()
    End Sub

    Public Sub StartOpsThread()
        If thrdOpsStarted = False Then
            thrdDoRunHour.Start()
            thrdDoRunSeas.Start()
            thrdDoSeason.Start()
            thrdDoAccessViolation.Start()
            thrdOpsStarted = True
        End If
    End Sub

    Public Sub CreateTables()

        Dim sqlString As String
        Dim sqlcn As SqlConnection
        Dim cmd As SqlCommand
        Dim hasError As Boolean = False

        Try
            sqlcn = New SqlConnection(connectionstr)
            sqlcn.Open()
            cmd = sqlcn.CreateCommand
            'create the table and the fields.
            sqlString = "CREATE TABLE IUSeasonInfo (IULabel nchar(10) NOT NULL, CashCardNo nchar(16) NULL, " & _
                        "ValidFrom datetime NULL, ValidTo datetime NULL, LicenceNo nvarchar(40) NULL, " & _
                        "Tenant nvarchar(40) NULL, Price money NULL, Name nvarchar(40) NULL, NRIC nvarchar(40) NULL, " & _
                        "SeasonType integer NULL, GroupNo integer NULL, Freeze bit NULL, IOCheck bit NULL, Remark nvarchar(80) NULL)"

            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()



            'sqlString = "CREATE TABLE CashCardSeasonInfo (CashCardNo nchar(16) Not NULL, " & _
            '            "ValidFrom datetime NULL, ValidTo datetime NULL, LicenceNo nvarchar(40) NULL, " & _
            '            "Tenant nvarchar(40) NULL, Price money NULL, Name nvarchar(40) NULL, NRIC nvarchar(40) NULL, " & _
            '            "SeasonType integer NULL, GroupNo integer NULL, Freeze bit NULL, IOCheck bit NULL, Remark nvarchar(80) NULL)"

            'cmd.CommandText = sqlString
            'cmd.ExecuteNonQuery()

            'sqlString = "CREATE TABLE ProxSeasonInfo (ProximityNo nchar(16) Not NULL, " & _
            '            "ValidFrom datetime NULL, ValidTo datetime NULL, LicenceNo nvarchar(40) NULL, " & _
            '            "Tenant nvarchar(40) NULL, Price money NULL, Name nvarchar(40) NULL, NRIC nvarchar(40) NULL, " & _
            '            "SeasonType integer NULL, GroupNo integer NULL, Freeze bit NULL, IOCheck bit NULL, Remark nvarchar(80) NULL)"

            'cmd.CommandText = sqlString
            'cmd.ExecuteNonQuery()

            sqlString = "CREATE TABLE REMSeasonInfo (REMNo nchar(16) Not NULL, " & _
                        "ValidFrom datetime NULL, ValidTo datetime NULL, LicenceNo nvarchar(40) NULL, " & _
                        "Tenant nvarchar(40) NULL, Price money NULL, Name nvarchar(40) NULL, NRIC nvarchar(40) NULL, " & _
                        "SeasonType integer NULL, GroupNo integer NULL, Freeze bit NULL, IOCheck bit NULL, Remark nvarchar(80) NULL)"

            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()

            sqlString = "CREATE TABLE SeasonGroup (GroupNo integer NOT NULL, " & _
                        "GroupName nvarchar(50) NULL, Maximum integer NULL, Threshold integer NULL, " & _
                        "CurrentCount integer NULL, Phone nvarchar(30) NULL, Fax nvarchar(30) NULL, " & _
                        "Address nvarchar(30) NULL, Remark nvarchar(80) NULL)"

            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()

            'sqlString = "CREATE TABLE Complimentary (ComplimentaryNo nvarchar(40) NOT NULL, " & _
            '            "ValidFrom datetime NULL, ValidTo datetime NULL, ComplimentaryType smallint NULL)"

            'cmd.CommandText = sqlString
            'cmd.ExecuteNonQuery()

            'sqlString = "CREATE TABLE Redemption (RedemptionNo nvarchar(40) NOT NULL, " & _
            '            "ValidFrom datetime NULL, ValidTo datetime NULL, Value money NULL, " & _
            '            "ValueType smallint NULL, RedemptionType smallint NULL)"

            'cmd.CommandText = sqlString
            'cmd.ExecuteNonQuery()

            'sqlString = "CREATE TABLE Valet (CashCardNo nvarchar(16) NOT NULL, " & _
            '            "ValidFrom datetime NULL, ValidTo datetime NULL, Value money NULL, " & _
            '            "ValueType smallint NULL)"

            'cmd.CommandText = sqlString
            'cmd.ExecuteNonQuery()

            sqlString = "CREATE TABLE IUSeasonPermits (Id integer NOT NULL, " & _
                        "IULabel nvarchar(10) NULL, CarparkNo smallint NULL, SubCarparkNo smallint NULL)"

            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()

            'sqlString = "CREATE TABLE CashCardSeasonPermits (Id integer NOT NULL, " & _
            '            "CashCardNo nvarchar(16) NULL, CarparkNo smallint NULL, SubCarparkNo smallint NULL)"

            'cmd.CommandText = sqlString
            'cmd.ExecuteNonQuery()

            'sqlString = "CREATE TABLE ProxSeasonPermits (Id integer NOT NULL, " & _
            '            "ProximityNo nvarchar(40) NULL, CarparkNo smallint NULL, SubCarparkNo smallint NULL)"

            'cmd.CommandText = sqlString
            'cmd.ExecuteNonQuery()

            'sqlString = "CREATE TABLE REMSeasonPermits (Id integer NOT NULL, " & _
            '            "REMNo nvarchar(40) NULL, CarparkNo smallint NULL, SubCarparkNo smallint NULL)"

            'cmd.CommandText = sqlString
            'cmd.ExecuteNonQuery()

            sqlString = "CREATE TABLE GroupSeasonPermits (Id integer NOT NULL, " & _
                        "GroupNo integer NULL, CarparkNo smallint NULL, SubCarparkNo smallint NULL)"

            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()

            'sqlString = "CREATE TABLE SeasonType (SeasonType integer IDENTITY(1, 1) NOT NULL, " & _
            '            "RateType integer NULL, Description nvarchar(80) NULL)"

            'cmd.CommandText = sqlString
            'cmd.ExecuteNonQuery()

            'sqlString = "CREATE TABLE IUCarpkSubCarpkMap (RecordID integer IDENTITY(1,1) NOT NULL, " & _
            '            "IULabel nchar(10) NOT NULL, CarparkNo smallint NOT NULL, SubCarparkNo smallint NOT NULL)"
            'cmd.CommandText = sqlString
            'cmd.ExecuteNonQuery()

            sqlString = "CREATE TABLE RunningSeason (Id integer IDENTITY(1,1) NOT NULL, SeasonNo nvarchar(40) NOT NULL, " & _
                        "EntryTime datetime NULL, TerminalNo smallint NULL, SeasonOption smallint NULL, " & _
                        "SeasonType smallint NULL, Status smallint NULL)"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()

            sqlString = "CREATE TABLE AccessViolation (Id integer IDENTITY(1,1) NOT NULL, TicketNo nvarchar(40) NOT NULL, " & _
                                    "EntryTime datetime NULL, TerminalNo smallint NULL, RelationID smallint NULL)"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()


            sqlString = "CREATE TABLE PGS (TerminalNo smallint NOT NULL, " & _
                                    "Connected bit NULL, InformedBy smallint NULL)"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()

            sqlString = "CREATE TABLE PGSEnabled (Enabled bit NOT NULL)"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()

            sqlString = "CREATE TABLE CCTVEnabled (Enabled bit NOT NULL)"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()

            sqlString = "CREATE TABLE FullStatus (TerminalNo smallint NOT NULL, " & _
                        "FullSign bit NULL)"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()

            sqlString = "CREATE TABLE AddonOption (LEDEnabled bit NOT NULL, " & _
                        "ShutterEnabled bit NULL, CashcardConfirm bit NULL, PowerFailAlarm bit NULL, " & _
                        "SeasonAllowedWhenFull bit NULL, TailGateSensor bit NULL, SubCPWithinSubCP bit NULL)"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()

            sqlString = "CREATE TABLE BlacklistedTicket (TicketNo nvarchar(40) NOT NULL, " & _
                        "TicketType nvarchar(10) NULL, ListedDate datetime NULL, EffectiveDate datetime NULL, " & _
                        "ListedBy nvarchar(20) NULL, Remark nvarchar(80) NULL)"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()

            sqlString = "CREATE TABLE AntennaConfig (AntNo smallint NOT NULL, " & _
                        "AntennaID smallint NULL, AntennaName nvarchar(20) NULL, TerminalNo smallint NULL, ComPort smallint NULL, CHUID smallint NULL)"

            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()

            'create the table and the fields.
            sqlString = "CREATE TABLE WorkingMode (ID smallint NOT NULL, WorkingMode smallint NULL," & _
                        "ParkingType smallint NULL, Description nvarchar(80) NULL, SubCarparkNo smallint NULL," & _
                        "CarparkNo smallint NULL)"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()

            sqlString = "CREATE TABLE MainConfig (CarparkNo smallint NOT NULL, CarparkName nvarchar(50) NULL, " & _
                        "MinUnit money NULL, GST money NULL, GSTNo nvarchar(20) NULL, StartOperation datetime NULL, " & _
                        "StartIOCheck datetime NULL)"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()

            sqlString = "CREATE TABLE SubCarpark (SubCarparkNo smallint NOT NULL, " & _
                        "SubCarParkName nvarchar(50) NULL, TotalSpace integer NULL, SeasonSpace integer NULL, " & _
                        "ExtraSeasSpace smallint NULL, FullsignReserved smallint NULL, " & _
                        "CarparkNo smallint NULL)"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()

            sqlString = "CREATE TABLE Terminal (TerminalNo smallint NOT NULL, " & _
                        "TerminalName nvarchar(20) NULL, TerminalType smallint NULL, SourceSubCarpark smallint NULL, DestinationSubCarpark smallint NULL, " & _
                        "CarparkNo smallint NULL, IPAddress nvarchar(15) NULL, Description nvarchar(80) NULL, " & _
                        "RateSetNo smallint NULL, SeasonOnly bit NULL)"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()

            sqlString = "CREATE TABLE TerminalRelation (ID smallint NOT NULL, " & _
                        "FromTerminal smallint NULL, ToTerminal smallint NULL, Duration integer NULL, ChargeBy smallint NULL)"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()


            '**************************************************************
            'Malbinda 2014-10-29
            'This Table will use only in the exit terminal
            sqlString = "CREATE TABLE [dbo].[RunningSeasonExit]([IUNo] [nvarchar](40) NOT NULL,[ExitTime] [datetime] NULL) ON [PRIMARY]"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()

            'Malbinda 2015-02-15
            'This Table will use only in the Entry terminal
            sqlString = "CREATE TABLE [dbo].[RunningSeasonEntry]([IUNo] [nvarchar](40) NOT NULL,[ExitTime] [datetime] NULL) ON [PRIMARY]"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()
            '**************************************************************


        Catch ex As Exception
            hasError = True
            sqlcn.Close()

            dblog.Log("Error in Creating CreateTables")
            dblog.Log(ex.ToString())


        Finally
            If hasError = False Then
                sqlcn.Close()
            End If
        End Try
    End Sub

    Public Sub CreateRunningHourlyDatabase()

        Dim sqlString As String
        Dim sqlcn As SqlConnection
        Dim cmd As SqlCommand
        Dim hasError As Boolean = False


        Try
            sqlcn = New SqlConnection(connectionstr)
            sqlcn.Open()
            cmd = sqlcn.CreateCommand
            sqlString = "CREATE TABLE RunningHourly (Id integer IDENTITY(1,1) NOT NULL, HourlyNo nvarchar(40) NOT NULL, " & _
                        "EntryTime datetime NULL, TerminalNo smallint NULL, HourlyType smallint NULL, Status smallint NULL)"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()
        Catch ex As Exception
            hasError = True
            sqlcn.Close()
        Finally
            If hasError = False Then
                sqlcn.Close()
            End If
        End Try
    End Sub

    Public Sub CreateRunningSeasonDatabase()

        Dim sqlString As String
        Dim sqlcn As SqlConnection
        Dim cmd As SqlCommand
        Dim hasError As Boolean = False

        Try
            sqlcn = New SqlConnection(connectionstr)
            sqlcn.Open()
            cmd = sqlcn.CreateCommand

            sqlString = "CREATE TABLE RunningSeason (Id integer IDENTITY(1,1) NOT NULL, SeasonNo nvarchar(40) NOT NULL, " & _
                        "EntryTime datetime NULL, TerminalNo smallint NULL, SeasonOption smallint NULL, " & _
                        "SeasonType smallint NULL, Status smallint NULL)"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()

        Catch ex As Exception
            hasError = True
            sqlcn.Close()
        Finally
            If hasError = False Then
                sqlcn.Close()
            End If
        End Try

    End Sub

    Public Sub CreateAccessViolationDatabase()

        Dim sqlString As String
        Dim sqlcn As SqlConnection
        Dim cmd As SqlCommand
        Dim hasError As Boolean = False


        Try
            sqlcn = New SqlConnection(connectionstr)
            sqlcn.Open()
            cmd = sqlcn.CreateCommand


            sqlString = "CREATE TABLE AccessViolation (Id integer IDENTITY(1,1) NOT NULL, TicketNo nvarchar(40) NOT NULL, " & _
                        "EntryTime datetime NULL, TerminalNo smallint NULL, RelationID smallint NULL)"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()


        Catch ex As Exception
            hasError = True
            sqlcn.Close()
        Finally
            If hasError = False Then
                sqlcn.Close()
            End If
        End Try
    End Sub

    Public Sub CreateAccessViolationDebitDatabase()

        Dim sqlString As String
        Dim sqlcn As SqlConnection
        Dim cmd As SqlCommand
        Dim hasError As Boolean = False

        Try
            sqlcn = New SqlConnection(connectionstr)
            sqlcn.Open()
            cmd = sqlcn.CreateCommand

            sqlString = "CREATE TABLE AccessViolationDebit (Id integer IDENTITY(1,1) NOT NULL, TicketNo nvarchar(40) NOT NULL, " & _
                        "EntryTime datetime NULL, ExitTime datetime NULL, EntryTerminalNo smallint NULL, ExitTerminalNo smallint NULL)"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()
        Catch ex As Exception
            hasError = True
            sqlcn.Close()
        Finally
            If hasError = False Then
                sqlcn.Close()
            End If
        End Try
    End Sub

    Public Sub CreateIntegrationDatabase()

        Dim sqlString As String
        Dim sqlcn As SqlConnection
        Dim cmd As SqlCommand
        Dim hasError As Boolean = False


        Try
            sqlcn = New SqlConnection(connectionstr)
            sqlcn.Open()
            cmd = sqlcn.CreateCommand
            sqlString = "CREATE TABLE PGS (TerminalNo smallint NOT NULL, " & _
                        "Connected bit NULL, InformedBy smallint NULL)"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()

            sqlString = "CREATE TABLE PGSEnabled (Enabled bit NOT NULL)"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()

            sqlString = "CREATE TABLE CCTVEnabled (Enabled bit NOT NULL)"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()

        Catch ex As Exception
            hasError = True
            sqlcn.Close()
        Finally
            If hasError = False Then
                sqlcn.Close()
            End If
        End Try
    End Sub

    Public Sub CreateOptionsDatabase()

        Dim sqlString As String
        Dim sqlcn As SqlConnection
        Dim cmd As SqlCommand
        Dim hasError As Boolean = False

        Try
            sqlcn = New SqlConnection(connectionstr)
            sqlcn.Open()
            cmd = sqlcn.CreateCommand
            sqlString = "CREATE TABLE FullStatus (TerminalNo smallint NOT NULL, " & _
                        "FullSign bit NULL)"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()

            sqlString = "CREATE TABLE AddonOption (LEDEnabled bit NOT NULL, " & _
                        "ShutterEnabled bit NULL, CashcardConfirm bit NULL, PowerFailAlarm bit NULL, " & _
                        "SeasonAllowedWhenFull bit NULL, TailGateSensor bit NULL, SubCPWithinSubCP bit NULL)"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()

            sqlString = "CREATE TABLE BlacklistedTicket (TicketNo nvarchar(40) NOT NULL, " & _
                        "TicketType nvarchar(10) NULL, ListedDate datetime NULL, EffectiveDate datetime NULL, " & _
                        "ListedBy nvarchar(20) NULL, Remark nvarchar(80) NULL)"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()
        Catch ex As Exception
            hasError = True
            sqlcn.Close()
        Finally
            If hasError = False Then
                sqlcn.Close()
            End If
        End Try
    End Sub

    Public Sub CreateRateDatabase()

        Dim sqlString As String
        Dim sqlcn As SqlConnection
        Dim cmd As SqlCommand
        Dim hasError As Boolean = False


        Try
            sqlcn = New SqlConnection(connectionstr)
            sqlcn.Open()
            cmd = sqlcn.CreateCommand
            'create the table and the fields.

            sqlString = "CREATE TABLE RateComposite (CompositeID integer NOT NULL, AutoSwitch bit NULL, " & _
                        "Name nvarchar(40) NULL)"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()

            sqlString = "CREATE TABLE CyclingBlockList (Block_ID integer NOT NULL, " & _
                        "Block_Type integer NULL, FromTime nvarchar(10) NULL, EndTime nvarchar(10) NULL, PerMinute bit NULL," & _
                        "FlatValue money NULL, RegularValue money NULL, RegularUnitAmount money NULL, " & _
                        "RegularUnitType integer NULL, RateID integer NULL)"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()

            sqlString = "CREATE TABLE DiscreteBlockList (Block_ID integer NOT NULL, " & _
                        "Block_Type integer NULL, FromTime nvarchar(10) NULL, EndTime nvarchar(10) NULL, PerMinute bit NULL," & _
                        "FlatValue money NULL, RegularValue money NULL, RegularUnitAmount money NULL, " & _
                        "RegularUnitType integer NULL, RateID integer NULL)"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()

            sqlString = "CREATE TABLE AdvancedBlockList (ID integer NOT NULL, " & _
                        "Duration integer NULL, RateValue integer NULL, UnitAmount integer NULL, UnitType nvarchar(20) NULL, Block_ID integer NULL)"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()


            sqlString = "CREATE TABLE CyclingCapList (CapID integer NOT NULL, FromTime nvarchar(10) NULL, EndTime nvarchar(10) NULL, " & _
                        "Amount money NULL, RateID integer NULL)"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()

            sqlString = "CREATE TABLE CrossOverList (CrossOverID integer NOT NULL, CrossOverType integer NULL, " & _
                        "Period nvarchar(20) NULL, FirstBlock integer NULL, SecondBlock integer NULL, RateID integer NULL)"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()

            sqlString = "CREATE TABLE CyclingRateList (RateID integer NOT NULL, RateType integer NULL, FreePass integer NULL, " & _
                        "PerMinute bit NULL, CountOfBlocks integer NULL, CountOfCaps integer NULL, CountOfCrossOver integer NULL, " & _
                        "CompositeID integer NULL, DayType nvarchar(40) NULL, CapType bit NULL)"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()

            sqlString = "CREATE TABLE RateSet (RateSetNo smallint NOT NULL, Car integer NULL, Lorry integer NULL, Motorcycle integer null, " & _
                        "Taxi integer null, Container integer null, Reserve1 integer null, Reserve2 integer null)"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()

            sqlString = "CREATE TABLE Holiday (HolidayID integer NOT NULL, HDate nvarchar(30) NULL)"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()

        Catch ex As Exception
            hasError = True
            sqlcn.Close()
        Finally
            If hasError = False Then
                sqlcn.Close()
            End If
        End Try
    End Sub

    Public Sub CreateSystemInfoDatabase()

        Dim sqlString As String
        Dim sqlcn As SqlConnection
        Dim cmd As SqlCommand
        Dim hasError As Boolean = False

        Try
            sqlcn = New SqlConnection(connectionstr)
            sqlcn.Open()
            cmd = sqlcn.CreateCommand
            'create the table and the fields.
            sqlString = "CREATE TABLE CHUConfig (CHUID smallint NOT NULL," & _
                        "Description nvarchar(80) NULL, CPTID nvarchar(50) NULL, SPID nvarchar(50) NULL, " & _
                        "IPAddress nvarchar(15) NULL, ControlLine integer NULL, ParameterLine integer NULL," & _
                        "LogLine integer NULL, DataLine integer NULL)"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()

            sqlString = "CREATE TABLE AntennaConfig (AntNo smallint NOT NULL, " & _
                        "AntennaID smallint NULL, AntennaName nvarchar(20) NULL, TerminalNo smallint NULL, ComPort smallint NULL, CHUID smallint NULL)"

            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()

            'create the table and the fields.
            sqlString = "CREATE TABLE WorkingMode (ID smallint NOT NULL, WorkingMode smallint NULL," & _
                        "ParkingType smallint NULL, Description nvarchar(80) NULL, SubCarparkNo smallint NULL," & _
                        "CarparkNo smallint NULL)"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()

            sqlString = "CREATE TABLE ReceiptMessage (ReceiptHeader1 nvarchar(18) NULL, " & _
                        "ReceiptHeader2 nvarchar(18) NULL, ReceiptFooter1 nvarchar(18) NULL, " & _
                        "ReceiptFooter2 nvarchar(18) NULL, IncludeGST bit NULL)"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()
        Catch ex As Exception
            hasError = True
            sqlcn.Close()
        Finally
            If hasError = False Then
                sqlcn.Close()
            End If
        End Try
    End Sub


    Public Sub CreateCarparkDatabase()

        Dim sqlString As String
        Dim sqlcn As SqlConnection
        Dim cmd As SqlCommand
        Dim hasError As Boolean = False

        Try
            sqlcn = New SqlConnection(connectionstr)
            sqlcn.Open()
            cmd = sqlcn.CreateCommand
            'create the table and the fields.
            sqlString = "CREATE TABLE MainConfig (CarparkNo smallint NOT NULL, CarparkName nvarchar(50) NULL, " & _
                        "MinUnit money NULL, GST money NULL, GSTNo nvarchar(20) NULL, StartOperation datetime NULL, " & _
                        "StartIOCheck datetime NULL)"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()

            sqlString = "CREATE TABLE SubCarpark (SubCarparkNo smallint NOT NULL, " & _
                        "SubCarParkName nvarchar(50) NULL, TotalSpace integer NULL, SeasonSpace integer NULL, " & _
                        "ExtraSeasSpace smallint NULL, FullsignReserved smallint NULL, " & _
                        "CarparkNo smallint NULL)"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()

            sqlString = "CREATE TABLE Terminal (TerminalNo smallint NOT NULL, " & _
                        "TerminalName nvarchar(20) NULL, TerminalType smallint NULL, SourceSubCarpark smallint NULL, DestinationSubCarpark smallint NULL, " & _
                        "CarparkNo smallint NULL, IPAddress nvarchar(15) NULL, Description nvarchar(80) NULL, " & _
                        "RateSetNo smallint NULL, SeasonOnly bit NULL)"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()

            sqlString = "CREATE TABLE TerminalRelation (ID smallint NOT NULL, " & _
                        "FromTerminal smallint NULL, ToTerminal smallint NULL, Duration integer NULL, ChargeBy smallint NULL)"
            cmd.CommandText = sqlString
            cmd.ExecuteNonQuery()

        Catch ex As Exception
            hasError = True
            sqlcn.Close()
        Finally
            If hasError = False Then
                sqlcn.Close()
            End If
        End Try
    End Sub

    Public Function SelfTest(ByVal eps As Boolean, ByVal cscard As Boolean, ByVal rmm As Boolean) As SelfTestResult
        If RetrieveAllWorkingModes().Rows.Count = 0 Then
            DeleteAllRecords()
            Return SelfTestResult.FATALDATABASEERROR
        End If
        If RetrieveAllSubCarparks.Rows.Count = 0 Then
            DeleteAllRecords()
            Return SelfTestResult.FATALDATABASEERROR
        End If
        If RetrieveAllTerminals.Rows.Count = 0 Then
            DeleteAllRecords()
            Return SelfTestResult.FATALDATABASEERROR
        End If
        'If RetrieveAllCyclingRateList.Rows.Count = 0 Then
        '    DeleteAllRecords()
        '    Return SelfTestResult.FATALDATABASEERROR
        'End If

        'If RetrieveAllRateSet.Rows.Count = 0 Then
        '    DeleteAllRecords()
        '    Return SelfTestResult.FATALDATABASEERROR
        'End If

        If HasFullEps() = True Then
            If RetrieveAllAntennaConfig.Rows.Count = 0 Then
                DeleteAllRecords()
                Return SelfTestResult.FATALDATABASEERROR
            End If

            'If HasFullEps() = True And eps = True Then
            '    If RetrieveAllCHUConfig.Rows.Count = 0 Then
            '        DeleteAllRecords()
            '        Return SelfTestResult.FATALDATABASEERROR
            '    End If
            'End If
        End If

        StartOpsThread()
        Return SelfTestResult.FINE
    End Function

    Public Sub DoRunningAccessViolation(ByVal runn As Object, ByVal act As DBAction)
        Dim a As New DBActionContext
        a.obj = runn
        a.action = act
        SyncLock mDBRunningAccessViolation.SyncRoot
            mDBRunningAccessViolation.Enqueue(a)
        End SyncLock
    End Sub

    Public Sub DoRunningHourRecord(ByVal runn As Object, ByVal act As DBAction)
        Dim a As New DBActionContext
        a.obj = runn
        a.action = act
        SyncLock mDBRunningHourQUE.SyncRoot
            mDBRunningHourQUE.Enqueue(a)
        End SyncLock
    End Sub

    Public Sub DoRunningSeasonRecord(ByVal runn As Object, ByVal act As DBAction)
        Dim a As New DBActionContext
        a.obj = runn
        a.action = act
        SyncLock mDBRunningSeasQUE.SyncRoot
            mDBRunningSeasQUE.Enqueue(a)
        End SyncLock
    End Sub

    Public Sub DoSeasonDBOps(ByVal obj As Object, ByVal act As DBAction, ByVal datatype As DBSeasonDataType)
        DoSeasonDBOps(obj, act, datatype, "")
    End Sub

    Public Sub DoSeasonDBOps(ByVal obj As Object, ByVal act As DBAction, ByVal datatype As DBSeasonDataType, ByVal licenceNo As String)
        Dim a As New DBSeasonActionContext
        a.obj = obj
        a.action = act
        a.seasondata = datatype
        a.licenceNo = licenceNo
        SyncLock mDBSeasonQUE
            mDBSeasonQUE.Enqueue(a)
        End SyncLock
    End Sub

    Private Sub ProcessRunningAccessViolation()
        While True
            Dim objt As DBActionContext = Nothing
            SyncLock mDBRunningAccessViolation.SyncRoot
                If mDBRunningAccessViolation.Count > 0 Then
                    objt = mDBRunningAccessViolation.Dequeue()
                End If
            End SyncLock
            If IsNothing(objt) = False Then
                Try
                    Dim rh As AccessViolation = Nothing
                    If objt.action = DBAction.DELETE Then
                        rh = New AccessViolation
                        rh.TicketNo = objt.obj
                    ElseIf objt.action = DBAction.UPDATE Then
                        rh = objt.obj
                    End If

                    If IsNothing(rh) = False Then
                        Dim par As New SqlParameter("TicketNo", SqlDbType.NVarChar)
                        par.Value = rh.TicketNo
                        Dim cnt As Integer = 0
                        cnt = executeCmdEx(connectionstr, "select count(*) from AccessViolation where TicketNo = @TicketNo", New SqlParameter() {par})
                        If cnt = 0 Then
                            If objt.action = DBAction.UPDATE Then
                                'Add
                                If AddAccessViolationRecords(New AccessViolation() {rh}) <> 1 Then
                                    dblog.Log("Error when add a AccessViolation record. Ticket No = " & rh.TicketNo)
                                End If
                            Else
                                dblog.Log("Try to delete AccessViolation record not exists. Ticket No = " & rh.TicketNo)
                            End If
                        ElseIf cnt = 1 Then
                            If objt.action = DBAction.DELETE Then
                                'Delete
                                'Dim par1 As New SqlParameter("HourlyNo", rh.HourlyNo)
                                Dim delnum As Integer = executeCmd(connectionstr, "delete from AccessViolation where TicketNo = @TicketNo", New SqlParameter() {par})
                                If delnum <> 1 Then
                                    dblog.Log("Error when delete a AccessViolation record. Ticket No = " & rh.TicketNo)
                                End If
                            Else
                                'Update
                                If UpdateAccessViolationRecords(New AccessViolation() {rh}) <> 1 Then
                                    dblog.Log("Error when update a AccessViolation record. Ticket No = " & rh.TicketNo)
                                End If
                            End If
                        Else
                            dblog.Log("There are " & cnt & "AccessViolation records in DB. Hourly No = " & rh.TicketNo)
                        End If
                    Else
                        Dim thetm As DateTime = objt.obj
                        Dim para As New SqlParameter("EntryTime", thetm)
                        executeCmd(connectionstr, "delete from AccessViolation where EntryTime <= @EntryTime", New SqlParameter() {para})
                    End If

                Catch ex As Exception
                    Console.WriteLine("ProcessRunningAccessViolation: " & ex.Message)
                End Try
            End If
            Threading.Thread.Sleep(50)
        End While
    End Sub

    Private Sub ProcessRunHourProc()
        While True
            Dim objt As DBActionContext = Nothing
            SyncLock mDBRunningHourQUE.SyncRoot
                If mDBRunningHourQUE.Count > 0 Then
                    objt = mDBRunningHourQUE.Dequeue()
                End If
            End SyncLock
            If IsNothing(objt) = False Then
                Try
                    Dim rh As RunningHourly = Nothing
                    If objt.action = DBAction.DELETE Then
                        rh = New RunningHourly
                        rh.HourlyNo = objt.obj
                    ElseIf objt.action = DBAction.UPDATE Then
                        rh = objt.obj
                    End If

                    If IsNothing(rh) = False Then
                        Dim par As New SqlParameter("HourlyNo", SqlDbType.NVarChar)
                        par.Value = rh.HourlyNo
                        Dim cnt As Integer = 0
                        cnt = executeCmdEx(connectionstr, "select count(*) from RunningHourly where HourlyNo = @HourlyNo", New SqlParameter() {par})
                        If cnt = 0 Then
                            If objt.action = DBAction.UPDATE Then
                                'Add
                                If AddRunningHourlyRecords(rh) <> 1 Then
                                    dblog.Log("Error when add a hourly record. Hourly No = " & rh.HourlyNo)
                                End If
                            Else
                                dblog.Log("Try to delete running hourly record not exists. Hourly No = " & rh.HourlyNo)
                            End If
                        ElseIf cnt = 1 Then
                            If objt.action = DBAction.DELETE Then
                                'Delete
                                'Dim par1 As New SqlParameter("HourlyNo", rh.HourlyNo)
                                Dim delnum As Integer = executeCmd(connectionstr, "delete from RunningHourly where HourlyNo = @HourlyNo", New SqlParameter() {par})
                                If delnum <> 1 Then
                                    dblog.Log("Error when delete a hourly record. Hourly No = " & rh.HourlyNo)
                                End If
                            Else
                                'Update
                                If UpdateRunningHourlyRecords(rh) <> 1 Then
                                    dblog.Log("Error when update a hourly record. Hourly No = " & rh.HourlyNo)
                                End If
                            End If
                        Else
                            dblog.Log("There are " & cnt & "running hourly records in DB. Hourly No = " & rh.HourlyNo)
                        End If
                    Else
                        Dim thetm As DateTime = objt.obj
                        Dim para As New SqlParameter("EntryTime", thetm)
                        executeCmd(connectionstr, "delete from RunningHourly where EntryTime <= @EntryTime", New SqlParameter() {para})
                    End If

                Catch ex As Exception
                    Console.WriteLine(" Error in ProcessRunHourProc: ")
                    Console.WriteLine(ex.ToString())

                    dblog.Log(" Error in ProcessRunHourProc: ")
                    dblog.Log(ex.ToString())
                End Try
            End If
            Threading.Thread.Sleep(50)
        End While
    End Sub

    Private Sub ProcessRunSeasProc()
        Try

            While True
                Dim objt As DBActionContext = Nothing
                SyncLock mDBRunningSeasQUE.SyncRoot
                    If mDBRunningSeasQUE.Count > 0 Then
                        objt = mDBRunningSeasQUE.Dequeue()
                    End If
                End SyncLock

                If IsNothing(objt) = False Then
                    Try
                        Dim rs As RunningSeason = Nothing
                        If objt.action = DBAction.DELETE Then
                            rs = New RunningSeason
                            rs.SeasonNo = objt.obj
                        ElseIf objt.action = DBAction.UPDATE Then
                            rs = objt.obj
                        End If

                        ' ''test
                        ''rs = New RunningSeason
                        ''rs.EntryTime = Now
                        ''rs.SeasonNo = "1234567890"
                        ''rs.SeasonOption = SEASONTOPTIONS.IU
                        ''rs.SeasonType = 0
                        ''rs.TerminalNo = 23
                        ' ''

                        If IsNothing(rs) = False Then
                            Dim par As New SqlParameter("SeasonNo", rs.SeasonNo)
                            Dim cnt As Integer = executeCmdEx(connectionstr, "select count(*) from RunningSeason where SeasonNo = @SeasonNo", New SqlParameter() {par})
                            If cnt = 0 Then
                                If objt.action = DBAction.UPDATE Then
                                    'Add
                                    If AddRunningSeasonRecords(rs) <> 1 Then
                                        dblog.Log("Error when add a running season record. Season No = " & rs.SeasonNo)
                                    End If
                                Else
                                    dblog.Log("Try to delete running season record not exists. Season No = " & rs.SeasonNo)
                                End If
                            ElseIf cnt = 1 Then
                                If objt.action = DBAction.DELETE Then
                                    'Delete
                                    Dim delnum As Integer = executeCmd(connectionstr, "delete from RunningSeason where SeasonNo = @SeasonNo", New SqlParameter() {par})
                                    If delnum <> 1 Then
                                        dblog.Log("Error when delete a running season record. Season No = " & rs.SeasonNo)
                                    End If
                                Else
                                    'Update
                                    If UpdateRunningSeasonRecords(rs) <> 1 Then
                                        dblog.Log("Error when update a season record. Season No = " & rs.SeasonNo)
                                    End If
                                End If
                            Else
                                dblog.Log("There are " & cnt & "running season records in DB. Season No = " & rs.SeasonNo)
                            End If
                        Else
                            Dim thetm As DateTime = objt.obj
                            Dim para As New SqlParameter("EntryTime", thetm)
                            executeCmd(connectionstr, "delete from RunningSeason where EntryTime <= @EntryTime", New SqlParameter() {para})

                        End If
                    Catch ex As Exception
                        Console.WriteLine("ProcessRunSeasProc: " & ex.Message)
                    End Try
                End If
                Threading.Thread.Sleep(50)
            End While

        Catch ex As Exception
            Console.WriteLine("Error in ProcessRunSeasProc")
            Console.WriteLine(ex.ToString())

            dblog.Log("Error in ProcessRunSeasProc")
            dblog.Log(ex.ToString())

        End Try
    End Sub

    Private Sub ProcessSeasonProc()

        Try
            While True
                Dim objt As DBSeasonActionContext = Nothing
                SyncLock mDBSeasonQUE.SyncRoot
                    If mDBSeasonQUE.Count > 0 Then
                        objt = mDBSeasonQUE.Dequeue()
                    End If
                End SyncLock

                If IsNothing(objt) = False Then
                    Try
                        Select Case objt.seasondata
                            Case DBSeasonDataType.CashCardSeason
                                Dim oo As ClsSeasonInfo
                                If objt.action = DBAction.DELETE Then
                                    oo = New ClsSeasonInfo
                                    oo.Ticket = objt.obj
                                Else
                                    oo = objt.obj
                                End If
                                If oo.Ticket = String.Empty Then
                                    DeleteAllCashCardSeasonInfo()
                                Else
                                    Dim par As New SqlParameter("CashCardNo", oo.Ticket)
                                    Dim cnt As Integer = executeCmdEx(connectionstr, "select count(*) from CashCardSeasonInfo where CashCardNo = @CashCardNo", New SqlParameter() {par})
                                    If cnt = 0 Then
                                        If objt.action = DBAction.UPDATE Then
                                            'Add
                                            If AddCashCardSeasonInfoRecords(oo) <> 1 Then
                                                dblog.Log("Error when add a CashCard season record. CashCard No = " & oo.Ticket)
                                            End If
                                        Else
                                            dblog.Log("Try to delete CashCard season record not exists. CashCard No = " & oo.Ticket)
                                        End If
                                    ElseIf cnt = 1 Then
                                        If objt.action = DBAction.DELETE Then
                                            'Delete
                                            Dim delnum As Integer = executeCmd(connectionstr, "delete from CashCardSeasonInfo where CashCardNo = @CashCardNo", New SqlParameter() {par})
                                            If delnum <> 1 Then
                                                dblog.Log("Error when delete a CashCard season record. CashCard No = " & oo.Ticket)
                                            End If
                                        Else
                                            'Update
                                            If UpdateCashCardSeasonInfoRecords(oo) <> 1 Then
                                                dblog.Log("Error when update a CashCard record. CashCard No = " & oo.Ticket)
                                            End If
                                        End If
                                    Else
                                        dblog.Log("There are " & cnt & "running CashCard records in DB. CashCard No = " & oo.Ticket)
                                    End If
                                End If
                            Case DBSeasonDataType.IUSeason
                                Dim oo As ClsSeasonInfo
                                If objt.action = DBAction.DELETE Then
                                    oo = New ClsSeasonInfo
                                    oo.Ticket = objt.obj
                                Else
                                    oo = objt.obj
                                End If
                                If oo.Ticket = String.Empty Then
                                    DeleteAllIUSeasonInfo()
                                Else
                                    Dim par As New SqlParameter("IULabel", oo.Ticket)
                                    Dim cnt As Integer = executeCmdEx(connectionstr, "select count(*) from IUSeasonInfo where IULabel = @IULabel", New SqlParameter() {par})
                                    If cnt = 0 Then
                                        If objt.action = DBAction.UPDATE Then
                                            'Add
                                            If AddIUSeasonInfoRecords(oo, objt.licenceNo) <> 1 Then
                                                dblog.Log("Error when add a IU season record. IULabel = " & oo.Ticket)
                                            End If
                                        Else
                                            dblog.Log("Try to delete IU season record not exists. IULabel = " & oo.Ticket)
                                        End If
                                    ElseIf cnt = 1 Then
                                        If objt.action = DBAction.DELETE Then
                                            'Delete
                                            Dim delnum As Integer = executeCmd(connectionstr, "delete from IUSeasonInfo where IULabel = @IULabel", New SqlParameter() {par})
                                            If delnum <> 1 Then
                                                dblog.Log("Error when delete a IU season record. IULabel = " & oo.Ticket)
                                            End If
                                        Else
                                            'Update
                                            If UpdateIUSeasonInfoRecords(oo, objt.licenceNo) <> 1 Then
                                                dblog.Log("Error when update a IU record. IULabel = " & oo.Ticket)
                                            End If
                                        End If
                                    Else
                                        dblog.Log("There are " & cnt & "running IU records in DB. IULabel = " & oo.Ticket)
                                    End If
                                End If
                            Case DBSeasonDataType.IUSeasonPermit
                                Dim oo As ClsSeasonPermits
                                If objt.action = DBAction.DELETE Then
                                    oo = New ClsSeasonPermits
                                    oo.Id = objt.obj
                                Else
                                    oo = objt.obj
                                End If
                                If oo.Id = -1 Then
                                    DeleteAllIUSeasonPermits()
                                Else
                                    Dim par As New SqlParameter("ID", oo.Id)
                                    Dim cnt As Integer = executeCmdEx(connectionstr, "select count(*) from IUSeasonPermits where Id = @ID", New SqlParameter() {par})
                                    If cnt = 0 Then
                                        If objt.action = DBAction.UPDATE Then
                                            'Add
                                            If AddIUSeasonPermitsRecords(oo) <> 1 Then
                                                dblog.Log("Error when add a IU season permit record. Id = " & oo.Id)
                                            End If
                                        Else
                                            dblog.Log("Try to delete IU season permit record not exists. Id = " & oo.Id)
                                        End If
                                    ElseIf cnt = 1 Then
                                        If objt.action = DBAction.DELETE Then
                                            'Delete
                                            Dim delnum As Integer = executeCmd(connectionstr, "delete from IUSeasonPermits where Id = @Id", New SqlParameter() {par})
                                            If delnum <> 1 Then
                                                dblog.Log("Error when delete a IU season permit record. Id = " & oo.Id)
                                            End If
                                        Else
                                            'Update
                                            If UpdateIUSeasonPermitsRecords(oo) <> 1 Then
                                                dblog.Log("Error when update a IU season permit record. Id = " & oo.Id)
                                            End If
                                        End If
                                    Else
                                        dblog.Log("There are " & cnt & "IU season permit records in DB. Id = " & oo.Id)
                                    End If
                                End If
                            Case DBSeasonDataType.SeasonGroup
                                Dim oo As ClsSeasonGroup
                                If objt.action = DBAction.DELETE Then
                                    oo = New ClsSeasonGroup
                                    oo.GroupNo = objt.obj
                                Else
                                    oo = objt.obj
                                End If

                                If oo.GroupNo = -1 Then
                                    DeleteAllSeasonGroup()
                                Else
                                    Dim par As New SqlParameter("GroupNo", oo.GroupNo)
                                    Dim cnt As Integer = executeCmdEx(connectionstr, "select count(*) from SeasonGroup where GroupNo = @GroupNo", New SqlParameter() {par})
                                    If cnt = 0 Then
                                        If objt.action = DBAction.UPDATE Then
                                            'Add
                                            If AddSeasonGroupRecords(oo) <> 1 Then
                                                dblog.Log("Error when add a season group record. GroupNo = " & oo.GroupNo)
                                            End If
                                        Else
                                            dblog.Log("Try to delete season group record not exists. GroupNo = " & oo.GroupNo)
                                        End If
                                    ElseIf cnt = 1 Then
                                        If objt.action = DBAction.DELETE Then
                                            'Delete
                                            Dim delnum As Integer = executeCmd(connectionstr, "delete from SeasonGroup where GroupNo = @GroupNo", New SqlParameter() {par})
                                            If delnum <> 1 Then
                                                dblog.Log("Error when delete a season group record. GroupNo = " & oo.GroupNo)
                                            End If
                                        Else
                                            'Update
                                            If UpdateSeasonGroupRecords(oo) <> 1 Then
                                                dblog.Log("Error when update a season group record. GroupNo = " & oo.GroupNo)
                                            End If
                                        End If
                                    Else
                                        dblog.Log("There are " & cnt & "season group records in DB. GroupNo = " & oo.GroupNo)
                                    End If
                                End If
                            Case DBSeasonDataType.SeasonGroupPermit
                                Dim oo As GroupSeasonPermits
                                If objt.action = DBAction.DELETE Then
                                    oo = New GroupSeasonPermits
                                    oo.Id = objt.obj
                                Else
                                    oo = objt.obj
                                End If
                                If oo.Id = -1 Then
                                    DeleteAllGroupSeasonPermits()
                                Else
                                    Dim par As New SqlParameter("Id", oo.Id)
                                    Dim cnt As Integer = executeCmdEx(connectionstr, "select count(*) from GroupSeasonPermits where Id = @Id", New SqlParameter() {par})
                                    If cnt = 0 Then
                                        If objt.action = DBAction.UPDATE Then
                                            'Add
                                            If AddGroupSeasonPermitsRecords(oo) <> 1 Then
                                                dblog.Log("Error when add a Group season permit record. Id = " & oo.Id)
                                            End If
                                        Else
                                            dblog.Log("Try to delete Group season permit record not exists. Id = " & oo.Id)
                                        End If
                                    ElseIf cnt = 1 Then
                                        If objt.action = DBAction.DELETE Then
                                            'Delete
                                            Dim delnum As Integer = executeCmd(connectionstr, "delete from GroupSeasonPermits where Id = @Id", New SqlParameter() {par})
                                            If delnum <> 1 Then
                                                dblog.Log("Error when delete a Group season permit record. Id = " & oo.Id)
                                            End If
                                        Else
                                            'Update
                                            If UpdateGroupSeasonPermitsRecords(oo) <> 1 Then
                                                dblog.Log("Error when update a Group season permit record. Id = " & oo.Id)
                                            End If
                                        End If
                                    Else
                                        dblog.Log("There are " & cnt & "Group season permit records in DB. Id = " & oo.Id)
                                    End If
                                End If
                            Case DBSeasonDataType.CCSeasonPermit
                                Dim oo As ClsSeasonPermits
                                If objt.action = DBAction.DELETE Then
                                    oo = New ClsSeasonPermits
                                    oo.Id = objt.obj
                                Else
                                    oo = objt.obj
                                End If
                                If oo.Id = -1 Then
                                    DeleteAllCashCardSeasonPermits()
                                Else
                                    Dim par As New SqlParameter("Id", oo.Id)
                                    Dim cnt As Integer = executeCmdEx(connectionstr, "select count(*) from CashCardSeasonPermits where Id = @Id", New SqlParameter() {par})
                                    If cnt = 0 Then
                                        If objt.action = DBAction.UPDATE Then
                                            'Add
                                            If AddCashCardSeasonPermitsRecords(oo) <> 1 Then
                                                dblog.Log("Error when add a Cashcard season permit record. Id = " & oo.Id)
                                            End If
                                        Else
                                            dblog.Log("Try to delete Cashcard season permit record not exists. Id = " & oo.Id)
                                        End If
                                    ElseIf cnt = 1 Then
                                        If objt.action = DBAction.DELETE Then
                                            'Delete
                                            Dim delnum As Integer = executeCmd(connectionstr, "delete from CashCardSeasonPermits where Id = @Id", New SqlParameter() {par})
                                            If delnum <> 1 Then
                                                dblog.Log("Error when delete a Cashcard season permit record. Id = " & oo.Id)
                                            End If
                                        Else
                                            'Update
                                            If UpdateCashCardSeasonPermitsRecords(oo) <> 1 Then
                                                dblog.Log("Error when update a Cashcard season permit record. Id = " & oo.Id)
                                            End If
                                        End If
                                    Else
                                        dblog.Log("There are " & cnt & "Cashcard season permit records in DB. Id = " & oo.Id)
                                    End If
                                End If
                            Case DBSeasonDataType.Redemption
                                Dim oo As Redemption
                                If objt.action = DBAction.DELETE Then
                                    oo = New Redemption
                                    oo.RedemptionNo = objt.obj
                                ElseIf objt.action = DBAction.DELETEBYTIME Then
                                    oo = New Redemption
                                    oo.RedemptionNo = -1
                                Else
                                    oo = objt.obj
                                End If
                                If oo.RedemptionNo = String.Empty Then
                                    DeleteAllRedemption()
                                ElseIf oo.RedemptionNo = -1 Then
                                    Dim thetm As DateTime = Now
                                    Dim para As New SqlParameter("ValidTo", thetm)
                                    Dim delnum As Integer = executeCmd(connectionstr, "delete from Redemption where  ValidTo < @ValidTo", New SqlParameter() {para})
                                    If delnum <> 1 Then
                                        dblog.Log("Error when delete a Redemption record. Id = " & oo.RedemptionNo)
                                    End If
                                Else
                                    Dim par As New SqlParameter("RedemptionNo", oo.RedemptionNo)
                                    Dim cnt As Integer = executeCmdEx(connectionstr, "select count(*) from Redemption where RedemptionNo = @RedemptionNo", New SqlParameter() {par})
                                    If cnt = 0 Then
                                        If objt.action = DBAction.UPDATE Then
                                            'Add
                                            If AddRedemptionRecords(oo) <> 1 Then
                                                dblog.Log("Error when add a Redemption record. RedemptionNo = " & oo.RedemptionNo)
                                            End If
                                        Else
                                            dblog.Log("Try to delete Redemption record not exists. RedemptionNo = " & oo.RedemptionNo)
                                        End If
                                    ElseIf cnt = 1 Then
                                        If objt.action = DBAction.DELETE Then
                                            'Delete
                                            Dim delnum As Integer = executeCmd(connectionstr, "delete from Redemption where RedemptionNo = @RedemptionNo", New SqlParameter() {par})
                                            If delnum <> 1 Then
                                                dblog.Log("Error when delete a Redemption record. Id = " & oo.RedemptionNo)
                                            End If
                                        Else
                                            'Update
                                            If UpdateRedemptionRecords(oo) <> 1 Then
                                                dblog.Log("Error when update a Redemption record. Id = " & oo.RedemptionNo)
                                            End If
                                        End If
                                    Else
                                        dblog.Log("There are " & cnt & "Redemption records in DB. Id = " & oo.RedemptionNo)
                                    End If
                                End If
                            Case DBSeasonDataType.Complementary
                                Dim oo As Complimentary
                                If objt.action = DBAction.DELETE Then
                                    oo = New Complimentary
                                    oo.ComplimentaryNo = objt.obj
                                ElseIf objt.action = DBAction.DELETEBYTIME Then
                                    oo = New Complimentary
                                    oo.ComplimentaryNo = -1
                                Else
                                    oo = objt.obj
                                End If
                                If oo.ComplimentaryNo = String.Empty Then
                                    DeleteAllComplimentary()
                                ElseIf oo.ComplimentaryNo = -1 Then
                                    Dim thetm As DateTime = Now
                                    Dim para As New SqlParameter("ValidTo", thetm)
                                    Dim delnum As Integer = executeCmd(connectionstr, "delete from Complimentary where  ValidTo < @ValidTo", New SqlParameter() {para})
                                    If delnum <> 1 Then
                                        dblog.Log("Error when delete a Redemption record. Id = " & oo.ComplimentaryNo)
                                    End If
                                Else
                                    Dim par As New SqlParameter("ComplimentaryNo", oo.ComplimentaryNo)
                                    Dim cnt As Integer = executeCmdEx(connectionstr, "select count(*) from Complimentary where ComplimentaryNo = @ComplimentaryNo", New SqlParameter() {par})
                                    If cnt = 0 Then
                                        If objt.action = DBAction.UPDATE Then
                                            'Add
                                            If AddComplimentaryRecords(oo) <> 1 Then
                                                dblog.Log("Error when add a Complimentary record. ComplimentaryNo = " & oo.ComplimentaryNo)
                                            End If
                                        Else
                                            dblog.Log("Try to delete Complimentary record not exists. ComplimentaryNo = " & oo.ComplimentaryNo)
                                        End If
                                    ElseIf cnt = 1 Then
                                        If objt.action = DBAction.DELETE Then
                                            'Delete
                                            Dim delnum As Integer = executeCmd(connectionstr, "delete from Complimentary where ComplimentaryNo = @ComplimentaryNo", New SqlParameter() {par})
                                            If delnum <> 1 Then
                                                dblog.Log("Error when delete a Complimentary record. Id = " & oo.ComplimentaryNo)
                                            End If
                                        Else
                                            'Update
                                            If UpdateComplimentaryRecords(oo) <> 1 Then
                                                dblog.Log("Error when update a Complimentary record. Id = " & oo.ComplimentaryNo)
                                            End If
                                        End If
                                    Else
                                        dblog.Log("There are " & cnt & "Complimentary records in DB. Id = " & oo.ComplimentaryNo)
                                    End If
                                End If
                        End Select
                    Catch ex As Exception
                        Console.WriteLine("ProcessSeasonProc: " & ex.Message)
                    End Try
                End If

                Threading.Thread.Sleep(50)
            End While
        Catch ex As Exception
            Console.WriteLine("Error in ProcessSeasonProc")
            Console.WriteLine(ex.ToString())

            dblog.Log("Error in ProcessSeasonProc")
            dblog.Log(ex.ToString())
        End Try

    End Sub

    Public Function GetIUSeasonDataColumns() As DataColumn()
        Return mIUseasonDatacolumns
    End Function

    Public Function GetCCSeasonDataColumns() As DataColumn()
        Return mCCseasonDatacolumns
    End Function

    Public Function SelfTestForHPC() As SelfTestResult
        If RetrieveAllTerminals.Rows.Count = 0 Then
            DeleteAllRecordsForHPC()
            Return SelfTestResult.FATALDATABASEERROR
        End If

        If RetrieveAllCHUConfig.Rows.Count = 0 Then
            DeleteAllRecordsForHPC()
            Return SelfTestResult.FATALDATABASEERROR
        End If

        If RetrieveAllAntennaConfig.Rows.Count = 0 Then
            DeleteAllRecordsForHPC()
            Return SelfTestResult.FATALDATABASEERROR
        End If

        Return SelfTestResult.FINE
    End Function

    Public Sub DeleteAllRecordsForHPC()
        DeleteAllAntennaConfig()
        DeleteAllCHUConfig()
        DeleteAllTerminals()
    End Sub

    Public Sub DeleteAllRecords()
        DeleteAllAntennaConfig()
        'DeleteAllCashCardSeasonInfo()
        'DeleteAllCashCardSeasonPermits()
        'DeleteAllCHUConfig()
        'DeleteAllComplimentary()
        'DeleteAllCrossOverList()
        'DeleteAllCyclingBlockList()
        'DeleteAllCyclingCapList()
        'DeleteAllCyclingRateList()
        'DeleteAllGroupSeasonPermits()
        DeleteAllIUSeasonInfo()
        DeleteAllIUSeasonPermits()
        'DeleteAllProxSeasonInfo()
        'DeleteAllProxSeasonPermits()
        'DeleteAllRateSets()
        'DeleteAllRedemption()
        'DeleteAllREMSeasonInfo()
        'DeleteAllREMSeasonPermits()
        DeleteAllSeasonGroup()
        'DeleteAllRateComposite()
        DeleteAllPGSInfo()
        'DeleteAllAdvancedBlockList()
        'DeleteAllDiscreteBlockList()
        DeleteAllSubCarparks()
        DeleteAllTerminals()
        'DeleteAllValet()
        DeleteAllWorkingModes()
        'DeleteAllRunningHourly()
        DeleteAllRunningSeason()
        DeleteAllAddonOption()
        DeleteAllTerminalRelations()
        'DeleteAllReceiptMessage()
    End Sub

    Public Function AddBlacklistedTicketRecord(ByVal tb As DataTable) As Integer
        SyncLock connectionstr
            AddBlacklistedTicketRecord = AddRecords(tb, "BlacklistedTicket", connectionstr)
        End SyncLock
    End Function

    Public Function AddAddonOptionRecords(ByVal addon As DataTable) As Integer
        SyncLock connectionstr
            AddAddonOptionRecords = AddRecords(addon, "AddonOption", connectionstr)
        End SyncLock
    End Function

    Public Function AddAddonOptionRecords(ByVal addon As AddonOption) As Integer
        Dim tb As New DataTable
        Dim str() As String = getColumnName("AddonOption")
        Dim cols(str.Length - 1) As DataColumn
        Dim i As Integer
        For i = 0 To cols.Length - 1
            cols(i) = New DataColumn
            cols(i).ColumnName = str(i)
        Next
        tb.Columns.AddRange(cols)
        Dim ther As DataRow = tb.NewRow
        ther("CashcardConfirm") = addon.CashcardConfirm
        ther("LEDEnabled") = addon.LEDEnabled
        ther("PowerFailAlarm") = addon.PowerFailAlarm
        ther("SeasonAllowedWhenFull") = addon.SeasonAllowedWhenFull
        ther("ShutterEnabled") = addon.ShutterEnabled
        ther("TailGateSensor") = addon.TailGateSensor
        ther("SubCPWithinSubCP") = addon.SubCPWithinSubCP
        tb.Rows.Add(ther)
        Return AddAddonOptionRecords(tb)
    End Function

    Public Function AddPGSEnabled(ByVal enabled As Boolean)
        SyncLock connectionstr
            AddPGSEnabled = executeCmd(connectionstr, "Insert into PGSEnabled values(@Enabled)", New SqlParameter() {New SqlParameter("Enabled", enabled)})
        End SyncLock
    End Function

    Public Function AddCCTVEnabled(ByVal enabled As Boolean)
        SyncLock connectionstr
            AddCCTVEnabled = executeCmd(connectionstr, "Insert into CCTVEnabled values(?)", New SqlParameter() {New SqlParameter("1", enabled)})
        End SyncLock
    End Function

    Public Function AddPGSRecords(ByVal thepgs As DataTable)
        SyncLock connectionstr
            AddPGSRecords = AddRecords(thepgs, "PGS", connectionstr)
        End SyncLock
    End Function

    Public Function AddPGSRecords(ByVal thepgs() As PGSInfo) As Integer
        Dim tb As New DataTable
        Dim str() As String = getColumnName("PGS")
        Dim cols(str.Length - 1) As DataColumn
        Dim i As Integer
        For i = 0 To cols.Length - 1
            cols(i) = New DataColumn
            cols(i).ColumnName = str(i)
        Next
        tb.Columns.AddRange(cols)
        For i = 0 To thepgs.Length - 1
            Dim ther As DataRow = tb.NewRow
            ther("Connected") = thepgs(i).Connected
            ther("InformedBy") = thepgs(i).InformedBy
            ther("TerminalNo") = thepgs(i).TerminalNo
            tb.Rows.Add(ther)
        Next
        Return AddPGSRecords(tb)
    End Function

    Public Function AddAccessViolationRecords(ByVal av As DataTable) As Integer
        SyncLock connectionstr
            AddAccessViolationRecords = AddIdentifiedRecords(av, "AccessViolation", connectionstr)
        End SyncLock
    End Function

    Public Function AddAccessViolationRecords(ByVal av() As AccessViolation) As Integer
        Dim tb As New DataTable
        Dim str() As String = getColumnName("AccessViolation")
        Dim cols(str.Length - 1) As DataColumn
        Dim i As Integer
        For i = 0 To cols.Length - 1
            cols(i) = New DataColumn
            cols(i).ColumnName = str(i)
        Next
        tb.Columns.AddRange(cols)
        For i = 0 To av.Length - 1
            Dim ther As DataRow = tb.NewRow
            ther("EntryTime") = av(i).EntryTime
            ther("RelationID") = av(i).RelationID
            ther("TerminalNo") = av(i).TerminalNo
            ther("TicketNo") = av(i).TicketNo
            tb.Rows.Add(ther)
        Next
        Return AddAccessViolationRecords(tb)
    End Function

    Public Function AddAccessViolationDebitRecords(ByVal av As DataTable) As Integer
        SyncLock connectionstr
            AddAccessViolationDebitRecords = AddIdentifiedRecords(av, "AccessViolationDebit", connectionstr)
        End SyncLock
    End Function

    Public Function AddAccessViolationDebitRecords(ByVal av() As AccessViolationDebit) As Integer
        Dim tb As New DataTable
        Dim str() As String = getColumnName("AccessViolationDebit")
        Dim cols(str.Length - 1) As DataColumn
        Dim i As Integer
        For i = 0 To cols.Length - 1
            cols(i) = New DataColumn
            cols(i).ColumnName = str(i)
        Next
        tb.Columns.AddRange(cols)
        For i = 0 To av.Length - 1
            Dim ther As DataRow = tb.NewRow
            ther("EntryTime") = av(i).EntryTime
            ther("EntryTerminalNo") = av(i).EntryTerminalNo
            ther("ExitTerminalNo") = av(i).ExitTerminalNo
            ther("TicketNo") = av(i).TicketNo
            ther("ExitTime") = av(i).ExitTime
            tb.Rows.Add(ther)
        Next
        Return AddAccessViolationDebitRecords(tb)
    End Function

    Public Function AddRunningHourlyRecords(ByVal rh As DataTable) As Integer
        SyncLock connectionstr
            AddRunningHourlyRecords = AddIdentifiedRecords(rh, "RunningHourly", connectionstr)
        End SyncLock
    End Function


    Public Function AddRunningHourlyRecords(ByVal rh() As RunningHourly) As Integer
        Dim tb As New DataTable
        Dim str() As String = getColumnName("RunningHourly")
        Dim cols(str.Length - 1) As DataColumn
        Dim i As Integer
        For i = 0 To cols.Length - 1
            cols(i) = New DataColumn
            cols(i).ColumnName = str(i)
        Next
        tb.Columns.AddRange(cols)
        For i = 0 To rh.Length - 1
            Dim ther As DataRow = tb.NewRow
            'ther("Id") = rh(i).ID
            ther("EntryTime") = rh(i).EntryTime
            ther("HourlyNo") = rh(i).HourlyNo
            ther("HourlyType") = CType(rh(i).HourlyType, Short)
            ther("Status") = rh(i).Status
            ther("TerminalNo") = rh(i).TerminalNo
            tb.Rows.Add(ther)
        Next
        Return AddRunningHourlyRecords(tb)
    End Function

    Public Function AddRunningHourlyRecords(ByVal rh As RunningHourly) As Integer
        Dim tb As New DataTable
        Dim str() As String = getColumnName("RunningHourly")
        Dim cols(str.Length - 1) As DataColumn
        Dim i As Integer
        For i = 0 To cols.Length - 1
            cols(i) = New DataColumn
            cols(i).ColumnName = str(i)
        Next
        tb.Columns.AddRange(cols)
        Dim ther As DataRow = tb.NewRow
        'ther("Id") = rh.ID
        ther("EntryTime") = rh.EntryTime
        ther("HourlyNo") = rh.HourlyNo
        ther("HourlyType") = CType(rh.HourlyType, Short)
        ther("Status") = rh.Status
        ther("TerminalNo") = rh.TerminalNo
        tb.Rows.Add(ther)
        Return AddRunningHourlyRecords(tb)
    End Function

    Public Function AddRunningSeasonRecords(ByVal rs As DataTable) As Integer
        SyncLock connectionstr
            AddRunningSeasonRecords = AddIdentifiedRecords(rs, "RunningSeason", connectionstr)
        End SyncLock
    End Function

    Public Function AddRunningSeasonRecords(ByVal rs() As RunningSeason) As Integer
        Dim tb As New DataTable
        Dim str() As String = getColumnName("RunningSeason")
        Dim cols(str.Length - 1) As DataColumn
        Dim i As Integer
        For i = 0 To cols.Length - 1
            cols(i) = New DataColumn
            cols(i).ColumnName = str(i)
        Next
        tb.Columns.AddRange(cols)
        For i = 0 To rs.Length - 1
            Dim ther As DataRow = tb.NewRow
            'ther("Id") = rs(i).ID
            ther("EntryTime") = rs(i).EntryTime
            ther("SeasonNo") = rs(i).SeasonNo
            ther("SeasonType") = rs(i).SeasonType
            ther("Status") = rs(i).Status
            ther("TerminalNo") = rs(i).TerminalNo
            ther("SeasonOption") = CType(rs(i).SeasonOption, Short)
            tb.Rows.Add(ther)
        Next
        Return AddRunningSeasonRecords(tb)
    End Function

    Public Function AddRunningSeasonRecords(ByVal rs As RunningSeason) As Integer
        Dim tb As New DataTable
        Dim str() As String = getColumnName("RunningSeason")
        Dim cols(str.Length - 1) As DataColumn
        Dim i As Integer
        For i = 0 To cols.Length - 1
            cols(i) = New DataColumn
            cols(i).ColumnName = str(i)
            If str(i) = "EntryTime" Then
                cols(i).DataType = GetType(DateTime)
            End If
        Next
        tb.Columns.AddRange(cols)
        Dim ther As DataRow = tb.NewRow
        'ther("Id") = rs.ID
        ther("EntryTime") = rs.EntryTime
        ther("SeasonNo") = rs.SeasonNo
        ther("SeasonType") = rs.SeasonType
        ther("Status") = rs.Status
        ther("TerminalNo") = rs.TerminalNo
        ther("SeasonOption") = CType(rs.SeasonOption, Short)
        tb.Rows.Add(ther)
        Return AddRunningSeasonRecords(tb)
    End Function


    Public Function AddWorkingModes(ByVal wmode As DataTable) As Integer
        SyncLock connectionstr
            AddWorkingModes = AddRecords(wmode, "WorkingMode", connectionstr)
        End SyncLock
    End Function

    Public Function AddIUSeasonInfoRecords(ByVal db As ClsSeasonInfo, ByVal licenceNo As String) As Integer
        Dim tb As New DataTable
        Dim str() As String = getColumnName("IUSeasonInfo")
        Dim cols(str.Length - 1) As DataColumn
        Dim i As Integer
        For i = 0 To cols.Length - 1
            cols(i) = New DataColumn
            cols(i).ColumnName = str(i)
            'If i = 2 Or i = 3 Then
            '    cols(i).DataType = GetType(DateTime)
            'End If
            cols(i).DataType = GetType(Object)
        Next
        tb.Columns.AddRange(cols)
        Dim ther As DataRow = tb.NewRow
        ther("IULabel") = db.Ticket
        ther("ValidFrom") = db.ValidFrom
        ther("ValidTo") = db.ValidTo
        ther("SeasonType") = db.SeasonType
        ther("IOCheck") = db.IOCheck
        ther("GroupNo") = db.GroupNo
        ther("Freeze") = db.Freeze
        ther("LicenceNo") = licenceNo
        tb.Rows.Add(ther)
        Return AddIUSeasonInfoRecords(tb)
    End Function

    Public Function AddIUSeasonInfoRecords(ByVal iuseasonInfo As DataTable) As Integer
        SyncLock connectionstr
            AddIUSeasonInfoRecords = AddRecords(iuseasonInfo, "IUSeasonInfo", connectionstr)
        End SyncLock
    End Function

    ''jswei
    'Public Function GetLicenceNoForIU(iuLabel As String) As String
    '    Dim dt As DataTable = RetrieveAllIUSeasonInfo()
    '    For Each row As DataRow In dt.Rows
    '        If row("IULabel").ToString().Trim() = iuLabel.Trim() Then
    '            Return row("LicenceNo").ToString()
    '        End If
    '    Next
    '    Return String.Empty
    'End Function


    Public Function AddCashCardSeasonInfoRecords(ByVal db As ClsSeasonInfo) As Integer
        Dim tb As New DataTable
        Dim str() As String = getColumnName("CashCardSeasonInfo")
        Dim cols(str.Length - 1) As DataColumn
        Dim i As Integer
        For i = 0 To cols.Length - 1
            cols(i) = New DataColumn
            cols(i).ColumnName = str(i)
        Next
        tb.Columns.AddRange(cols)
        Dim ther As DataRow = tb.NewRow
        ther("CashCardNo") = db.Ticket
        ther("ValidFrom") = db.ValidFrom
        ther("ValidTo") = db.ValidTo
        ther("SeasonType") = db.SeasonType
        ther("IOCheck") = db.IOCheck
        ther("GroupNo") = db.GroupNo
        ther("Freeze") = db.Freeze
        tb.Rows.Add(ther)
        Return AddCashCardSeasonInfoRecords(tb)
        ''Dim iuinfo As New IUSeasonInfo
        'sInfo.IULabel = thereader("IULabel")
        ''sInfo.CashCardNo = thereader("CashCardNo")
        ''sInfo.Tenant = thereader("Tenant")
        'sInfo.ValidFrom = thereader("ValidFrom")
        'sInfo.ValidTo = thereader("ValidTo")
        ''sInfo.LicenceNo = thereader("LicenceNo")
        ''sInfo.Name = thereader("Name")
        ''sInfo.NRIC = thereader("NRIC")
        ''sInfo.Price = thereader("Price")
        'sInfo.SeasonType = IIf(thereader("SeasonType") Is DBNull.Value, -1, thereader("SeasonType"))
        'sInfo.IOCheck = thereader("IOCheck")
        'sInfo.GroupNo = IIf(thereader("GroupNo") Is DBNull.Value, -1, thereader("GroupNo"))
        ''sInfo.Remark = IIf(thereader("Remark") Is DBNull.Value, "", thereader("Remark"))
        'sInfo.Freeze = thereader("Freeze")
    End Function

    Public Function AddCashCardSeasonInfoRecords(ByVal db As DataTable) As Integer
        SyncLock connectionstr
            AddCashCardSeasonInfoRecords = AddRecords(db, "CashCardSeasonInfo", connectionstr)
        End SyncLock
    End Function

    Public Function AddProxSeasonInfoRecords(ByVal db As DataTable) As Integer
        SyncLock connectionstr
            AddProxSeasonInfoRecords = AddRecords(db, "ProxSeasonInfo", connectionstr)
        End SyncLock
    End Function

    Public Function AddREMSeasonInfoRecords(ByVal db As DataTable) As Integer
        SyncLock connectionstr
            AddREMSeasonInfoRecords = AddRecords(db, "REMSeasonInfo", connectionstr)
        End SyncLock
    End Function

    Public Function AddSeasonGroupRecords(ByVal seasgrp As ClsSeasonGroup) As Integer
        Dim tb As New DataTable
        Dim str() As String = getColumnName("SeasonGroup")
        Dim cols(str.Length - 1) As DataColumn
        Dim i As Integer
        For i = 0 To cols.Length - 1
            cols(i) = New DataColumn
            cols(i).ColumnName = str(i)
        Next
        tb.Columns.AddRange(cols)
        Dim ther As DataRow = tb.NewRow
        ther("Address") = String.Empty
        ther("CurrentCount") = seasgrp.CurrentCount
        ther("Fax") = String.Empty
        ther("GroupNo") = seasgrp.GroupNo
        ther("Maximum") = seasgrp.Maximum
        ther("Phone") = String.Empty
        ther("Remark") = String.Empty
        ther("Threshold") = seasgrp.Threshold
        tb.Rows.Add(ther)
        AddSeasonGroupRecords = AddSeasonGroupRecords(tb)
        'seasgrp.Address = IIf(thereader("Address") Is DBNull.Value, String.Empty, thereader("Address"))
        'seasgrp.CurrentCount = thereader("CurrentCount")
        'seasgrp.Fax = IIf(thereader("Fax") Is DBNull.Value, String.Empty, thereader("Fax"))
        ''seasgrp.GroupName = thereader("GroupName")
        'seasgrp.GroupNo = thereader("GroupNo")
        'seasgrp.Maximum = thereader("Maximum")
        'seasgrp.Phone = IIf(thereader("Phone") Is DBNull.Value, String.Empty, thereader("Phone"))
        'seasgrp.Remark = IIf(thereader("Remark") Is DBNull.Value, String.Empty, thereader("Remark"))
        'seasgrp.Threshold = thereader("Threshold")
        'SyncLock connectionstr
        '    Console.WriteLine("Add season group : " & seasongrp.Rows(0)(0))
        '    AddSeasonGroupRecords = AddRecords(seasongrp, "SeasonGroup", connectionstr)
        'End SyncLock
    End Function

    Public Function AddSeasonGroupRecords(ByVal seasongrp As DataTable) As Integer
        SyncLock connectionstr
            Console.WriteLine("Add season group : " & seasongrp.Rows(0)(0))
            AddSeasonGroupRecords = AddRecords(seasongrp, "SeasonGroup", connectionstr)
        End SyncLock
    End Function

    Public Function AddComplimentaryRecords(ByVal db As DataTable) As Integer
        SyncLock connectionstr
            AddComplimentaryRecords = AddRecords(db, "Complimentary", connectionstr)
        End SyncLock
    End Function

    Public Function AddComplimentaryRecords(ByVal db As Complimentary) As Integer
        Dim dt As New DataTable
        Dim str() As String = getColumnName("Complimentary")
        Dim cols(str.Length - 1) As DataColumn
        Dim i As Integer
        For i = 0 To cols.Length - 1
            cols(i) = New DataColumn
            cols(i).ColumnName = str(i)
        Next
        dt.Columns.AddRange(cols)
        Dim ther As DataRow = dt.NewRow
        ther("ComplimentaryNo") = db.ComplimentaryNo
        ther("ComplimentaryType") = CType(db.ComplimentaryType, Integer)
        ther("ValidFrom") = db.ValidFrom
        ther("ValidTo") = db.ValidTo
        dt.Rows.Add(ther)
        Return AddComplimentaryRecords(dt)
    End Function

    Public Function AddRedemptionRecords(ByVal db As Redemption) As Integer
        Dim dt As New DataTable
        Dim str() As String = getColumnName("Redemption")
        Dim cols(str.Length - 1) As DataColumn
        Dim i As Integer
        For i = 0 To cols.Length - 1
            cols(i) = New DataColumn
            cols(i).ColumnName = str(i)
        Next
        dt.Columns.AddRange(cols)
        Dim ther As DataRow = dt.NewRow
        ther("RedemptionNo") = db.RedemptionNo
        ther("RedemptionType") = CType(db.RedemptionType, Integer)
        ther("ValidFrom") = db.ValidFrom
        ther("ValidTo") = db.ValidTo
        ther("Value") = db.Value
        ther("Valuetype") = CType(db.Valuetype, Integer)
        dt.Rows.Add(ther)
        Return AddRedemptionRecords(dt)
    End Function

    Public Function AddRedemptionRecords(ByVal db As DataTable) As Integer
        SyncLock connectionstr
            AddRedemptionRecords = AddRecords(db, "Redemption", connectionstr)
        End SyncLock
    End Function

    Public Function AddValetRecords(ByVal db As DataTable) As Integer
        SyncLock connectionstr
            AddValetRecords = AddRecords(db, "Valet", connectionstr)
        End SyncLock
    End Function

    Public Function AddIUSeasonPermitsRecords(ByVal db As ClsSeasonPermits) As Integer
        Dim tb As New DataTable
        Dim str() As String = getColumnName("IUSeasonPermits")
        Dim cols(str.Length - 1) As DataColumn
        Dim i As Integer
        For i = 0 To cols.Length - 1
            cols(i) = New DataColumn
            cols(i).ColumnName = str(i)
        Next
        tb.Columns.AddRange(cols)
        Dim ther As DataRow = tb.NewRow
        ther("IULabel") = db.Ticket
        ther("CarparkNo") = db.CarparkNo
        ther("Id") = db.Id
        ther("SubCarparkNo") = db.SubCarparkNo
        tb.Rows.Add(ther)
        Return AddIUSeasonPermitsRecords(tb)
        'SyncLock connectionstr
        '    AddIUSeasonPermitsRecords = AddRecords(db, "IUSeasonPermits", connectionstr)
        'End SyncLock
    End Function

    Public Function AddIUSeasonPermitsRecords(ByVal db As DataTable) As Integer
        SyncLock connectionstr
            AddIUSeasonPermitsRecords = AddRecords(db, "IUSeasonPermits", connectionstr)
        End SyncLock
    End Function

    Public Function AddCashCardSeasonPermitsRecords(ByVal db As ClsSeasonPermits) As Integer
        Dim tb As New DataTable
        Dim str() As String = getColumnName("CashCardSeasonPermits")
        Dim cols(str.Length - 1) As DataColumn
        Dim i As Integer
        For i = 0 To cols.Length - 1
            cols(i) = New DataColumn
            cols(i).ColumnName = str(i)
        Next
        tb.Columns.AddRange(cols)
        Dim ther As DataRow = tb.NewRow
        ther("CashCardNo") = db.Ticket
        ther("CarparkNo") = db.CarparkNo
        ther("Id") = db.Id
        ther("SubCarparkNo") = db.SubCarparkNo
        tb.Rows.Add(ther)
        Return AddCashCardSeasonPermitsRecords(tb)
        'SyncLock connectionstr
        '    AddIUSeasonPermitsRecords = AddRecords(db, "IUSeasonPermits", connectionstr)
        'End SyncLock
    End Function

    Public Function AddCashCardSeasonPermitsRecords(ByVal db As DataTable) As Integer
        SyncLock connectionstr
            AddCashCardSeasonPermitsRecords = AddRecords(db, "CashCardSeasonPermits", connectionstr)
        End SyncLock
    End Function

    Public Function AddProxSeasonPermitsRecords(ByVal db As DataTable) As Integer
        SyncLock connectionstr
            AddProxSeasonPermitsRecords = AddRecords(db, "ProxSeasonPermits", connectionstr)
        End SyncLock
    End Function

    Public Function AddGroupSeasonPermitsRecords(ByVal db As GroupSeasonPermits) As Integer
        Dim tb As New DataTable
        Dim str() As String = getColumnName("GroupSeasonPermits")
        Dim cols(str.Length - 1) As DataColumn
        Dim i As Integer
        For i = 0 To cols.Length - 1
            cols(i) = New DataColumn
            cols(i).ColumnName = str(i)
        Next
        tb.Columns.AddRange(cols)
        Dim ther As DataRow = tb.NewRow
        ther("GroupNo") = db.GroupNo
        ther("CarparkNo") = db.CarparkNo
        ther("Id") = db.Id
        ther("SubCarparkNo") = db.SubCarparkNo
        tb.Rows.Add(ther)
        AddGroupSeasonPermitsRecords(tb)
        'SyncLock connectionstr
        '    AddGroupSeasonPermitsRecords = AddRecords(db, "GroupSeasonPermits", connectionstr)
        'End SyncLock
    End Function


    Public Function AddGroupSeasonPermitsRecords(ByVal db As DataTable) As Integer
        SyncLock connectionstr
            AddGroupSeasonPermitsRecords = AddRecords(db, "GroupSeasonPermits", connectionstr)
        End SyncLock
    End Function

    Public Function AddREMSeasonPermitsRecords(ByVal db As DataTable) As Integer
        SyncLock connectionstr
            AddREMSeasonPermitsRecords = AddRecords(db, "REMSeasonPermits", connectionstr)
        End SyncLock
    End Function

    Public Function AddSeasonTypeRecord(ByVal seasontype As DataTable) As Integer
        SyncLock connectionstr
            AddSeasonTypeRecord = AddRecords(seasontype, "SeasonType", connectionstr)
        End SyncLock
    End Function

    Public Function AddIUCarpkSubCpkmapRecords(ByVal therecord As DataTable) As Integer
        SyncLock connectionstr
            AddIUCarpkSubCpkmapRecords = AddRecords(therecord, "IUCarpkSubCarpkMap", connectionstr)
        End SyncLock
    End Function

    Public Function AddSubCarparkRecords(ByVal tb As DataTable) As Integer
        SyncLock connectionstr
            AddSubCarparkRecords = AddRecords(tb, "SubCarpark", connectionstr)
        End SyncLock
    End Function

    Public Function AddTerminalRecords(ByVal tb As DataTable) As Integer
        SyncLock connectionstr
            AddTerminalRecords = AddRecords(tb, "Terminal", connectionstr)
        End SyncLock
    End Function

    Public Function AddTerminalRelationRecords(ByVal tb As DataTable) As Integer
        SyncLock connectionstr
            AddTerminalRelationRecords = AddRecords(tb, "TerminalRelation", connectionstr)
        End SyncLock
    End Function

    Public Function AddCHUConfigRecords(ByVal tb As DataTable) As Integer
        SyncLock connectionstr
            AddCHUConfigRecords = AddRecords(tb, "CHUConfig", connectionstr)
        End SyncLock
    End Function

    Public Function AddAntennaConfigRecords(ByVal tb As DataTable) As Integer
        SyncLock connectionstr
            AddAntennaConfigRecords = AddRecords(tb, "AntennaConfig", connectionstr)
        End SyncLock
    End Function

    Public Function AddCyclingBlockList(ByVal tb As DataTable) As Integer
        SyncLock connectionstr
            AddCyclingBlockList = AddRecords(tb, "CyclingBlockList", connectionstr)
        End SyncLock
    End Function

    Public Function AddAdvancedBlockList(ByVal tb As DataTable) As Integer
        SyncLock connectionstr
            AddAdvancedBlockList = AddRecords(tb, "AdvancedBlockList", connectionstr)
        End SyncLock
    End Function

    Public Function AddDiscreteBlockList(ByVal tb As DataTable) As Integer
        SyncLock connectionstr
            AddDiscreteBlockList = AddRecords(tb, "DiscreteBlockList", connectionstr)
        End SyncLock
    End Function


    Public Function AddRateComposite(ByVal tb As DataTable) As Integer
        SyncLock connectionstr
            AddRateComposite = AddRecords(tb, "RateComposite", connectionstr)
        End SyncLock
    End Function

    Public Function AddMainCarparkConfig(ByVal tb As DataTable) As Integer
        SyncLock connectionstr
            AddMainCarparkConfig = AddRecords(tb, "MainConfig", connectionstr)
        End SyncLock
    End Function

    Public Function AddReceiptMessage(ByVal tb As DataTable) As Integer
        SyncLock connectionstr
            AddReceiptMessage = AddRecords(tb, "ReceiptMessage", connectionstr)
        End SyncLock
    End Function

    Public Function AddCyclingCapList(ByVal tb As DataTable) As Integer
        SyncLock connectionstr
            AddCyclingCapList = AddRecords(tb, "CyclingCapList", connectionstr)
        End SyncLock
    End Function

    Public Function AddCyclingRateList(ByVal tb As DataTable) As Integer
        SyncLock connectionstr
            AddCyclingRateList = AddRecords(tb, "CyclingRateList", connectionstr)
        End SyncLock
    End Function

    Public Function AddCrossOverList(ByVal tb As DataTable) As Integer
        SyncLock connectionstr
            AddCrossOverList = AddRecords(tb, "CrossOverList", connectionstr)
        End SyncLock
    End Function

    Public Function AddRateSetRecords(ByVal tb As DataTable) As Integer
        SyncLock connectionstr
            AddRateSetRecords = AddRecords(tb, "RateSet", connectionstr)
        End SyncLock
    End Function

    Public Function AddRateSetRecords(ByVal rts() As RateSet) As Integer
        Dim tb As New DataTable
        Dim str() As String = getColumnName("RateSet")
        Dim cols(str.Length - 1) As DataColumn
        Dim i As Integer
        For i = 0 To cols.Length - 1
            cols(i) = New DataColumn
            cols(i).ColumnName = str(i)
        Next
        tb.Columns.AddRange(cols)
        For i = 0 To rts.Length - 1
            Dim ther As DataRow = tb.NewRow
            ther("RateSetNo") = rts(i).RateSetNo
            ther("Car") = rts(i).Car
            ther("Taxi") = rts(i).Taxi
            ther("Container") = rts(i).Container
            ther("Lorry") = rts(i).Lorry
            ther("Motorcycle") = rts(i).Motorcycle
            ther("Reserve1") = rts(i).Reserve1
            ther("Reserve2") = rts(i).Reserve2
            tb.Rows.Add(ther)
        Next
        Return AddRateSetRecords(tb)
    End Function

    Public Function AddHolidayRecords(ByVal tb As DataTable)
        SyncLock connectionstr
            AddHolidayRecords = AddRecords(tb, "Holiday", connectionstr)
        End SyncLock
    End Function

    Public Function AddHolidayRecords(ByVal holi() As StrucHoliday) As Integer
        Dim tb As New DataTable
        Dim str() As String = getColumnName("Holiday")
        Dim cols(str.Length - 1) As DataColumn
        Dim i As Integer
        For i = 0 To cols.Length - 1
            cols(i) = New DataColumn
            cols(i).ColumnName = str(i)
        Next
        tb.Columns.AddRange(cols)
        For i = 0 To holi.Length - 1
            Dim ther As DataRow = tb.NewRow
            ther("HolidayID") = holi(i).HolidayID
            ther("HDate") = holi(i).HDate
            tb.Rows.Add(ther)
        Next
        Return AddHolidayRecords(tb)
    End Function

    Public Function AddFullStatusRecords(ByVal tb As DataTable) As Integer
        SyncLock connectionstr
            AddFullStatusRecords = AddRecords(tb, "FullStatus", connectionstr)
        End SyncLock
    End Function

    Public Function AddFullStatusRecord(ByVal Termno As Short, ByVal stat As Boolean) As Integer
        Dim tb As New DataTable
        Dim str() As String = getColumnName("FullStatus")
        Dim cols(str.Length - 1) As DataColumn
        Dim i As Integer
        For i = 0 To cols.Length - 1
            cols(i) = New DataColumn
            cols(i).ColumnName = str(i)
        Next
        tb.Columns.AddRange(cols)
        Dim ther As DataRow = tb.NewRow
        ther("FullSign") = stat
        ther("TerminalNo") = Termno
        tb.Rows.Add(ther)
        Return AddFullStatusRecords(tb)
    End Function


    Public Function UpdatePGSRecords(ByVal thepgs As DataTable)
        SyncLock connectionstr
            UpdatePGSRecords = updateRecords(thepgs, "PGS", connectionstr)
        End SyncLock
    End Function

    Public Function UpdatePGSRecords(ByVal thepgs() As PGSInfo) As Integer
        Dim tb As New DataTable
        Dim str() As String = getColumnName("PGS")
        Dim cols(str.Length - 1) As DataColumn
        Dim i As Integer
        For i = 0 To cols.Length - 1
            cols(i) = New DataColumn
            cols(i).ColumnName = str(i)
        Next
        tb.Columns.AddRange(cols)
        For i = 0 To thepgs.Length - 1
            Dim ther As DataRow = tb.NewRow
            ther("Connected") = thepgs(i).Connected
            ther("InformedBy") = thepgs(i).InformedBy
            ther("TerminalNo") = thepgs(i).TerminalNo
            tb.Rows.Add(ther)
        Next
        Return UpdatePGSRecords(tb)
    End Function

    Public Function UpdatePGSEnabled(ByVal enabled As Boolean)
        SyncLock connectionstr
            UpdatePGSEnabled = executeCmd(connectionstr, "update PGSEnabled set Enabled = @Enabled", New SqlParameter() {New SqlParameter("Enabled", enabled)})
        End SyncLock
    End Function

    Public Function UpdateCCTVEnabled(ByVal enabled As Boolean)
        SyncLock connectionstr
            UpdateCCTVEnabled = executeCmd(connectionstr, "update CCTVEnabled set Enabled = @Enabled", New SqlParameter() {New SqlParameter("Enabled", enabled)})
        End SyncLock
    End Function

    Public Function UpdateRunningHourlyRecords(ByVal rh As DataTable)
        Dim sqlString As String
        Dim numbers As Integer = 0
        Dim hasError As Boolean = False

        Dim colnames() As String = getColumnName("RunningHourly")
        Dim strbuild As New Text.StringBuilder(10)
        strbuild.Append("UPDATE ")
        strbuild.Append("RunningHourly")
        strbuild.Append(" set ")
        Dim i As Integer
        For i = 1 To colnames.Length - 2
            strbuild.Append(colnames(i))
            strbuild.Append("=?, ")
        Next
        strbuild.Append(colnames(colnames.Length - 1))
        strbuild.Append("=? ")
        strbuild.Append("where ")
        strbuild.Append(colnames(1))
        strbuild.Append("=?")
        sqlString = strbuild.ToString()

        Dim sqlcn As New SqlConnection(connectionstr)
        Dim cmd As SqlCommand
        Dim therow As DataRow

        Try
            sqlcn.Open()
            For Each therow In rh.Rows
                cmd = sqlcn.CreateCommand
                cmd.Parameters.Clear()
                cmd.CommandText = sqlString
                For i = 1 To colnames.Length - 1
                    Dim para As New SqlParameter(colnames(i), therow(i))
                    cmd.Parameters.Add(para)
                Next
                Dim para1 As New SqlParameter(colnames(1), therow(1))
                cmd.Parameters.Add(para1)
                cmd.ExecuteNonQuery()
                numbers += 1
            Next
        Catch ex As Exception
            hasError = True
            cmd.Parameters.Clear()
            cmd.Dispose()
            sqlcn.Close()
            Console.WriteLine("RunningHourly" & "Update :" & ex.ToString)
        Finally
            If hasError = False Then
                cmd.Parameters.Clear()
                cmd.Dispose()
                sqlcn.Close()
            End If
        End Try
        Return numbers
    End Function

    Public Function UpdateRunningHourlyRecords(ByVal rh() As RunningHourly) As Integer
        Dim tb As New DataTable
        Dim str() As String = getColumnName("RunningHourly")
        Dim cols(str.Length - 1) As DataColumn
        Dim i As Integer
        For i = 0 To cols.Length - 1
            cols(i) = New DataColumn
            cols(i).ColumnName = str(i)
        Next
        tb.Columns.AddRange(cols)
        For i = 0 To rh.Length - 1
            Dim ther As DataRow = tb.NewRow
            ther("Id") = rh(i).ID
            ther("EntryTime") = rh(i).EntryTime
            ther("HourlyNo") = rh(i).HourlyNo
            ther("HourlyType") = CType(rh(i).HourlyType, Short)
            ther("Status") = rh(i).Status
            ther("TerminalNo") = rh(i).TerminalNo
            tb.Rows.Add(ther)
        Next
        Return UpdateRunningHourlyRecords(tb)
    End Function

    Public Function UpdateRunningHourlyRecords(ByVal rh As RunningHourly) As Integer
        Dim tb As New DataTable
        Dim str() As String = getColumnName("RunningHourly")
        Dim cols(str.Length - 1) As DataColumn
        Dim i As Integer
        For i = 0 To cols.Length - 1
            cols(i) = New DataColumn
            cols(i).ColumnName = str(i)
        Next
        tb.Columns.AddRange(cols)
        Dim ther As DataRow = tb.NewRow
        ther("Id") = rh.ID
        ther("EntryTime") = rh.EntryTime
        ther("HourlyNo") = rh.HourlyNo
        ther("HourlyType") = CType(rh.HourlyType, Short)
        ther("Status") = rh.Status
        ther("TerminalNo") = rh.TerminalNo
        tb.Rows.Add(ther)
        Return UpdateRunningHourlyRecords(tb)
    End Function

    Public Function UpdateRunningSeasonRecords(ByVal rs As DataTable)
        Dim sqlString As String
        Dim numbers As Integer = 0
        Dim hasError As Boolean = False

        Dim colnames() As String = getColumnName("RunningSeason")
        Dim strbuild As New Text.StringBuilder(10)
        strbuild.Append("UPDATE ")
        strbuild.Append("RunningSeason")
        strbuild.Append(" set ")
        Dim i As Integer
        For i = 2 To colnames.Length - 2
            strbuild.Append(colnames(i))
            strbuild.Append("=@")
            strbuild.Append(colnames(i))
            strbuild.Append(", ")
        Next
        strbuild.Append(colnames(colnames.Length - 1))
        strbuild.Append("=@")
        strbuild.Append(colnames(i))
        strbuild.Append(" where ")
        strbuild.Append(colnames(1))
        strbuild.Append("=@")
        strbuild.Append(colnames(1))
        sqlString = strbuild.ToString()

        Dim sqlcn As New SqlConnection(connectionstr)
        Dim cmd As SqlCommand
        Dim therow As DataRow

        Try
            sqlcn.Open()
            For Each therow In rs.Rows
                cmd = sqlcn.CreateCommand
                cmd.Parameters.Clear()
                cmd.CommandText = sqlString
                For i = 2 To colnames.Length - 1
                    Dim para As New SqlParameter(colnames(i), therow(i))
                    cmd.Parameters.Add(para)
                Next
                Dim para1 As New SqlParameter(colnames(1), therow(1))
                cmd.Parameters.Add(para1)
                cmd.ExecuteNonQuery()
                numbers += 1
            Next
        Catch ex As Exception
            hasError = True
            cmd.Parameters.Clear()
            cmd.Dispose()
            sqlcn.Close()
            Console.WriteLine("RunningSeason" & "Update :" & ex.ToString)
        Finally
            If hasError = False Then
                cmd.Parameters.Clear()
                cmd.Dispose()
                sqlcn.Close()
            End If
        End Try
        Return numbers
    End Function

    Public Function UpdateAccessViolationRecords(ByVal av() As AccessViolation) As Integer
        Dim tb As New DataTable
        Dim str() As String = getColumnName("AccessViolation")
        Dim cols(str.Length - 1) As DataColumn
        Dim i As Integer
        For i = 0 To cols.Length - 1
            cols(i) = New DataColumn
            cols(i).ColumnName = str(i)
        Next
        tb.Columns.AddRange(cols)
        For i = 0 To av.Length - 1
            Dim ther As DataRow = tb.NewRow
            ther("EntryTime") = av(i).EntryTime
            ther("RelationID") = av(i).RelationID
            ther("TerminalNo") = av(i).TerminalNo
            ther("TicketNo") = av(i).TicketNo
            tb.Rows.Add(ther)
        Next
        Return UpdateAccessViolationRecords(tb)
    End Function

    Public Function UpdateAccessViolationRecords(ByVal av As DataTable) As Integer
        Dim sqlString As String
        Dim numbers As Integer = 0
        Dim hasError As Boolean = False

        Dim colnames() As String = getColumnName("AccessViolation")
        Dim strbuild As New Text.StringBuilder(10)
        strbuild.Append("UPDATE ")
        strbuild.Append("AccessViolation")
        strbuild.Append(" set ")
        Dim i As Integer
        For i = 1 To colnames.Length - 2
            strbuild.Append(colnames(i))
            strbuild.Append("=?, ")
        Next
        strbuild.Append(colnames(colnames.Length - 1))
        strbuild.Append("=? ")
        strbuild.Append("where ")
        strbuild.Append(colnames(1))
        strbuild.Append("=?")
        sqlString = strbuild.ToString()

        Dim sqlcn As New SqlConnection(connectionstr)
        Dim cmd As SqlCommand
        Dim therow As DataRow

        Try
            sqlcn.Open()
            For Each therow In av.Rows
                cmd = sqlcn.CreateCommand
                cmd.Parameters.Clear()
                cmd.CommandText = sqlString
                For i = 1 To colnames.Length - 1
                    Dim para As New SqlParameter(colnames(i), therow(i))
                    cmd.Parameters.Add(para)
                Next
                Dim para1 As New SqlParameter(colnames(1), therow(1))
                cmd.Parameters.Add(para1)
                cmd.ExecuteNonQuery()
                numbers += 1
            Next
        Catch ex As Exception
            hasError = True
            cmd.Parameters.Clear()
            cmd.Dispose()
            sqlcn.Close()
            Console.WriteLine("AccessViolation" & "Update :" & ex.ToString)
        Finally
            If hasError = False Then
                cmd.Parameters.Clear()
                cmd.Dispose()
                sqlcn.Close()
            End If
        End Try
        Return numbers
    End Function

    Public Function UpdateRunningSeasonRecords(ByVal rs() As RunningSeason) As Integer
        Dim tb As New DataTable
        Dim str() As String = getColumnName("RunningSeason")
        Dim cols(str.Length - 1) As DataColumn
        Dim i As Integer
        For i = 0 To cols.Length - 1
            cols(i) = New DataColumn
            cols(i).ColumnName = str(i)
        Next
        tb.Columns.AddRange(cols)
        For i = 0 To rs.Length - 1
            Dim ther As DataRow = tb.NewRow
            ther("Id") = rs(i).ID
            ther("EntryTime") = rs(i).EntryTime
            ther("SeasonNo") = rs(i).SeasonNo
            ther("SeasonType") = rs(i).SeasonType
            ther("Status") = rs(i).Status
            ther("TerminalNo") = rs(i).TerminalNo
            ther("SeasonOption") = CType(rs(i).SeasonOption, Short)
            tb.Rows.Add(ther)
        Next
        Return UpdateRunningSeasonRecords(tb)
    End Function

    Public Function UpdateRunningSeasonRecords(ByVal rs As RunningSeason) As Integer
        Dim tb As New DataTable
        Dim str() As String = getColumnName("RunningSeason")
        Dim cols(str.Length - 1) As DataColumn
        Dim i As Integer
        For i = 0 To cols.Length - 1
            cols(i) = New DataColumn
            cols(i).ColumnName = str(i)
        Next
        tb.Columns.AddRange(cols)
        Dim ther As DataRow = tb.NewRow
        ther("Id") = rs.ID
        ther("EntryTime") = rs.EntryTime
        ther("SeasonNo") = rs.SeasonNo
        ther("SeasonType") = rs.SeasonType
        ther("Status") = rs.Status
        ther("TerminalNo") = rs.TerminalNo
        ther("SeasonOption") = CType(rs.SeasonOption, Short)
        tb.Rows.Add(ther)
        Return UpdateRunningSeasonRecords(tb)
    End Function

    Public Function UpdateFullStatusRecord(ByVal Termno As Short, ByVal stat As Boolean) As Integer
        Dim tb As New DataTable
        Dim str() As String = getColumnName("FullStatus")
        Dim cols(str.Length - 1) As DataColumn
        Dim i As Integer
        For i = 0 To cols.Length - 1
            cols(i) = New DataColumn
            cols(i).ColumnName = str(i)
        Next
        tb.Columns.AddRange(cols)
        Dim ther As DataRow = tb.NewRow
        ther("FullSign") = stat
        ther("TerminalNo") = Termno
        tb.Rows.Add(ther)
        Return UpdateFullStatusRecords(tb)
    End Function

    Public Function UpdateFullStatusRecords(ByVal tb As DataTable) As Integer
        SyncLock connectionstr
            UpdateFullStatusRecords = updateRecords(tb, "FullStatus", connectionstr)
        End SyncLock
    End Function

    Public Function UpdateBlacklistedTicketRecords(ByVal tb As DataTable) As Integer
        SyncLock connectionstr
            UpdateBlacklistedTicketRecords = updateRecords(tb, "BlacklistedTicket", connectionstr)
        End SyncLock
    End Function

    Public Function UpdateTerminalRecords(ByVal tb As DataTable) As Integer
        SyncLock connectionstr
            UpdateTerminalRecords = updateRecords(tb, "Terminal", connectionstr)
        End SyncLock
    End Function

    Public Function UpdateCHUConfigRecords(ByVal tb As DataTable) As Integer
        SyncLock connectionstr
            UpdateCHUConfigRecords = updateRecords(tb, "CHUConfig", connectionstr)
        End SyncLock
    End Function

    Public Function UpdateAntennaConfigRecords(ByVal tb As DataTable) As Integer
        SyncLock connectionstr
            UpdateAntennaConfigRecords = updateRecords(tb, "AntennaConfig", connectionstr)
        End SyncLock
    End Function

    Public Function UpdateWorkingModeRecords(ByVal tb As DataTable) As Integer
        SyncLock connectionstr
            UpdateWorkingModeRecords = updateRecords(tb, "WorkingMode", connectionstr)
        End SyncLock
    End Function

    Public Function UpdateSubCarparkRecords(ByVal tb As DataTable) As Integer
        SyncLock connectionstr
            UpdateSubCarparkRecords = updateRecords(tb, "SubCarpark", connectionstr)
        End SyncLock
    End Function

    Public Function UpdateIUSeasonInfoRecords(ByVal db As ClsSeasonInfo, ByVal licenceNo As String) As Integer
        Dim tb As New DataTable
        Dim str() As String = getColumnName("IUSeasonInfo")
        Dim cols(str.Length - 1) As DataColumn
        Dim i As Integer
        For i = 0 To cols.Length - 1
            cols(i) = New DataColumn
            cols(i).ColumnName = str(i)
            If i = 2 Or i = 3 Then
                cols(i).DataType = GetType(DateTime)
            End If
        Next
        tb.Columns.AddRange(cols)
        Dim ther As DataRow = tb.NewRow
        ther("IULabel") = db.Ticket
        ther("ValidFrom") = db.ValidFrom
        ther("ValidTo") = db.ValidTo
        ther("SeasonType") = db.SeasonType
        ther("IOCheck") = db.IOCheck
        ther("GroupNo") = db.GroupNo
        ther("Freeze") = db.Freeze
        ther("LicenceNo") = licenceNo
        tb.Rows.Add(ther)
        Return UpdateIUSeasonInfoRecords(tb)
        'SyncLock connectionstr
        '    UpdateIUSeasonInfoRecords = updateRecords(tb, "IUSeasonInfo", connectionstr)
        'End SyncLock
    End Function

    Public Function UpdateIUSeasonInfoRecords(ByVal tb As DataTable) As Integer
        SyncLock connectionstr
            UpdateIUSeasonInfoRecords = updateRecords(tb, "IUSeasonInfo", connectionstr)
        End SyncLock
    End Function

    Public Function UpdateCashCardSeasonInfoRecords(ByVal db As ClsSeasonInfo) As Integer
        Dim tb As New DataTable
        Dim str() As String = getColumnName("CashCardSeasonInfo")
        Dim cols(str.Length - 1) As DataColumn
        Dim i As Integer
        For i = 0 To cols.Length - 1
            cols(i) = New DataColumn
            cols(i).ColumnName = str(i)
        Next
        tb.Columns.AddRange(cols)
        Dim ther As DataRow = tb.NewRow
        ther("CashCardNo") = db.Ticket
        ther("ValidFrom") = db.ValidFrom
        ther("ValidTo") = db.ValidTo
        ther("SeasonType") = db.SeasonType
        ther("IOCheck") = db.IOCheck
        ther("GroupNo") = db.GroupNo
        ther("Freeze") = db.Freeze
        tb.Rows.Add(ther)
        Return UpdateCashCardSeasonInfoRecords(tb)

        'SyncLock connectionstr
        '    UpdateCashCardSeasonInfoRecords = updateRecords(tb, "CashCardSeasonInfo", connectionstr)
        'End SyncLock
    End Function

    Public Function UpdateCashCardSeasonInfoRecords(ByVal tb As DataTable) As Integer
        SyncLock connectionstr
            UpdateCashCardSeasonInfoRecords = updateRecords(tb, "CashCardSeasonInfo", connectionstr)
        End SyncLock
    End Function

    Public Function UpdateProxSeasonInfoRecords(ByVal tb As DataTable) As Integer
        SyncLock connectionstr
            UpdateProxSeasonInfoRecords = updateRecords(tb, "ProxSeasonInfo", connectionstr)
        End SyncLock
    End Function

    Public Function UpdateREMSeasonInfoRecords(ByVal tb As DataTable) As Integer
        SyncLock connectionstr
            UpdateREMSeasonInfoRecords = updateRecords(tb, "REMSeasonInfo", connectionstr)
        End SyncLock
    End Function

    Public Function UpdateSeasonGroupRecords(ByVal seasgrp As ClsSeasonGroup) As Integer
        Dim tb As New DataTable
        Dim str() As String = getColumnName("SeasonGroup")
        Dim cols(str.Length - 1) As DataColumn
        Dim i As Integer
        For i = 0 To cols.Length - 1
            cols(i) = New DataColumn
            cols(i).ColumnName = str(i)
        Next
        tb.Columns.AddRange(cols)
        Dim ther As DataRow = tb.NewRow
        ther("Address") = String.Empty
        ther("CurrentCount") = seasgrp.CurrentCount
        ther("Fax") = String.Empty
        ther("GroupNo") = seasgrp.GroupNo
        ther("Maximum") = seasgrp.Maximum
        ther("Phone") = String.Empty
        ther("Remark") = String.Empty
        ther("Threshold") = seasgrp.Threshold
        tb.Rows.Add(ther)
        UpdateSeasonGroupRecords(tb)
        'seasgrp.Address = IIf(thereader("Address") Is DBNull.Value, String.Empty, thereader("Address"))
        'seasgrp.CurrentCount = thereader("CurrentCount")
        'seasgrp.Fax = IIf(thereader("Fax") Is DBNull.Value, String.Empty, thereader("Fax"))
        ''seasgrp.GroupName = thereader("GroupName")
        'seasgrp.GroupNo = thereader("GroupNo")
        'seasgrp.Maximum = thereader("Maximum")
        'seasgrp.Phone = IIf(thereader("Phone") Is DBNull.Value, String.Empty, thereader("Phone"))
        'seasgrp.Remark = IIf(thereader("Remark") Is DBNull.Value, String.Empty, thereader("Remark"))
        'seasgrp.Threshold = thereader("Threshold")
        'SyncLock connectionstr
        '    Console.WriteLine("Add season group : " & seasongrp.Rows(0)(0))
        '    AddSeasonGroupRecords = AddRecords(seasongrp, "SeasonGroup", connectionstr)
        'End SyncLock
    End Function

    Public Function UpdateSeasonGroupRecords(ByVal tb As DataTable) As Integer
        SyncLock connectionstr
            UpdateSeasonGroupRecords = updateRecords(tb, "SeasonGroup", connectionstr)
        End SyncLock
    End Function

    Public Function UpdateComplimentaryRecords(ByVal tb As DataTable) As Integer
        SyncLock connectionstr
            UpdateComplimentaryRecords = updateRecords(tb, "Complimentary", connectionstr)
        End SyncLock
    End Function

    Public Function UpdateComplimentaryRecords(ByVal db As Complimentary) As Integer
        Dim dt As New DataTable
        Dim str() As String = getColumnName("Complimentary")
        Dim cols(str.Length - 1) As DataColumn
        Dim i As Integer
        For i = 0 To cols.Length - 1
            cols(i) = New DataColumn
            cols(i).ColumnName = str(i)
        Next
        dt.Columns.AddRange(cols)
        Dim ther As DataRow = dt.NewRow
        ther("ComplimentaryNo") = db.ComplimentaryNo
        ther("ComplimentaryType") = CType(db.ComplimentaryType, Integer)
        ther("ValidFrom") = db.ValidFrom
        ther("ValidTo") = db.ValidTo
        dt.Rows.Add(ther)
        Return UpdateComplimentaryRecords(dt)
    End Function

    Public Function UpdateRedemptionRecords(ByVal db As Redemption) As Integer
        Dim dt As New DataTable
        Dim str() As String = getColumnName("Redemption")
        Dim cols(str.Length - 1) As DataColumn
        Dim i As Integer
        For i = 0 To cols.Length - 1
            cols(i) = New DataColumn
            cols(i).ColumnName = str(i)
        Next
        dt.Columns.AddRange(cols)
        Dim ther As DataRow = dt.NewRow
        ther("RedemptionNo") = db.RedemptionNo
        ther("RedemptionType") = CType(db.RedemptionType, Integer)
        ther("ValidFrom") = db.ValidFrom
        ther("ValidTo") = db.ValidTo
        ther("Value") = db.Value
        ther("Valuetype") = CType(db.Valuetype, Integer)
        dt.Rows.Add(ther)
        Return UpdateRedemptionRecords(dt)
    End Function

    Public Function UpdateRedemptionRecords(ByVal tb As DataTable) As Integer
        SyncLock connectionstr
            UpdateRedemptionRecords = updateRecords(tb, "Redemption", connectionstr)
        End SyncLock
    End Function

    Public Function UpdateValetRecords(ByVal tb As DataTable) As Integer
        SyncLock connectionstr
            UpdateValetRecords = updateRecords(tb, "Valet", connectionstr)
        End SyncLock
    End Function

    Public Function UpdateIUSeasonPermitsRecords(ByVal db As ClsSeasonPermits) As Integer
        Dim tb As New DataTable
        Dim str() As String = getColumnName("CashCardSeasonPermits")
        Dim cols(str.Length - 1) As DataColumn
        Dim i As Integer
        For i = 0 To cols.Length - 1
            cols(i) = New DataColumn
            cols(i).ColumnName = str(i)
        Next
        tb.Columns.AddRange(cols)
        Dim ther As DataRow = tb.NewRow
        ther("CashCardNo") = db.Ticket
        ther("CarparkNo") = db.CarparkNo
        ther("Id") = db.Id
        ther("SubCarparkNo") = db.SubCarparkNo
        tb.Rows.Add(ther)
        Return UpdateIUSeasonPermitsRecords(tb)
        'SyncLock connectionstr
        '    AddIUSeasonPermitsRecords = AddRecords(db, "IUSeasonPermits", connectionstr)
        'End SyncLock
    End Function

    Public Function UpdateIUSeasonPermitsRecords(ByVal tb As DataTable) As Integer
        SyncLock connectionstr
            UpdateIUSeasonPermitsRecords = updateRecords(tb, "IUSeasonPermits", connectionstr)
        End SyncLock
    End Function

    Public Function UpdateCashCardSeasonPermitsRecords(ByVal db As ClsSeasonPermits) As Integer
        Dim tb As New DataTable
        Dim str() As String = getColumnName("CashCardSeasonPermits")
        Dim cols(str.Length - 1) As DataColumn
        Dim i As Integer
        For i = 0 To cols.Length - 1
            cols(i) = New DataColumn
            cols(i).ColumnName = str(i)
        Next
        tb.Columns.AddRange(cols)
        Dim ther As DataRow = tb.NewRow
        ther("CashCardNo") = db.Ticket
        ther("CarparkNo") = db.CarparkNo
        ther("Id") = db.Id
        ther("SubCarparkNo") = db.SubCarparkNo
        tb.Rows.Add(ther)
        Return UpdateCashCardSeasonPermitsRecords(tb)
        'SyncLock connectionstr
        '    AddIUSeasonPermitsRecords = AddRecords(db, "IUSeasonPermits", connectionstr)
        'End SyncLock
    End Function

    Public Function UpdateCashCardSeasonPermitsRecords(ByVal tb As DataTable) As Integer
        SyncLock connectionstr
            UpdateCashCardSeasonPermitsRecords = updateRecords(tb, "CashCardSeasonPermits", connectionstr)
        End SyncLock
    End Function

    Public Function UpdateProxSeasonPermitsRecords(ByVal tb As DataTable) As Integer
        SyncLock connectionstr
            UpdateProxSeasonPermitsRecords = updateRecords(tb, "ProxSeasonPermits", connectionstr)
        End SyncLock
    End Function

    Public Function UpdateREMSeasonPermitsRecords(ByVal tb As DataTable) As Integer
        SyncLock connectionstr
            UpdateREMSeasonPermitsRecords = updateRecords(tb, "REMSeasonPermits", connectionstr)
        End SyncLock
    End Function

    Public Function UpdateGroupSeasonPermitsRecords(ByVal db As GroupSeasonPermits) As Integer
        Dim tb As New DataTable
        Dim str() As String = getColumnName("GroupSeasonPermits")
        Dim cols(str.Length - 1) As DataColumn
        Dim i As Integer
        For i = 0 To cols.Length - 1
            cols(i) = New DataColumn
            cols(i).ColumnName = str(i)
        Next
        tb.Columns.AddRange(cols)
        Dim ther As DataRow = tb.NewRow
        ther("GroupNo") = db.GroupNo
        ther("CarparkNo") = db.CarparkNo
        ther("Id") = db.Id
        ther("SubCarparkNo") = db.SubCarparkNo
        tb.Rows.Add(ther)
        UpdateGroupSeasonPermitsRecords(tb)
        'SyncLock connectionstr
        '    AddGroupSeasonPermitsRecords = AddRecords(db, "GroupSeasonPermits", connectionstr)
        'End SyncLock
    End Function

    Public Function UpdateGroupSeasonPermitsRecords(ByVal tb As DataTable) As Integer
        SyncLock connectionstr
            UpdateGroupSeasonPermitsRecords = updateRecords(tb, "GroupSeasonPermits", connectionstr)
        End SyncLock
    End Function

    Public Function UpdateRateSet(ByVal tb As DataTable) As Integer
        SyncLock connectionstr
            UpdateRateSet = updateRecords(tb, "RateSet", connectionstr)
        End SyncLock
    End Function

    Public Function UpdateRateset(ByVal rts() As RateSet) As Integer
        Dim tb As New DataTable
        Dim str() As String = getColumnName("RateSet")
        Dim cols(str.Length - 1) As DataColumn
        Dim i As Integer
        For i = 0 To cols.Length - 1
            cols(i) = New DataColumn
            cols(i).ColumnName = str(i)
        Next
        tb.Columns.AddRange(cols)
        For i = 0 To rts.Length - 1
            Dim ther As DataRow = tb.NewRow
            ther("RateSetNo") = rts(i).RateSetNo
            ther("Car") = rts(i).Car
            ther("Taxi") = rts(i).Taxi
            ther("Container") = rts(i).Container
            ther("Lorry") = rts(i).Lorry
            ther("Motorcycle") = rts(i).Motorcycle
            ther("Reserve1") = rts(i).Reserve1
            ther("Reserve2") = rts(i).Reserve2
            tb.Rows.Add(ther)
        Next
        Return UpdateRateset(tb)
    End Function

    Public Function UpdateHolidayRecords(ByVal tb As DataTable)
        SyncLock connectionstr
            UpdateHolidayRecords = updateRecords(tb, "Holiday", connectionstr)
        End SyncLock
    End Function

    Public Function UpdateHolidayRecords(ByVal holi() As StrucHoliday) As Integer
        Dim tb As New DataTable
        Dim str() As String = getColumnName("Holiday")
        Dim cols(str.Length - 1) As DataColumn
        Dim i As Integer
        For i = 0 To cols.Length - 1
            cols(i) = New DataColumn
            cols(i).ColumnName = str(i)
        Next
        tb.Columns.AddRange(cols)
        For i = 0 To holi.Length - 1
            Dim ther As DataRow = tb.NewRow
            ther("HolidayID") = holi(i).HolidayID
            ther("HDate") = holi(i).HDate
            tb.Rows.Add(ther)
        Next
        Return UpdateHolidayRecords(tb)
    End Function

    Public Function RetrieveAllBlacklistedTicket() As DataTable
        SyncLock connectionstr
            RetrieveAllBlacklistedTicket = executeCmd(connectionstr, "SELECT * FROM BlacklistedTicket")
        End SyncLock
    End Function

    Public Function RetrieveAllAddonOption() As DataTable
        SyncLock connectionstr
            RetrieveAllAddonOption = executeCmd(connectionstr, "select * from AddonOption")
        End SyncLock
    End Function

    Public Function RetrieveAllPGSInfo() As DataTable
        SyncLock connectionstr
            RetrieveAllPGSInfo = executeCmd(connectionstr, "select * from PGS")
        End SyncLock
    End Function

    Public Function RetrieveAllPGSInfoEx() As PGSInfo()
        Dim tb As DataTable = RetrieveAllPGSInfo()
        Dim rh(tb.Rows.Count - 1) As PGSInfo
        Dim rw As DataRow
        Dim i As Integer = 0
        For Each rw In tb.Rows
            rh(i).Connected = rw("Connected")
            rh(i).InformedBy = rw("InformedBy")
            rh(i).TerminalNo = rw("TerminalNo")
            i += 1
        Next
        Return rh
    End Function

    Public Function RetrieveAllRunningHourly() As DataTable
        SyncLock connectionstr
            RetrieveAllRunningHourly = executeCmd(connectionstr, "select * from RunningHourly")
        End SyncLock
    End Function

    Public Function RetrieveAllRunningHourlyEx() As RunningHourly()
        Dim tb As DataTable = RetrieveAllRunningHourly()
        Dim rh(tb.Rows.Count - 1) As RunningHourly
        Dim rw As DataRow
        Dim i As Integer = 0
        For Each rw In tb.Rows
            rh(i) = New RunningHourly
            rh(i).ID = rw("Id")
            rh(i).EntryTime = rw("EntryTime")
            rh(i).HourlyNo = rw("HourlyNo")
            rh(i).HourlyType = rw("HourlyType")
            rh(i).Status = rw("Status")
            rh(i).TerminalNo = rw("TerminalNo")
            i += 1
        Next
        Return rh
    End Function

    Public Function RetrieveAllRunningSeason() As DataTable
        SyncLock connectionstr
            RetrieveAllRunningSeason = executeCmd(connectionstr, "select * from RunningSeason")
        End SyncLock
    End Function

    Public Function RetrieveAllRunningSeasonEx() As RunningSeason()
        Dim tb As DataTable = RetrieveAllRunningSeason()
        Dim rs(tb.Rows.Count - 1) As RunningSeason
        Dim rw As DataRow
        Dim i As Integer = 0
        For Each rw In tb.Rows
            rs(i) = New RunningSeason
            rs(i).ID = rw("Id")
            rs(i).EntryTime = rw("EntryTime")
            rs(i).SeasonNo = rw("SeasonNo")
            rs(i).SeasonType = rw("SeasonType")
            rs(i).Status = rw("Status")
            rs(i).TerminalNo = rw("TerminalNo")
            rs(i).SeasonOption = rw("SeasonOption")
            i += 1
        Next
        Return rs
    End Function

    Public Function RetrieveAllWorkingModes() As DataTable
        SyncLock connectionstr
            RetrieveAllWorkingModes = executeCmd(connectionstr, "select * from WorkingMode order by WorkingMode")
        End SyncLock
    End Function

    Public Function RetrieveAllTerminals() As DataTable
        SyncLock connectionstr
            RetrieveAllTerminals = executeCmd(connectionstr, "select * from Terminal")
        End SyncLock
    End Function

    Public Function RetrieveAllTerminalsEx() As Terminal()
        Dim tb As DataTable = RetrieveAllTerminals()
        Dim terms(tb.Rows.Count - 1) As Terminal
        Dim rw As DataRow
        Dim i As Integer = 0
        For Each rw In tb.Rows
            'terms(i).Description = IIf(rw("Description") Is DBNull.Value, "", rw("Description"))
            terms(i).IPAddress = rw("IPAddress")
            terms(i).SourceSubCarpark = IIf(rw("SourceSubCarpark") Is DBNull.Value, -1, rw("SourceSubCarpark"))
            terms(i).DestinationSubCarpark = IIf(rw("DestinationSubCarpark") Is DBNull.Value, -1, rw("DestinationSubCarpark"))
            'terms(i).TerminalName = rw("TerminalName")
            terms(i).TerminalNo = rw("TerminalNo")
            terms(i).TerminalType = rw("TerminalType")
            terms(i).CarparkNo = rw("CarparkNo")
            terms(i).RateSetNo = IIf(rw("RateSetNo") Is DBNull.Value, -1, rw("RateSetNo"))
            terms(i).SeasonOnly = rw("SeasonOnly")
            i += 1
        Next
        Return terms
    End Function

    Public Function RetrieveAllTerminalRelations() As DataTable
        SyncLock connectionstr
            RetrieveAllTerminalRelations = executeCmd(connectionstr, "select * from TerminalRelation")
        End SyncLock
    End Function

    Public Function RetrieveAllTerminalTerminalRelationsEx() As TerminalRelation()
        Dim tb As DataTable = RetrieveAllTerminalRelations()
        Dim terms(tb.Rows.Count - 1) As TerminalRelation
        Dim rw As DataRow
        Dim i As Integer = 0
        For Each rw In tb.Rows
            terms(i).ID = rw("ID")
            terms(i).ChargeBy = rw("ChargeBy")
            terms(i).Duration = IIf(rw("Duration") Is DBNull.Value, -1, rw("Duration"))
            terms(i).FromTerminal = rw("FromTerminal")
            terms(i).ToTerminal = rw("ToTerminal")
            i += 1
        Next
        Return terms
    End Function

    Public Function RetrieveAllSubCarparks() As DataTable
        SyncLock connectionstr
            RetrieveAllSubCarparks = executeCmd(connectionstr, "select * from SubCarpark")
        End SyncLock
    End Function

    Public Function RetrieveAllCHUConfig() As DataTable
        SyncLock connectionstr
            RetrieveAllCHUConfig = executeCmd(connectionstr, "select * from CHUConfig")
        End SyncLock
    End Function

    Public Function RetrieveAllAntennaConfig() As DataTable
        SyncLock connectionstr
            RetrieveAllAntennaConfig = executeCmd(connectionstr, "select * from AntennaConfig")
        End SyncLock
    End Function

    Public Function RetrieveAllIUSeasonInfo() As DataTable
        SyncLock connectionstr
            RetrieveAllIUSeasonInfo = executeCmd(connectionstr, "select * from IUSeasonInfo")
        End SyncLock
    End Function

    Public Function RetrieveAllIUSeasonInfo_sentosa() As DataTable
        SyncLock connectionstr
            RetrieveAllIUSeasonInfo_sentosa = executeCmd(connectionstr, "select * from IUSeasonInfo where SeasonType=999 and Remark='AuthorizeIU'")
        End SyncLock
    End Function

    Public Function RetrieveAllCashCardSeasonInfo() As DataTable
        SyncLock connectionstr
            RetrieveAllCashCardSeasonInfo = executeCmd(connectionstr, "select * from CashCardSeasonInfo")
        End SyncLock
    End Function

    Public Function RetrieveAllProxSeasonInfo() As DataTable
        SyncLock connectionstr
            RetrieveAllProxSeasonInfo = executeCmd(connectionstr, "select * from ProxSeasonInfo")
        End SyncLock
    End Function

    Public Function RetrieveAllREMSeasonInfo() As DataTable
        SyncLock connectionstr
            RetrieveAllREMSeasonInfo = executeCmd(connectionstr, "select * from REMSeasonInfo")
        End SyncLock
    End Function

    Public Function RetrieveAllSeasonGroup() As DataTable
        SyncLock connectionstr
            RetrieveAllSeasonGroup = executeCmd(connectionstr, "select * from SeasonGroup")
        End SyncLock
    End Function

    Public Function RetrieveSeasonGroup(ByVal grpno As Integer, ByRef seasgrp As SeasonGroup) As Boolean
        Dim con As SqlConnection
        Dim ps() As SqlParameter = New SqlParameter() {New SqlParameter("GroupNo", grpno)}
        SyncLock connectionstr
            Dim thereader As SqlDataReader = executeCmd(connectionstr, "select * from SeasonGroup where GroupNo = @GroupNo", ps, con)
            If thereader Is Nothing Then
                RetrieveSeasonGroup = False
            Else
                If thereader.Read() = True Then
                    seasgrp.Address = IIf(thereader("Address") Is DBNull.Value, String.Empty, thereader("Address"))
                    seasgrp.CurrentCount = thereader("CurrentCount")
                    seasgrp.Fax = IIf(thereader("Fax") Is DBNull.Value, String.Empty, thereader("Fax"))
                    'seasgrp.GroupName = thereader("GroupName")
                    seasgrp.GroupNo = thereader("GroupNo")
                    seasgrp.Maximum = thereader("Maximum")
                    seasgrp.Phone = IIf(thereader("Phone") Is DBNull.Value, String.Empty, thereader("Phone"))
                    seasgrp.Remark = IIf(thereader("Remark") Is DBNull.Value, String.Empty, thereader("Remark"))
                    seasgrp.Threshold = thereader("Threshold")
                    thereader.Close()
                    con.Close()
                    RetrieveSeasonGroup = True
                Else
                    thereader.Close()
                    con.Close()
                    RetrieveSeasonGroup = False
                End If
            End If
        End SyncLock
    End Function

    Public Function RetrieveAccessViolationRecord(ByVal ticketno As String, ByRef av As AccessViolation) As Boolean
        Dim con As SqlConnection
        Dim ps() As SqlParameter = New SqlParameter() {New SqlParameter("TicketNo", ticketno)}
        SyncLock connectionstr
            Dim thereader As SqlDataReader = executeCmd(connectionstr, "select * from AccessViolation where TicketNo = @TicketNo", ps, con)
            If thereader Is Nothing Then
                RetrieveAccessViolationRecord = False
            Else
                If thereader.Read() = True Then
                    av.EntryTime = thereader("EntryTime")
                    av.ID = thereader("ID")
                    av.RelationID = thereader("RelationID")
                    av.TerminalNo = thereader("TerminalNo")
                    av.TicketNo = thereader("TicketNo")
                    thereader.Close()
                    con.Close()
                    RetrieveAccessViolationRecord = True
                Else
                    thereader.Close()
                    con.Close()
                    RetrieveAccessViolationRecord = False
                End If
            End If
        End SyncLock
    End Function


    Public Function RetrieveAllComplimentary() As DataTable
        SyncLock connectionstr
            RetrieveAllComplimentary = executeCmd(connectionstr, "select * from Complimentary")
        End SyncLock
    End Function

    Public Function RetrieveAllRedemption() As DataTable
        SyncLock connectionstr
            RetrieveAllRedemption = executeCmd(connectionstr, "select * from Redemption")
        End SyncLock
    End Function

    Public Function RetrieveAllValet() As DataTable
        SyncLock connectionstr
            RetrieveAllValet = executeCmd(connectionstr, "select * from Valet")
        End SyncLock
    End Function

    Public Function RetrieveAllIUSeasonPermits() As DataTable
        SyncLock connectionstr
            RetrieveAllIUSeasonPermits = executeCmd(connectionstr, "select * from IUSeasonPermits")
        End SyncLock
    End Function

    Public Function RetrieveAllCashCardSeasonPermits() As DataTable
        SyncLock connectionstr
            RetrieveAllCashCardSeasonPermits = executeCmd(connectionstr, "select * from CashCardSeasonPermits")
        End SyncLock
    End Function

    Public Function RetrieveAllProxSeasonPermits() As DataTable
        SyncLock connectionstr
            RetrieveAllProxSeasonPermits = executeCmd(connectionstr, "select * from ProxSeasonPermits")
        End SyncLock
    End Function

    Public Function RetrieveAllREMSeasonPermits() As DataTable
        SyncLock connectionstr
            RetrieveAllREMSeasonPermits = executeCmd(connectionstr, "select * from REMSeasonPermits")
        End SyncLock
    End Function

    Public Function RetrieveAllGroupSeasonPermits() As DataTable
        SyncLock connectionstr
            RetrieveAllGroupSeasonPermits = executeCmd(connectionstr, "select * from GroupSeasonPermits")
        End SyncLock
    End Function

    Public Function RetrieveAllCyclingBlockList() As DataTable
        SyncLock connectionstr
            RetrieveAllCyclingBlockList = executeCmd(connectionstr, "select * from CyclingBlockList")
        End SyncLock
    End Function

    Public Function RetrieveAllRateComposite() As DataTable
        SyncLock connectionstr
            RetrieveAllRateComposite = executeCmd(connectionstr, "select * from RateComposite")
        End SyncLock
    End Function

    Public Function RetrieveAllAdvancedBlockList() As DataTable
        SyncLock connectionstr
            RetrieveAllAdvancedBlockList = executeCmd(connectionstr, "select * from AdvancedBlockList")
        End SyncLock
    End Function

    Public Function RetrieveAllDiscreteBlockList() As DataTable
        SyncLock connectionstr
            RetrieveAllDiscreteBlockList = executeCmd(connectionstr, "select * from DiscreteBlockList")
        End SyncLock
    End Function

    Public Function RetrieveAllCyclingCapList() As DataTable
        SyncLock connectionstr
            RetrieveAllCyclingCapList = executeCmd(connectionstr, "select * from CyclingCapList")
        End SyncLock
    End Function

    Public Function RetrieveAllCrossOverList() As DataTable
        SyncLock connectionstr
            RetrieveAllCrossOverList = executeCmd(connectionstr, "select * from CrossOverList")
        End SyncLock
    End Function

    Public Function RetrieveAllCyclingRateList() As DataTable
        SyncLock connectionstr
            RetrieveAllCyclingRateList = executeCmd(connectionstr, "select * from CyclingRateList")
        End SyncLock
    End Function

    Public Function RetrieveAllRateSet() As DataTable
        SyncLock connectionstr
            RetrieveAllRateSet = executeCmd(connectionstr, "select * from RateSet")
        End SyncLock
    End Function

    Public Function RetrieveAllRateSetEx() As RateSet()
        Dim tb As DataTable = RetrieveAllRateSet()
        Dim rts(tb.Rows.Count - 1) As RateSet
        Dim rw As DataRow
        Dim i As Integer = 0
        For Each rw In tb.Rows
            rts(i).RateSetNo = rw("RateSetNo")
            rts(i).Car = rw("Car")
            rts(i).Container = rw("Container")
            rts(i).Lorry = rw("Lorry")
            rts(i).Motorcycle = rw("Motorcycle")
            rts(i).Taxi = rw("Taxi")
            rts(i).Reserve1 = rw("Reserve1")
            rts(i).Reserve2 = rw("Reserve2")
            i += 1
        Next
        Return rts
    End Function

    Public Function RetrieveAllFullStatus() As DataTable
        SyncLock connectionstr
            RetrieveAllFullStatus = executeCmd(connectionstr, "select * from FullStatus")
        End SyncLock
    End Function

    Public Function RetrieveAllHoliday() As DataTable
        SyncLock connectionstr
            RetrieveAllHoliday = executeCmd(connectionstr, "select * from Holiday")
        End SyncLock
    End Function

    Public Function RetrieveAllHolidayEx() As StrucHoliday()
        Dim tb As DataTable = RetrieveAllHoliday()
        Dim holi(tb.Rows.Count - 1) As StrucHoliday
        Dim rw As DataRow
        Dim i As Integer = 0
        For Each rw In tb.Rows
            holi(i).HolidayID = rw("HolidayID")
            holi(i).HDate = rw("HDate")
            i += 1
        Next
        Return holi
    End Function

    Public Function RetrieveIUSeasonPermit(ByVal iu As String, ByVal carpk As Short, ByVal subcarpk As Short, ByRef info As IUSeasonPermits) As Boolean
        Dim con As SqlConnection
        Dim ps() As SqlParameter = New SqlParameter() {New SqlParameter("IULabel", iu), _
            New SqlParameter("CarparkNo", carpk), New SqlParameter("SubCarparkNo", subcarpk)}
        SyncLock connectionstr
            Dim thereader As SqlDataReader = executeCmd(connectionstr, "select * from IUSeasonPermits where IULabel = @IULabel and CarparkNo = @CarparkNo and " & _
                    "SubCarparkNo = @SubCarparkNo", ps, con)
            If thereader Is Nothing Then
                RetrieveIUSeasonPermit = False
            Else
                If thereader.Read() = False Then
                    thereader.Close()
                    con.Close()
                    'Error
                    RetrieveIUSeasonPermit = False
                Else
                    info.IULabel = thereader("IULabel")
                    info.CarparkNo = thereader("CarparkNo")
                    info.Id = thereader("Id")
                    info.SubCarparkNo = thereader("SubCarparkNo")
                    thereader.Close()
                    con.Close()
                    RetrieveIUSeasonPermit = True
                End If
            End If
        End SyncLock
    End Function

    Public Function RetrieveCashCardSeasonPermit(ByVal ccno As String, ByVal carpk As Short, ByVal subcarpk As Short, ByRef info As CashCardSeasonPermits) As Boolean
        Dim con As SqlConnection
        Dim ps() As SqlParameter = New SqlParameter() {New SqlParameter("CashCardNo", ccno), _
            New SqlParameter("CarparkNo", carpk), New SqlParameter("SubCarparkNo", subcarpk)}
        SyncLock connectionstr
            Dim thereader As SqlDataReader = executeCmd(connectionstr, "select * from CashCardSeasonPermits where CashCardNo = @CashCardNo and CarparkNo = @CarparkNo and " & _
                    "SubCarparkNo = @SubCarparkNo", ps, con)
            If thereader Is Nothing Then
                RetrieveCashCardSeasonPermit = False
            Else
                If thereader.Read() = False Then
                    thereader.Close()
                    con.Close()
                    'Error
                    RetrieveCashCardSeasonPermit = False
                Else
                    info.CashCardNo = thereader("CashCardNo")
                    info.CarparkNo = thereader("CarparkNo")
                    info.Id = thereader("Id")
                    info.SubCarparkNo = thereader("SubCarparkNo")
                    thereader.Close()
                    con.Close()
                    RetrieveCashCardSeasonPermit = True
                End If
            End If
        End SyncLock
    End Function

    'Public Function RetrieveIUSeasonPermit(ByVal iu As String, ByRef sInfo() As IUSeasonPermits) As Boolean
    '    Dim con As sqlconnection
    '    Dim thereader As SqlDataReader = executeCmd(connectionstr, "select * from IUSeasonPermits where IULabel = ?", _
    '    New SqlParameter("IULabel", iu), con)
    '    If thereader Is Nothing Then
    '        Return False
    '    Else
    '        If thereader.Read() = False Then
    '            thereader.Close()
    '            con.Close()
    '            'Error
    '            Return False
    '        Else
    '            'Dim iuinfo As New IUSeasonInfo
    '            Dim i As Integer = 0
    '            Do
    '                ReDim Preserve sInfo(i)
    '                sInfo(i).IULabel = thereader("IULabel")
    '                sInfo(i).CarparkNo = thereader("CarparkNo")
    '                sInfo(i).Id = thereader("Id")
    '                sInfo(i).SubCarparkNo = thereader("SubCarparkNo")
    '                i += 1
    '            Loop Until thereader.Read = False
    '            thereader.Close()
    '            con.Close()
    '            Return True
    '        End If
    '    End If
    'End Function

    Public Function DeleteAllAddonOption() As Integer
        SyncLock connectionstr
            DeleteAllAddonOption = executeCmdNonQuery(connectionstr, "Delete from AddonOption")
        End SyncLock
    End Function


    Public Function DeleteAllRunningHourly() As Integer
        SyncLock connectionstr
            DeleteAllRunningHourly = executeCmdNonQuery(connectionstr, "Delete from RunningHourly")
        End SyncLock
    End Function

    Public Function DeleteAllRunningSeason() As Integer
        SyncLock connectionstr
            DeleteAllRunningSeason = executeCmdNonQuery(connectionstr, "Delete from RunningSeason")
        End SyncLock
    End Function

    Public Function DeleteAllCyclingBlockList() As Integer
        SyncLock connectionstr
            DeleteAllCyclingBlockList = executeCmdNonQuery(connectionstr, "Delete from CyclingBlockList")
        End SyncLock
    End Function

    Public Function DeleteAllDiscreteBlockList() As Integer
        SyncLock connectionstr
            DeleteAllDiscreteBlockList = executeCmdNonQuery(connectionstr, "Delete from DiscreteBlockList")
        End SyncLock
    End Function

    Public Function DeleteAllAdvancedBlockList() As Integer
        SyncLock connectionstr
            DeleteAllAdvancedBlockList = executeCmdNonQuery(connectionstr, "Delete from AdvancedBlockList")
        End SyncLock
    End Function

    Public Function DeleteAllRateComposite() As Integer
        SyncLock connectionstr
            DeleteAllRateComposite = executeCmdNonQuery(connectionstr, "Delete from RateComposite")
        End SyncLock
    End Function

    Public Function DeleteAllReceiptMessage() As Integer
        SyncLock connectionstr
            DeleteAllReceiptMessage = executeCmdNonQuery(connectionstr, "Delete from ReceiptMessage")
        End SyncLock
    End Function

    Public Function DeleteAllPGSInfo() As Integer
        SyncLock connectionstr
            DeleteAllPGSInfo = executeCmdNonQuery(connectionstr, "Delete from PGS")
        End SyncLock
    End Function

    Public Function DeleteAllPGSEnabled() As Integer
        SyncLock connectionstr
            DeleteAllPGSEnabled = executeCmdNonQuery(connectionstr, "Delete from PGSEnabled")
        End SyncLock
    End Function

    Public Function DeleteAllCCTVEnabled() As Integer
        SyncLock connectionstr
            DeleteAllCCTVEnabled = executeCmdNonQuery(connectionstr, "Delete from CCTVEnabled")
        End SyncLock
    End Function


    Public Function DeleteAllFullStatus() As Integer
        SyncLock connectionstr
            DeleteAllFullStatus = executeCmdNonQuery(connectionstr, "Delete from FullStatus")
        End SyncLock
    End Function

    Public Function DeleteAllCyclingCapList() As Integer
        SyncLock connectionstr
            DeleteAllCyclingCapList = executeCmdNonQuery(connectionstr, "Delete from CyclingCapList")
        End SyncLock
    End Function

    Public Function DeleteAllCrossOverList() As Integer
        SyncLock connectionstr
            DeleteAllCrossOverList = executeCmdNonQuery(connectionstr, "Delete from CrossOverList")
        End SyncLock
    End Function

    Public Function DeleteAllCyclingRateList() As Integer
        SyncLock connectionstr
            DeleteAllCyclingRateList = executeCmdNonQuery(connectionstr, "Delete from CyclingRateList")
        End SyncLock
    End Function

    Public Function DeleteAllGroupSeasonPermits() As Integer
        SyncLock connectionstr
            DeleteAllGroupSeasonPermits = executeCmdNonQuery(connectionstr, "Delete from GroupSeasonPermits")
        End SyncLock
    End Function

    Public Function DeleteAllREMSeasonPermits() As Integer
        SyncLock connectionstr
            DeleteAllREMSeasonPermits = executeCmdNonQuery(connectionstr, "Delete from REMSeasonPermits")
        End SyncLock
    End Function

    Public Function DeleteAllProxSeasonPermits() As Integer
        SyncLock connectionstr
            DeleteAllProxSeasonPermits = executeCmdNonQuery(connectionstr, "Delete from ProxSeasonPermits")
        End SyncLock
    End Function

    Public Function DeleteAllCashCardSeasonPermits() As Integer
        SyncLock connectionstr
            DeleteAllCashCardSeasonPermits = executeCmdNonQuery(connectionstr, "Delete from CashCardSeasonPermits")
        End SyncLock
    End Function

    Public Function DeleteAllIUSeasonPermits() As Integer
        SyncLock connectionstr
            DeleteAllIUSeasonPermits = executeCmdNonQuery(connectionstr, "Delete from IUSeasonPermits")
        End SyncLock
    End Function

    Public Function DeleteAllValet() As Integer
        SyncLock connectionstr
            DeleteAllValet = executeCmdNonQuery(connectionstr, "Delete from Valet")
        End SyncLock
    End Function

    Public Function DeleteAllRedemption() As Integer
        SyncLock connectionstr
            DeleteAllRedemption = executeCmdNonQuery(connectionstr, "Delete from Redemption")
        End SyncLock
    End Function

    Public Function DeleteAllComplimentary() As Integer
        SyncLock connectionstr
            DeleteAllComplimentary = executeCmdNonQuery(connectionstr, "Delete from Complimentary")
        End SyncLock
    End Function

    Public Function DeleteAllSeasonGroup() As Integer
        SyncLock connectionstr
            DeleteAllSeasonGroup = executeCmdNonQuery(connectionstr, "Delete from SeasonGroup")
        End SyncLock
    End Function

    Public Function DeleteAllREMSeasonInfo() As Integer
        SyncLock connectionstr
            DeleteAllREMSeasonInfo = executeCmdNonQuery(connectionstr, "Delete from REMSeasonInfo")
        End SyncLock
    End Function

    Public Function DeleteAllProxSeasonInfo() As Integer
        SyncLock connectionstr
            DeleteAllProxSeasonInfo = executeCmdNonQuery(connectionstr, "Delete from ProxSeasonInfo")
        End SyncLock
    End Function

    Public Function DeleteAllCashCardSeasonInfo() As Integer
        SyncLock connectionstr
            DeleteAllCashCardSeasonInfo = executeCmdNonQuery(connectionstr, "Delete from CashCardSeasonInfo")
        End SyncLock
    End Function

    Public Function DeleteAllIUSeasonInfo() As Integer
        SyncLock connectionstr
            DeleteAllIUSeasonInfo = executeCmdNonQuery(connectionstr, "Delete from IUSeasonInfo")
        End SyncLock
    End Function

    Public Function DeleteAllCHUConfig() As Integer
        SyncLock connectionstr
            DeleteAllCHUConfig = executeCmdNonQuery(connectionstr, "Delete from CHUConfig")
        End SyncLock
    End Function

    Public Function DeleteAllAntennaConfig() As Integer
        SyncLock connectionstr
            DeleteAllAntennaConfig = executeCmdNonQuery(connectionstr, "Delete from AntennaConfig")
        End SyncLock
    End Function

    Public Function DeleteAllSubCarparks() As Integer
        SyncLock connectionstr
            DeleteAllSubCarparks = executeCmdNonQuery(connectionstr, "Delete from SubCarpark")
        End SyncLock
    End Function

    Public Function DeleteAllTerminals() As Integer
        SyncLock connectionstr
            DeleteAllTerminals = executeCmdNonQuery(connectionstr, "Delete from Terminal")
        End SyncLock
    End Function

    Public Function DeleteAllTerminalRelations() As Integer
        SyncLock connectionstr
            DeleteAllTerminalRelations = executeCmdNonQuery(connectionstr, "Delete from TerminalRelation")
        End SyncLock
    End Function


    Public Function DeleteAllRateSets() As Integer
        SyncLock connectionstr
            DeleteAllRateSets = executeCmdNonQuery(connectionstr, "Delete from RateSet")
        End SyncLock
    End Function

    Public Function DeleteAllHoliday() As Integer
        SyncLock connectionstr
            DeleteAllHoliday = executeCmdNonQuery(connectionstr, "Delete from Holiday")
        End SyncLock
    End Function

    Public Function DeleteAllWorkingModes() As Integer
        SyncLock connectionstr
            DeleteAllWorkingModes = executeCmdNonQuery(connectionstr, "Delete from WorkingMode")
        End SyncLock
    End Function

    Public Function RetrieveIUSeasonInfo(ByVal iuLabel As String, ByRef sInfo As IUSeasonInfo) As Boolean
        Dim con As SqlConnection
        SyncLock connectionstr
            Dim thereader As SqlDataReader = executeCmd(connectionstr, "select * from IUSeasonInfo where IULabel = @IULabel", _
                    New SqlParameter("IULabel", iuLabel), con)
            If thereader Is Nothing Then
                RetrieveIUSeasonInfo = False
            Else
                If thereader.Read() = False Then
                    thereader.Close()
                    con.Close()
                    'Error
                    RetrieveIUSeasonInfo = False
                Else
                    'Dim iuinfo As New IUSeasonInfo
                    sInfo.IULabel = thereader("IULabel")
                    'sInfo.CashCardNo = thereader("CashCardNo")
                    'sInfo.Tenant = thereader("Tenant")
                    sInfo.ValidFrom = thereader("ValidFrom")
                    sInfo.ValidTo = thereader("ValidTo")
                    'sInfo.LicenceNo = thereader("LicenceNo")
                    'sInfo.Name = thereader("Name")
                    'sInfo.NRIC = thereader("NRIC")
                    'sInfo.Price = thereader("Price")
                    sInfo.SeasonType = IIf(thereader("SeasonType") Is DBNull.Value, -1, thereader("SeasonType"))
                    sInfo.IOCheck = thereader("IOCheck")
                    sInfo.GroupNo = IIf(thereader("GroupNo") Is DBNull.Value, -1, thereader("GroupNo"))
                    'sInfo.Remark = IIf(thereader("Remark") Is DBNull.Value, "", thereader("Remark"))
                    sInfo.Freeze = thereader("Freeze")
                    thereader.Close()
                    con.Close()
                    RetrieveIUSeasonInfo = True
                End If
            End If
        End SyncLock
    End Function

    Public Function RetrieveIUSeasonInfo(ByVal GrpNo As Integer, ByRef sInfo() As IUSeasonInfo) As Boolean
        Dim con As SqlConnection
        SyncLock connectionstr
            Dim thereader As SqlDataReader = executeCmd(connectionstr, "select * from IUSeasonInfo where GroupNo = @GroupNo", _
                    New SqlParameter("GroupNo", GrpNo), con, False)
            If thereader Is Nothing Then
                RetrieveIUSeasonInfo = False
            Else
                Dim i As Integer = 0
                While thereader.Read() = True
                    ReDim Preserve sInfo(i)
                    'Dim iuinfo As New IUSeasonInfo
                    sInfo(i).IULabel = thereader("IULabel")
                    'sInfo(i).CashCardNo = thereader("CashCardNo")
                    'sInfo(i).Tenant = thereader("Tenant")
                    sInfo(i).ValidFrom = thereader("ValidFrom")
                    sInfo(i).ValidTo = thereader("ValidTo")
                    'sInfo(i).LicenceNo = thereader("LicenceNo")
                    'sInfo(i).Name = thereader("Name")
                    'sInfo(i).NRIC = thereader("NRIC")
                    'sInfo(i).Price = thereader("Price")
                    sInfo(i).SeasonType = IIf(thereader("SeasonType") Is DBNull.Value, -1, thereader("SeasonType"))
                    sInfo(i).IOCheck = thereader("IOCheck")
                    sInfo(i).GroupNo = IIf(thereader("GroupNo") Is DBNull.Value, -1, thereader("GroupNo"))
                    'sInfo(i).Remark = IIf(thereader("Remark") Is DBNull.Value, "", thereader("Remark"))
                    sInfo(i).Freeze = thereader("Freeze")
                    i += 1
                End While
                thereader.Close()
                con.Close()
                If sInfo Is Nothing Then
                    RetrieveIUSeasonInfo = False
                Else
                    RetrieveIUSeasonInfo = True
                End If
            End If
        End SyncLock

    End Function

    Public Function RetrieveBlackListedInfo(ByVal ticket As String, ByRef sInfo As BlacklistedTicket) As Boolean
        Dim con As SqlConnection
        SyncLock connectionstr
            Dim thereader As SqlDataReader = executeCmd(connectionstr, "select * from BlacklistedTicket where TicketNo = @TicketNo", _
                    New SqlParameter("TicketNo", ticket), con)
            If thereader Is Nothing Then
                RetrieveBlackListedInfo = False
            Else
                If thereader.Read() = False Then
                    thereader.Close()
                    con.Close()
                    'Error
                    RetrieveBlackListedInfo = False
                Else
                    'Dim iuinfo As New IUSeasonInfo
                    'sInfo.IULabel = thereader("IULabel")
                    sInfo.TicketNo = thereader("TicketNo")
                    sInfo.EffectiveDate = thereader("EffectiveDate")
                    sInfo.ListedBy = thereader("ListedBy")
                    sInfo.ListedDate = thereader("ListedDate")
                    sInfo.Remark = IIf(thereader("Remark") Is DBNull.Value, "", thereader("Remark"))
                    sInfo.TicketType = thereader("TicketType")
                    thereader.Close()
                    con.Close()
                    RetrieveBlackListedInfo = True
                End If
            End If
        End SyncLock
    End Function

    Public Function RetrieveCashcardSeasonInfo(ByVal ccno As String, ByRef sInfo As CashCardSeasonInfo) As Boolean
        Dim con As SqlConnection

        SyncLock connectionstr
            Dim thereader As SqlDataReader = executeCmd(connectionstr, "select * from CashCardSeasonInfo where CashCardNo = @CashCardNo", _
                   New SqlParameter("CashCardNo", ccno), con)
            If thereader Is Nothing Then
                Return False
            Else
                If thereader.Read() = False Then
                    thereader.Close()
                    con.Close()
                    'Error
                    RetrieveCashcardSeasonInfo = False
                Else
                    'Dim iuinfo As New IUSeasonInfo
                    'sInfo.IULabel = thereader("IULabel")
                    sInfo.CashCardNo = thereader("CashCardNo")
                    'sInfo.Tenant = thereader("Tenant")
                    sInfo.ValidFrom = thereader("ValidFrom")
                    sInfo.ValidTo = thereader("ValidTo")
                    'sInfo.LicenceNo = thereader("LicenceNo")
                    'sInfo.Name = thereader("Name")
                    'sInfo.NRIC = thereader("NRIC")
                    'sInfo.Price = thereader("Price")
                    sInfo.SeasonType = IIf(thereader("SeasonType") Is DBNull.Value, -1, thereader("SeasonType"))
                    sInfo.IOCheck = thereader("IOCheck")
                    sInfo.GroupNo = IIf(thereader("GroupNo") Is DBNull.Value, -1, thereader("GroupNo"))
                    'sInfo.Remark = IIf(thereader("Remark") Is DBNull.Value, "", thereader("Remark"))
                    sInfo.Freeze = thereader("Freeze")
                    thereader.Close()
                    con.Close()
                    RetrieveCashcardSeasonInfo = True
                End If
            End If
        End SyncLock

    End Function

    Public Function RetrieveCashcardSeasonInfo(ByVal GrpNo As Integer, ByRef sInfo() As CashCardSeasonInfo) As Boolean
        Dim con As SqlConnection

        SyncLock connectionstr
            Dim thereader As SqlDataReader = executeCmd(connectionstr, "select * from CashCardSeasonInfo where GroupNo = @GroupNo", _
                    New SqlParameter("GroupNo", GrpNo), con, False)
            If thereader Is Nothing Then
                RetrieveCashcardSeasonInfo = False
            Else
                Dim i As Integer = 0
                While thereader.Read() = True
                    ReDim Preserve sInfo(i)
                    'Dim iuinfo As New IUSeasonInfo
                    'sInfo(i).IULabel = thereader("IULabel")
                    sInfo(i).CashCardNo = thereader("CashCardNo")
                    'sInfo(i).Tenant = thereader("Tenant")
                    sInfo(i).ValidFrom = thereader("ValidFrom")
                    sInfo(i).ValidTo = thereader("ValidTo")
                    'sInfo(i).LicenceNo = thereader("LicenceNo")
                    'sInfo(i).Name = thereader("Name")
                    'sInfo(i).NRIC = thereader("NRIC")
                    'sInfo(i).Price = thereader("Price")
                    sInfo(i).SeasonType = IIf(thereader("SeasonType") Is DBNull.Value, -1, thereader("SeasonType"))
                    sInfo(i).IOCheck = thereader("IOCheck")
                    sInfo(i).GroupNo = IIf(thereader("GroupNo") Is DBNull.Value, -1, thereader("GroupNo"))
                    'sInfo(i).Remark = IIf(thereader("Remark") Is DBNull.Value, "", thereader("Remark"))
                    sInfo(i).Freeze = thereader("Freeze")
                    i += 1
                End While
                thereader.Close()
                con.Close()
                If sInfo Is Nothing Then
                    RetrieveCashcardSeasonInfo = False
                Else
                    RetrieveCashcardSeasonInfo = True
                End If
            End If
        End SyncLock

    End Function

    Public Function GetLocalRateSet(ByVal StNo As Short, ByRef rset As RateSet) As Boolean
        Dim con As SqlConnection
        SyncLock connectionstr
            Dim thereader As SqlDataReader = executeCmd(connectionstr, "select * from RateSet where RateSetNo = @RateSetNo", _
                    New SqlParameter("RateSetNo", StNo), con)
            If thereader Is Nothing Then
                GetLocalRateSet = False
            Else
                If thereader.Read() = False Then
                    thereader.Close()
                    con.Close()
                    'Error
                    GetLocalRateSet = False
                Else
                    rset.RateSetNo = thereader("RateSetNo")
                    rset.Car = thereader("Car")
                    rset.Container = thereader("Container")
                    rset.Lorry = thereader("Lorry")
                    rset.Motorcycle = thereader("Motorcycle")
                    rset.Taxi = thereader("Taxi")
                    rset.Reserve1 = thereader("Reserve1")
                    rset.Reserve2 = thereader("Reserve2")
                    thereader.Close()
                    con.Close()
                    GetLocalRateSet = True
                End If
            End If
        End SyncLock
    End Function

    Public Function GetRedemptionRecord(ByVal ticketno As String, ByRef dt As Redemption) As Boolean
        Dim con As SqlConnection
        SyncLock connectionstr
            Dim thereader As SqlDataReader = executeCmd(connectionstr, "select * from Redemption where RedemptionNo = @RedemptionNo", _
                    New SqlParameter("RedemptionNo", ticketno), con)
            If thereader Is Nothing Then
                GetRedemptionRecord = False
            Else
                If thereader.Read() = False Then
                    thereader.Close()
                    con.Close()
                    'Error
                    GetRedemptionRecord = False
                Else
                    dt.RedemptionNo = thereader("RedemptionNo")
                    dt.RedemptionType = CType(thereader("RedemptionType"), SPECIALOPTION)
                    dt.ValidFrom = thereader("ValidFrom")
                    dt.ValidTo = thereader("ValidTo")
                    dt.Value = thereader("Value")
                    dt.Valuetype = CType(thereader("Valuetype"), REDEMPTIONOPTION)
                    thereader.Close()
                    con.Close()
                    GetRedemptionRecord = True
                End If
            End If
        End SyncLock
    End Function

    Public Function DeleteRunningHourlyRecord(ByVal rh As Integer) As Integer
        Dim para As New SqlParameter("ID", rh)
        SyncLock connectionstr
            DeleteRunningHourlyRecord = executeCmd(connectionstr, "delete from RunningHourly where ID = @ID", New SqlParameter() {para})
        End SyncLock
    End Function

    Public Function DeleteRunningHourlyRecord(ByVal rh As String, ByVal term As Short) As Integer
        Dim para1 As New SqlParameter("HourlyNo", rh)
        Dim para2 As New SqlParameter("TerminalNo", term)
        SyncLock connectionstr
            DeleteRunningHourlyRecord = executeCmd(connectionstr, "delete from RunningHourly where HourlyNo = @HourlyNo and TerminalNo = @TerminalNo", New SqlParameter() {para1, para2})
        End SyncLock
    End Function

    Public Function DeleteExpRunningHourlyRecord(ByVal dt As Date) As Integer
        Dim para As New SqlParameter("EntryTime", dt)
        SyncLock connectionstr
            DeleteExpRunningHourlyRecord = executeCmd(connectionstr, "delete from RunningHourly where EntryTime <= @EntryTime", New SqlParameter() {para})
        End SyncLock

    End Function

    Public Function DeleteRunningSeasonRecord(ByVal rs As Integer) As Integer
        Dim para As New SqlParameter("ID", rs)
        SyncLock connectionstr
            DeleteRunningSeasonRecord = executeCmd(connectionstr, "delete from RunningSeason where ID = @ID", New SqlParameter() {para})
        End SyncLock
    End Function

    Public Function DeleteRunningSeasonRecord(ByVal rs As String, ByVal term As Short) As Integer
        Dim para1 As New SqlParameter("SeasonNo", rs)
        Dim para2 As New SqlParameter("TerminalNo", term)
        SyncLock connectionstr
            DeleteRunningSeasonRecord = executeCmd(connectionstr, "delete from RunningSeason where SeasonNo = @SeasonNo and TerminalNo = @TerminalNo", New SqlParameter() {para1, para2})
        End SyncLock
    End Function

    Public Function DeleteExpRunningSeasonRecord(ByVal dt As Date) As Integer
        Dim para As New SqlParameter("EntryTime", dt)
        SyncLock connectionstr
            DeleteExpRunningSeasonRecord = executeCmd(connectionstr, "delete from RunningSeason where EntryTime <= @EntryTime", New SqlParameter() {para})
        End SyncLock
    End Function

    Public Function DeleteAccessViolationRecord(ByVal av As String) As Integer
        Dim para As New SqlParameter("TicketNo", av)
        SyncLock connectionstr
            DeleteAccessViolationRecord = executeCmd(connectionstr, "delete from AccessViolation where TicketNo = @TicketNo", New SqlParameter() {para})
        End SyncLock
    End Function

    Public Function DeleteAccessViolationdebitRecord(ByVal avd As String) As Integer
        Dim para As New SqlParameter("TicketNo", avd)
        SyncLock connectionstr
            DeleteAccessViolationdebitRecord = executeCmd(connectionstr, "delete from AccessViolationdebit where TicketNo = @TicketNo", New SqlParameter() {para})
        End SyncLock
    End Function

    Public Function DeleteHolidayRecord(ByVal hid As Integer) As Integer
        Dim para As New SqlParameter("HolidayID", hid)
        SyncLock connectionstr
            DeleteHolidayRecord = executeCmd(connectionstr, "delete from Holiday where HolidayID = @HolidayID", New SqlParameter() {para})
        End SyncLock
    End Function

    Public Function DeleteRateSetRecord(ByVal setid As Integer) As Integer
        Dim para As New SqlParameter("RateSetNo", setid)
        SyncLock connectionstr
            Return executeCmd(connectionstr, "delete from RateSet where RateSetNo = @RateSetNo", New SqlParameter() {para})
        End SyncLock
    End Function

    Public Function DeleteIUSeasonInfo(ByVal iu As String) As Integer
        Dim para As New SqlParameter("IULabel", iu)
        SyncLock connectionstr
            DeleteIUSeasonInfo = executeCmd(connectionstr, "delete from IUSeasonInfo where IULabel = @IULabel", New SqlParameter() {para})
        End SyncLock
    End Function

    Public Function DeleteIUSeasonPermit(ByVal id As Integer) As Integer
        Dim para As New SqlParameter("Id", id)
        SyncLock connectionstr
            DeleteIUSeasonPermit = executeCmd(connectionstr, "delete from IUSeasonPermits where id = @Id", New SqlParameter() {para})
        End SyncLock
    End Function

    Public Function DeleteCashcardSeasonInfo(ByVal ccard As String) As Integer
        Dim para As New SqlParameter("CashCardNo", ccard)
        SyncLock connectionstr
            DeleteCashcardSeasonInfo = executeCmd(connectionstr, "delete from CashCardSeasonInfo where CashCardNo = @CashCardNo", New SqlParameter() {para})
        End SyncLock
    End Function

    Public Function DeleteCashcardSeasonPermit(ByVal id As Integer) As Integer
        Dim para As New SqlParameter("Id", id)
        SyncLock connectionstr
            DeleteCashcardSeasonPermit = executeCmd(connectionstr, "delete from CashCardSeasonPermits where id = @Id", New SqlParameter() {para})
        End SyncLock
    End Function

    Public Function DeleteValetInfo(ByVal ccard As String) As Integer
        Dim para As New SqlParameter("CashCardNo", ccard)
        SyncLock connectionstr
            DeleteValetInfo = executeCmd(connectionstr, "delete from Valet where CashCardNo = @CashCardNo", New SqlParameter() {para})
        End SyncLock
    End Function

    Public Function DeleteProxSeasonInfo(ByVal prox As String) As Integer
        Dim para As New SqlParameter("ProximityNo", prox)
        SyncLock connectionstr
            DeleteProxSeasonInfo = executeCmd(connectionstr, "delete from ProxSeasonInfo where ProximityNo = @ProximityNo", New SqlParameter() {para})
        End SyncLock
    End Function

    Public Function DeleteProxSeasonPermit(ByVal id As Integer) As Integer
        Dim para As New SqlParameter("Id", id)
        SyncLock connectionstr
            DeleteProxSeasonPermit = executeCmd(connectionstr, "delete from ProxSeasonPermits where id = @Id", New SqlParameter() {para})
        End SyncLock
    End Function

    Public Function DeleteREMSeason(ByVal prox As String) As Integer
        Dim para As New SqlParameter("REMNo", prox)
        SyncLock connectionstr
            DeleteREMSeason = executeCmd(connectionstr, "delete from REMSeasonInfo where REMNo = @REMNo", New SqlParameter() {para})
        End SyncLock
    End Function

    Public Function DeleteRemSeasonPermit(ByVal id As Integer) As Integer
        Dim para As New SqlParameter("Id", id)
        SyncLock connectionstr
            DeleteRemSeasonPermit = executeCmd(connectionstr, "delete from REMSeasonPermits where id = @Id", New SqlParameter() {para})
        End SyncLock
    End Function

    Public Function DeleteSeasonGroup(ByVal prox As String) As Integer
        Dim para As New SqlParameter("GroupNo", prox)
        SyncLock connectionstr
            DeleteSeasonGroup = executeCmd(connectionstr, "delete from SeasonGroup where GroupNo = @GroupNo", New SqlParameter() {para})
        End SyncLock
    End Function

    Public Function DeleteComplimentary(ByVal compli As String) As Integer
        Dim para As New SqlParameter("ComplimentaryNo", compli)
        SyncLock connectionstr
            DeleteComplimentary = executeCmd(connectionstr, "delete from Complimentary where ComplimentaryNo = @ComplimentaryNo", New SqlParameter() {para})
        End SyncLock
    End Function

    Public Function DeleteRedemption(ByVal redem As String) As Integer
        Dim para As New SqlParameter("RedemptionNo", redem)
        SyncLock connectionstr
            DeleteRedemption = executeCmd(connectionstr, "delete from Redemption where RedemptionNo = @RedemptionNo", New SqlParameter() {para})
        End SyncLock
    End Function

    Public Function DeleteSeasonGroupPermit(ByVal sgp As String) As Integer
        Dim para As New SqlParameter("Id", sgp)
        SyncLock connectionstr
            DeleteSeasonGroupPermit = executeCmd(connectionstr, "delete from GroupSeasonPermits where Id = @Id", New SqlParameter() {para})
        End SyncLock
    End Function

    Public Function DeleteAntennaInfo(ByVal ant As String) As Integer
        Dim para As New SqlParameter("AntNo", ant)
        SyncLock connectionstr
            DeleteAntennaInfo = executeCmd(connectionstr, "delete from AntennaConfig where AntNo = @AntNo", New SqlParameter() {para})
        End SyncLock
    End Function

    Public Function DeleteBlackListTicket(ByVal bltNo As String) As Integer
        Dim para As New SqlParameter("TicketNo", bltNo)
        SyncLock connectionstr
            DeleteBlackListTicket = executeCmd(connectionstr, "delete from BlacklistedTicket where TicketNo = @TicketNo", New SqlParameter() {para})
        End SyncLock
    End Function
    'Public Function RetrieveIUSeason(ByVal iu As String, ByRef iuinfo As IUSeasonInfo) As Boolean

    'End Function

    Public Function IsCashCardSeason() As Boolean
        Return True
    End Function

    Public Function IsProxiSeason() As Boolean
        Return True
    End Function

    Public Function IsREMSeason() As Boolean
        Return True
    End Function

    Public Function GetReceiptMessage(ByRef recptmsg As ReceiptMsg) As Boolean
        Dim tb As DataTable
        SyncLock connectionstr
            tb = executeCmd(connectionstr, "select * from ReceiptMessage")
        End SyncLock

        If tb.Rows.Count <> 1 Then
            Return False
        Else
            recptmsg.ReceiptHeader1 = IIf(tb.Rows(0)(0) Is DBNull.Value, String.Empty, tb.Rows(0)(0))
            recptmsg.ReceiptHeader2 = IIf(tb.Rows(0)(1) Is DBNull.Value, String.Empty, tb.Rows(0)(1))
            recptmsg.ReceiptFooter1 = IIf(tb.Rows(0)(2) Is DBNull.Value, String.Empty, tb.Rows(0)(2))
            recptmsg.ReceiptFooter2 = IIf(tb.Rows(0)(3) Is DBNull.Value, String.Empty, tb.Rows(0)(3))
            recptmsg.IncludeGST = CType(tb.Rows(0)(4), Boolean)
            Return True
        End If
    End Function

    Public Function GetMainConfigInfo(ByVal carpkNo As Short, ByRef cinfo As MainConfig) As Boolean
        Dim result As Boolean = False
        Dim con As SqlConnection
        SyncLock connectionstr
            Dim thereader As SqlDataReader = executeCmd(connectionstr, "select * from MainConfig where CarparkNo = @CarparkNo", _
                    New SqlParameter("CarparkNo", carpkNo), con)
            If thereader Is Nothing Then
                result = False
            Else
                If thereader.Read() = False Then
                    thereader.Close()
                    con.Close()
                    'Error
                    result = False
                Else
                    cinfo.CarparkNo = thereader("CarparkNo")
                    'cinfo.CarparkName = thereader("CarparkName")
                    cinfo.GST = CType(thereader("GST"), Single)
                    cinfo.GSTNo = thereader("GSTNo")
                    'cinfo.MinUnit = CType(thereader("MinUnit"), Single)
                    'cinfo.StartIOCheck = thereader("StartIOCheck")
                    'cinfo.StartOperation = thereader("StartOperation")
                    thereader.Close()
                    con.Close()
                    result = True
                End If
            End If
        End SyncLock
        Return result
    End Function

    Public Function GetTerminalInfo(ByVal TermIP As String, ByRef tinfo As Terminal) As Boolean
        Dim con As SqlConnection
        Dim result As Boolean = False
        SyncLock connectionstr
            Dim thereader As SqlDataReader = executeCmd(connectionstr, "select * from Terminal where IPAddress = @IPAddress", _
                            New SqlParameter("@IPAddress", TermIP), con)
            If thereader Is Nothing Then
                result = False
            Else
                If thereader.Read() = False Then
                    thereader.Close()
                    con.Close()
                    'Error
                    result = False
                Else
                    tinfo.CarparkNo = thereader("CarparkNo")
                    'tinfo.Description = IIf(thereader("Description") Is DBNull.Value, "", thereader("Description"))
                    tinfo.IPAddress = TermIP
                    tinfo.DestinationSubCarpark = IIf(thereader("DestinationSubCarpark") Is DBNull.Value, -1, thereader("DestinationSubCarpark"))
                    tinfo.SourceSubCarpark = IIf(thereader("SourceSubCarpark") Is DBNull.Value, -1, thereader("SourceSubCarpark"))
                    'tinfo.TerminalName = thereader("TerminalName")
                    tinfo.TerminalNo = thereader("TerminalNo")
                    tinfo.TerminalType = thereader("TerminalType")
                    tinfo.RateSetNo = thereader("RateSetNo")
                    tinfo.SeasonOnly = thereader("SeasonOnly")
                    tinfo.TerminalName = thereader("TerminalName")  'add on 25/06/08
                    thereader.Close()
                    con.Close()
                    result = True
                End If
            End If
        End SyncLock

        Return result
    End Function

    Public Function GetWorkingModeForTerminal(ByVal carparkno As Short, ByVal subcarparkno As Short, ByRef winfo() As WorkingMode) As Boolean
        'Return executeCmd(connectionstr, "select * from WorkingMode order by WorkingMode")
        Dim con As SqlConnection
        Dim para1 As New SqlParameter("@SubCarparkNo", subcarparkno)
        Dim para2 As New SqlParameter("@CarparkNo", carparkno)

        SyncLock connectionstr
            Dim thereader As SqlDataReader = executeCmd(connectionstr, "select * from WorkingMode where SubCarparkNo = @SubCarparkNo and CarparkNo = @CarparkNo order by WorkingMode", _
                            New SqlParameter() {para1, para2}, con, False)
            If thereader Is Nothing Then
                GetWorkingModeForTerminal = False
            Else
                Dim i As Integer = 0
                While thereader.Read() = True
                    ReDim Preserve winfo(i)
                    winfo(i).CarparkNo = thereader("CarparkNo")
                    winfo(i).Description = IIf(thereader("Description") Is DBNull.Value, "", thereader("Description"))
                    winfo(i).ID = thereader("ID")
                    winfo(i).SubCarparkNo = thereader("SubCarparkNo")
                    winfo(i).mode = CType(thereader("WorkingMode"), WORKINGMODEOPTION)
                    winfo(i).ParkingType = CType(thereader("ParkingType"), PARKINGTYPENUM)
                    i += 1
                End While
                thereader.Close()

                con.Close()
                If winfo Is Nothing Then
                    GetWorkingModeForTerminal = False
                Else
                    GetWorkingModeForTerminal = True
                End If
            End If
        End SyncLock
    End Function


    Public Function GetAddonOptionInfo(ByRef tinfo As AddonOption) As Boolean
        Dim con As SqlConnection
        SyncLock connectionstr
            Dim thetb As DataTable = executeCmd(connectionstr, "select * from AddonOption")
            If thetb Is Nothing Then
                GetAddonOptionInfo = False
            Else
                If thetb.Rows.Count = 0 Then
                    GetAddonOptionInfo = False
                Else
                    tinfo.CashcardConfirm = thetb.Rows(0)("CashcardConfirm")
                    tinfo.LEDEnabled = thetb.Rows(0)("LEDEnabled")
                    tinfo.PowerFailAlarm = thetb.Rows(0)("PowerFailAlarm")
                    tinfo.SeasonAllowedWhenFull = thetb.Rows(0)("SeasonAllowedWhenFull")
                    tinfo.ShutterEnabled = thetb.Rows(0)("ShutterEnabled")
                    tinfo.TailGateSensor = thetb.Rows(0)("TailGateSensor")
                    tinfo.SubCPWithinSubCP = thetb.Rows(0)("SubCPWithinSubCP")
                    GetAddonOptionInfo = True
                End If
            End If
        End SyncLock
    End Function

    'Public Function GetRunningHourly(ByVal rh As String, ByRef tinfo As RunningHourly) As Boolean
    '    Dim con As sqlconnection
    '    Dim thereader As SqlDataReader = executeCmd(RunningStr, "select * from RunningHourly where HourlyNo = ?", _
    '            New SqlParameter("HourlyNo", rh), con)
    '    If thereader Is Nothing Then
    '        Return False
    '    Else
    '        If thereader.Read() = False Then
    '            thereader.Close()
    '            con.Close()
    '            'Error
    '        Else
    '            tinfo.HourlyNo = thereader("HourlyNo")
    '            tinfo.EntryTime = thereader("EntryTime")
    '            tinfo.HourlyType = thereader("HourlyType")
    '            tinfo.TerminalNo = thereader("TerminalNo")
    '            tinfo.Status = thereader("Status")
    '            thereader.Close()
    '            con.Close()
    '            Return True
    '        End If
    '    End If
    'End Function

    Public Function GetRunningHourly(ByVal rh As String, ByRef tinfo() As RunningHourly) As Boolean
        Dim con As SqlConnection
        SyncLock connectionstr
            Dim thereader As SqlDataReader = executeCmd(connectionstr, "select * from RunningHourly where HourlyNo = @HourlyNo", _
                            New SqlParameter("HourlyNo", rh), con, False)
            If thereader Is Nothing Then
                GetRunningHourly = False
            Else
                Dim i As Integer = 0
                While thereader.Read() = True
                    ReDim Preserve tinfo(i)
                    tinfo(i) = New RunningHourly
                    tinfo(i).ID = thereader("Id")
                    tinfo(i).HourlyNo = thereader("HourlyNo")
                    tinfo(i).EntryTime = thereader("EntryTime")
                    tinfo(i).HourlyType = thereader("HourlyType")
                    tinfo(i).TerminalNo = thereader("TerminalNo")
                    tinfo(i).Status = thereader("Status")
                    i += 1
                End While
                thereader.Close()

                con.Close()
                If tinfo Is Nothing Then
                    GetRunningHourly = False
                Else
                    GetRunningHourly = True
                End If
            End If
        End SyncLock
    End Function

    'Public Function GetRunningSeason(ByVal rs As String, ByRef tinfo As RunningSeason) As Boolean
    '    Dim con As sqlconnection
    '    Dim thereader As SqlDataReader = executeCmd(RunningStr, "select * from RunningSeason where SeasonNo = ?", _
    '            New SqlParameter("SeasonNo", rs), con)
    '    If thereader Is Nothing Then
    '        Return False
    '    Else
    '        If thereader.Read() = False Then
    '            thereader.Close()
    '            con.Close()
    '            'Error
    '        Else
    '            tinfo.SeasonNo = thereader("SeasonNo")
    '            tinfo.EntryTime = thereader("EntryTime")
    '            tinfo.SeasonType = thereader("SeasonType")
    '            tinfo.TerminalNo = thereader("TerminalNo")
    '            tinfo.Status = thereader("Status")
    '            tinfo.SeasonOption = thereader("SeasonOption")
    '            thereader.Close()
    '            con.Close()
    '            Return True
    '        End If
    '    End If
    'End Function

    Public Function GetRunningSeason(ByVal rs As String, ByRef tinfo() As RunningSeason) As Boolean
        Dim con As SqlConnection
        SyncLock connectionstr
            Dim thereader As SqlDataReader = executeCmd(connectionstr, "select * from RunningSeason where SeasonNo = @SeasonNo", _
                            New SqlParameter("SeasonNo", rs), con, False)
            If thereader Is Nothing Then
                GetRunningSeason = False
            Else
                Dim i As Integer = 0
                While thereader.Read() = True
                    ReDim Preserve tinfo(i)
                    tinfo(i) = New RunningSeason
                    tinfo(i).ID = thereader("Id")
                    tinfo(i).SeasonNo = thereader("SeasonNo")
                    tinfo(i).EntryTime = thereader("EntryTime")
                    tinfo(i).SeasonType = thereader("SeasonType")
                    tinfo(i).TerminalNo = thereader("TerminalNo")
                    tinfo(i).Status = thereader("Status")
                    tinfo(i).SeasonOption = thereader("SeasonOption")
                    i += 1
                End While
                thereader.Close()

                con.Close()
                If tinfo Is Nothing Then
                    GetRunningSeason = False
                Else
                    GetRunningSeason = True
                End If
            End If
        End SyncLock
    End Function

    Public Function GetPGSInfo(ByVal terminalNo As Short, ByRef pgs As PGSInfo) As Boolean
        Dim con As SqlConnection
        SyncLock connectionstr
            Dim thereader As SqlDataReader = executeCmd(connectionstr, "select * from PGS where TerminalNo = @TerminalNo", _
                            New SqlParameter("TerminalNo", terminalNo), con)
            If thereader Is Nothing Then
                GetPGSInfo = False
            Else
                If thereader.Read() = False Then
                    thereader.Close()
                    con.Close()
                    'Error
                    GetPGSInfo = False
                Else
                    pgs.Connected = thereader("Connected")
                    pgs.InformedBy = thereader("InformedBy")
                    pgs.TerminalNo = thereader("TerminalNo")
                    thereader.Close()
                    con.Close()
                    GetPGSInfo = True
                End If
            End If
        End SyncLock
    End Function

    Public Function IsTerminalBlockedByFull(ByVal thetermno As Short) As Boolean
        Dim con As SqlConnection
        Dim para1 As New SqlParameter("TerminalNo", thetermno)
        SyncLock connectionstr
            Dim thereader As SqlDataReader = executeCmd(connectionstr, "select FullSign from FullStatus where TerminalNo = @TerminalNo", _
                            New SqlParameter() {para1}, con)
            If thereader Is Nothing Then
                IsTerminalBlockedByFull = False
            Else
                If thereader.Read() = False Then
                    thereader.Close()
                    con.Close()
                    'Error
                    IsTerminalBlockedByFull = False
                Else
                    Dim result As Boolean = thereader.GetBoolean(0)
                    thereader.Close()
                    con.Close()
                    IsTerminalBlockedByFull = result
                End If
            End If
        End SyncLock
    End Function

    Public Function ResetFullStatus() As Integer
        DeleteAllFullStatus()
        Dim term As DataTable = RetrieveAllTerminals()
        If term.Rows.Count <> 0 Then
            For Each rw As DataRow In term.Rows
                If rw("TerminalType") = CType(TERMINALOPTION.ENTRYTERM, Short) Then
                    AddFullStatusRecord(rw("TerminalNo"), False)
                End If
            Next
        End If
        Return term.Rows.Count
    End Function

    Public Function HasSemiEps() As Boolean
        SyncLock connectionstr
            If executeCmdEx(connectionstr, "select workingmode from workingmode where workingmode = @WorkingMode", _
                        New SqlParameter() {New SqlParameter("WorkingMode", CType(WORKINGMODEOPTION.SEMIEPS, Short))}) Is Nothing Then
                HasSemiEps = False
            Else
                HasSemiEps = True
            End If
        End SyncLock
    End Function

    Public Function HasFullEps() As Boolean
        SyncLock connectionstr
            If executeCmdEx(connectionstr, "select workingmode from workingmode where workingmode = @WorkingMode", _
                        New SqlParameter() {New SqlParameter("WorkingMode", CType(WORKINGMODEOPTION.FULLEPS, Short))}) Is Nothing Then
                HasFullEps = False
            Else
                HasFullEps = True
            End If
        End SyncLock
    End Function

    'Public Function HasREMSystem() As Boolean
    '    SyncLock connectionstr
    '        If executeCmdEx(connectionstr, "select workingmode from workingmode where workingmode = ?", _
    '                    New SqlParameter() {New SqlParameter("1", CType(WORKINGMODEOPTION.REMTICKET, Short))}) Is Nothing Then
    '            HasREMSystem = False
    '        Else
    '            HasREMSystem = True
    '        End If
    '    End SyncLock
    'End Function

    Public Function HasCashcardSystem() As Boolean
        SyncLock connectionstr
            If executeCmdEx(connectionstr, "select workingmode from workingmode where workingmode = @WorkingMode", _
                        New SqlParameter() {New SqlParameter("WorkingMode", CType(WORKINGMODEOPTION.CASHCARD, Short))}) Is Nothing Then
                HasCashcardSystem = False
            Else
                HasCashcardSystem = True
            End If
        End SyncLock
    End Function

    Public Function HasCHUWhenEntry() As Boolean
        SyncLock connectionstr
            If executeCmdScalar(connectionstr, "select count(*) from AntennaConfig where CHUID <> null") = 0 Then
                HasCHUWhenEntry = False
            Else
                HasCHUWhenEntry = True
            End If
        End SyncLock
    End Function

    Public Function GetFullStatus(ByVal termno As Short) As Boolean
        Dim para As New SqlParameter("TerminalNo", termno)
        Dim sqlstr As String = "select FullSign from FullStatus where TerminalNo = @TerminalNo"
        SyncLock connectionstr
            Dim obj As Object = executeCmdEx(connectionstr, sqlstr, New SqlParameter() {para})
            If obj Is Nothing Then
                GetFullStatus = False
            Else
                GetFullStatus = CType(obj, Boolean)
            End If
        End SyncLock
    End Function

    Public Function FindRelatedFromTerminal(ByVal termno As Short) As TerminalRelation()
        Dim para1 As New SqlParameter("ToTerminal", termno)
        Dim conn As SqlConnection
        SyncLock connectionstr
            Dim sqlstrTerm As String = "select * from TerminalRelation where ToTerminal = @ToTerminal"
            Dim thereader As SqlDataReader = executeCmd(connectionstr, sqlstrTerm, New SqlParameter() {para1}, conn, False)
            If thereader Is Nothing Then
                FindRelatedFromTerminal = Nothing
            Else
                Dim i As Integer = 0
                Dim ret() As TerminalRelation
                While thereader.Read() = True
                    ReDim Preserve ret(i)
                    ret(i).ID = thereader("ID")
                    ret(i).FromTerminal = thereader("FromTerminal")
                    ret(i).ToTerminal = thereader("ToTerminal")
                    ret(i).Duration = thereader("Duration")
                    ret(i).ChargeBy = thereader("ChargeBy")
                    i += 1
                End While
                thereader.Close()

                conn.Close()
                FindRelatedFromTerminal = ret
            End If
        End SyncLock
    End Function

    Public Function FindRelatedToTerminal(ByVal termno As Short) As TerminalRelation()
        Dim para1 As New SqlParameter("FromTerminal", termno)
        Dim conn As SqlConnection
        SyncLock connectionstr
            Dim sqlstrTerm As String = "select * from TerminalRelation where FromTerminal = @FromTerminal"
            Dim thereader As SqlDataReader = executeCmd(connectionstr, sqlstrTerm, New SqlParameter() {para1}, conn, False)
            If thereader Is Nothing Then
                FindRelatedToTerminal = Nothing
            Else
                Dim i As Integer = 0
                Dim ret() As TerminalRelation
                While thereader.Read() = True
                    ReDim Preserve ret(i)
                    ret(i).ID = thereader("ID")
                    ret(i).FromTerminal = thereader("FromTerminal")
                    ret(i).ToTerminal = thereader("ToTerminal")
                    ret(i).Duration = thereader("Duration")
                    ret(i).ChargeBy = thereader("ChargeBy")
                    i += 1
                End While
                thereader.Close()

                conn.Close()
                FindRelatedToTerminal = ret
            End If
        End SyncLock
    End Function

    Public Function FindFullSignReserved(ByVal subcarparkno As Short) As Object
        Dim para1 As New SqlParameter("SubCarparkNo", subcarparkno)
        Dim sqlstrTerm As String = "select FullsignReserved from SubCarpark where SubCarparkNo = @SubCarparkNo"
        SyncLock connectionstr
            FindFullSignReserved = executeCmdEx(connectionstr, sqlstrTerm, New SqlParameter() {para1})
        End SyncLock
    End Function

    Public Function FindCHUID(ByVal terminalno As Short) As Object
        Dim para1 As New SqlParameter("TerminalNo", terminalno)
        Dim sqlstrTerm As String = "select CHUID from AntennaConfig where TerminalNo = @TerminalNo"
        SyncLock connectionstr
            FindCHUID = executeCmdEx(connectionstr, sqlstrTerm, New SqlParameter() {para1})
        End SyncLock
    End Function

    Public Function FindAntennaNo(ByVal terminalno As Short) As Object
        Dim para1 As New SqlParameter("TerminalNo", terminalno)
        Dim sqlstrTerm As String = "select AntNo from AntennaConfig where TerminalNo = @TerminalNo"
        SyncLock connectionstr
            FindAntennaNo = executeCmdEx(connectionstr, sqlstrTerm, New SqlParameter() {para1})
        End SyncLock
    End Function

    Public Function FindAntennaID(ByVal terminalno As Short) As Object
        Dim para1 As New SqlParameter("TerminalNo", terminalno)
        Dim sqlstrTerm As String = "select AntennaID from AntennaConfig where TerminalNo = @TerminalNo"
        SyncLock connectionstr
            FindAntennaID = executeCmdEx(connectionstr, sqlstrTerm, New SqlParameter() {para1})
        End SyncLock
    End Function

    Public Function FindTerminalIP(ByVal antNo As Short) As String
        Dim para1 As New SqlParameter("AntNo", antNo)
        Dim sqlstrTerm As String = "select TerminalNo from AntennaConfig where AntNo = @AntNo"
        Dim termobj As Object
        SyncLock connectionstr
            termobj = executeCmdEx(connectionstr, sqlstrTerm, New SqlParameter() {para1})
        End SyncLock

        If termobj Is Nothing Then
            Return Nothing
        End If
        Dim termno As Short = CType(termobj, Short)
        Dim para2 As New SqlParameter("TerminalNo", termno)
        Dim sqlstrip As String = "select IPAddress from Terminal where TerminalNo = @TerminalNo"
        SyncLock connectionstr
            FindTerminalIP = executeCmdEx(connectionstr, sqlstrip, New SqlParameter() {para2})
        End SyncLock
    End Function

    Public Function FindTerminalIP(ByVal chuid As Short, ByVal antId As Short) As String
        Dim hasError As Boolean = False
        Dim sqlstrIP As String = "select IPAddress from Terminal where TerminalNo = @TerminalNo"
        Dim sqlstrTerm As String = "select TerminalNo from AntennaConfig where CHUID = @CHUID and AntennaID = @AntennaID"
        Dim termid As Short
        Dim sqlcn As New SqlConnection(connectionstr)
        Dim cmd As SqlCommand
        Dim tmp As Object
        SyncLock connectionstr
            Try
                sqlcn.Open()
                cmd = sqlcn.CreateCommand
                cmd.CommandText = sqlstrTerm
                cmd.Parameters.Add(New SqlParameter("CHUID", chuid))
                cmd.Parameters.Add(New SqlParameter("AntennaID", antId))
                tmp = cmd.ExecuteScalar()
                sqlcn.Close()
            Catch ex As Exception
                hasError = True
                sqlcn.Close()
            End Try
        End SyncLock

        If hasError = False Then
            If tmp Is Nothing Then
                'report fatal error
            Else
                termid = tmp
            End If

            sqlcn = New SqlConnection(connectionstr)
            SyncLock connectionstr
                Try
                    sqlcn.Open()
                    cmd = sqlcn.CreateCommand
                    cmd.CommandText = sqlstrIP
                    cmd.Parameters.Add(New SqlParameter("TerminalNo", termid))
                    tmp = cmd.ExecuteScalar()
                    sqlcn.Close()
                Catch ex As Exception
                    hasError = True
                    sqlcn.Close()
                End Try
            End SyncLock
            If hasError = False Then
                If tmp Is Nothing Then
                    'report fatal error
                    FindTerminalIP = Nothing
                Else
                    FindTerminalIP = CType(tmp, String)
                End If
            Else
                FindTerminalIP = Nothing
            End If
        Else
            FindTerminalIP = Nothing
        End If
    End Function

    Public Function FindRunningHourlyRecord(ByVal id As String, ByVal subcp As Short, ByVal cp As Short) As Object
        Dim running() As RunningHourly
        Dim ret As Boolean = GetRunningHourly(id, running)
        If ret = True Then
            Dim found As Boolean = False
            For Each runitm As RunningHourly In running
                If IsTerminalInsideThisCarpark(runitm.TerminalNo, cp, subcp, True) = True Then
                    found = True
                    Return runitm
                Else
                    found = False
                End If
            Next
            If found = False Then
                Return Nothing
            End If
        Else
            Return Nothing
        End If
    End Function

    Public Function FindRunningSeasonRecord(ByVal id As String, ByVal subcp As Short, ByVal cp As Short) As Object
        Dim running() As RunningSeason
        Dim ret As Boolean = GetRunningSeason(id, running)
        If ret = True Then
            Dim found As Boolean = False
            For Each runitm As RunningSeason In running
                If IsTerminalInsideThisCarpark(runitm.TerminalNo, cp, subcp, True) = True Then
                    found = True
                    Return runitm
                Else
                    found = False
                End If
            Next
            If found = False Then
                Return Nothing
            End If
        Else
            Return Nothing
        End If
    End Function

    Public Function FindAccessViolationDebitRecord(ByVal ticketno As String, ByRef avb() As AccessViolationDebit) As Boolean
        Dim con As SqlConnection
        SyncLock connectionstr
            Dim thereader As SqlDataReader = executeCmd(connectionstr, "select * from AccessViolationDebit where TicketNo = @TicketNo", _
                            New SqlParameter("TicketNo", ticketno), con)
            If thereader Is Nothing Then
                FindAccessViolationDebitRecord = False
            Else
                Dim i As Integer = 0
                While thereader.Read() = True
                    ReDim avb(i)
                    avb(i).EntryTerminalNo = thereader("EntryTerminalNo")
                    avb(i).EntryTime = thereader("EntryTime")
                    avb(i).ExitTerminalNo = thereader("ExitTerminalNo")
                    avb(i).ExitTime = thereader("ExitTime")
                    avb(i).ID = thereader("ID")
                    avb(i).TicketNo = thereader("TicketNo")
                    i += 1
                End While
                thereader.Close()
                con.Close()
                If i > 0 Then
                    FindAccessViolationDebitRecord = True
                Else
                    FindAccessViolationDebitRecord = False
                End If
            End If
        End SyncLock
    End Function

    Public Function PGSIsEnabled() As Boolean
        SyncLock connectionstr
            Dim tb As DataTable = executeCmd(connectionstr, "Select Enabled from PGSEnabled")
            If tb.Rows.Count = 0 Then
                PGSIsEnabled = False
            Else
                PGSIsEnabled = CType(tb.Rows(0)(0), Boolean)
            End If
        End SyncLock
    End Function

    Public Function CCTVIsEnabled() As Boolean
        SyncLock connectionstr
            Dim tb As DataTable = executeCmd(connectionstr, "Select Enabled from CCTVEnabled")
            If tb.Rows.Count = 0 Then
                CCTVIsEnabled = False
            Else
                CCTVIsEnabled = CType(tb.Rows(0)(0), Boolean)
            End If
        End SyncLock
    End Function

    Public Function IsTerminalInsideThisCarpark(ByVal termno As Short, ByVal cpk As Short, ByVal subcpk As Short, ByVal isEntry As Boolean) As Boolean
        Dim conn As SqlConnection
        Dim thereader As SqlDataReader
        Dim result As Boolean

        SyncLock connectionstr
            If isEntry = True Then
                thereader = executeCmd(connectionstr, "select * from Terminal where DestinationSubCarpark = @DestinationSubCarpark and CarparkNo = @CarparkNo", _
                        New SqlParameter() {New SqlParameter("DestinationSubCarpark", subcpk), New SqlParameter("CarparkNo", cpk)}, conn, False)
            Else
                thereader = executeCmd(connectionstr, "select * from Terminal where SourceSubCarpark = @SourceSubCarpark and CarparkNo = @CarparkNo", _
                                    New SqlParameter() {New SqlParameter("SourceSubCarpark", subcpk), New SqlParameter("CarparkNo", cpk)}, conn, False)
            End If
            'Dim thereader As SqlDataReader = executeCmd(connectionstr, "select * from Terminal where (SourceSubCarpark = ? or DestinationSubCarpark = ?) and CarparkNo = ?", _
            '            New SqlParameter() {New SqlParameter("1", subcpk), New SqlParameter("2", subcpk), New SqlParameter("3", cpk)}, conn, False)
            If thereader Is Nothing Then
                result = False
            Else
                Dim i As Integer = 0
                Dim found As Boolean = False
                While thereader.Read() = True
                    If thereader("TerminalNo") = termno Then
                        found = True
                    End If
                End While
                thereader.Close()

                conn.Close()
                If found = True Then
                    result = True
                Else
                    result = False
                End If
            End If
        End SyncLock

        Return result
    End Function

    Private Function AddRecords(ByVal records As DataTable, ByVal tbnm As String, ByVal constr As String) As Integer

        Dim sqlString As String
        Dim numbers As Integer = 0
        Dim hasError As Boolean = False

        Console.WriteLine("Running: " + tbnm)

        'sqlString = "INSERT INTO TransactionLog (IULabel, CashCardNo, ValidFrom, " & _
        ' "ValidTo, Freeze, Price, IOCheck, Liense, Tenant, Name, NRIC, SeasonType, GroupNo, Remark) " & _
        ' "VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)"

        Dim colnames() As String = getColumnName(tbnm)
        'sqlString = "INSERT INTO IUSeasonInfo VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?)"
        Dim strbuild As New Text.StringBuilder(10)
        strbuild.Append("INSERT INTO ")
        strbuild.Append(tbnm)
        strbuild.Append(" VALUES(")
        'strbuild.Append(getMQuestionMarksStr(colnames.Length))
        strbuild.Append(getParNames(colnames))
        strbuild.Append(")")
        sqlString = strbuild.ToString()
        Dim sqlcn As New SqlConnection(constr)
        Dim cmd As SqlCommand
        Dim therow As DataRow
        Try
            sqlcn.Open()
            cmd = sqlcn.CreateCommand
            cmd.CommandText = sqlString
            Dim i As Integer
            For Each therow In records.Rows
                cmd.Parameters.Clear()
                For i = 0 To colnames.Length - 1
                    Dim para As New SqlParameter(colnames(i), therow(i))
                    cmd.Parameters.Add(para)
                Next
                cmd.ExecuteNonQuery()
                'Catch ex As Exception
                'Console.WriteLine(True)
                'End Try
                numbers += 1
            Next
        Catch ex As SqlClient.SqlException
            hasError = True
            sqlcn.Close()

            dblog.Log("Error in Addrecord.")
            dblog.Log(tbnm & "Add :" & ex.ToString)
            Console.WriteLine(tbnm & "Add :" & ex.ToString)

            If ex.ErrorCode = -2147024882 Then
                dblog.Log("Total memory before collection is " & GC.GetTotalMemory(False))
                Console.WriteLine("Total memory before collection is " & GC.GetTotalMemory(False))
                GC.Collect()
                Console.WriteLine("Total memory after collection is " & GC.GetTotalMemory(False))
                dblog.Log("Total memory after collection is " & GC.GetTotalMemory(False))
            End If
        Catch ex As Exception
            dblog.Log("Error in Addrecord.")
            dblog.Log(tbnm & "Add :" & ex.ToString)
            Console.WriteLine(tbnm & "Add :" & ex.ToString)
        Finally
            If hasError = False Then
                sqlcn.Close()
            End If
            sqlcn = Nothing
            cmd.Dispose()
            cmd = Nothing
        End Try
        Return numbers
    End Function

    Private Function AddIdentifiedRecords(ByVal records As DataTable, ByVal tbnm As String, ByVal constr As String) As Integer

        Dim sqlString As String
        Dim numbers As Integer = 0
        Dim hasError As Boolean = False

        Console.WriteLine("Running Identified: " + tbnm)

        'sqlString = "INSERT INTO TransactionLog (IULabel, CashCardNo, ValidFrom, " & _
        ' "ValidTo, Freeze, Price, IOCheck, Liense, Tenant, Name, NRIC, SeasonType, GroupNo, Remark) " & _
        ' "VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)"

        Dim colnames() As String = getColumnName(tbnm)
        'sqlString = "INSERT INTO IUSeasonInfo VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?)"

        'sqlString = "INSERT INTO " & tbnm & "(" & getParametersString(tbnm) & ") VALUES(" & getMQuestionMarksStr(colnames.Length - 1) & ")"
        Dim strbuild As New Text.StringBuilder(10)
        strbuild.Append("INSERT INTO ")
        strbuild.Append(tbnm)
        strbuild.Append("(")
        strbuild.Append(getParametersString(tbnm))
        strbuild.Append(") VALUES(")
        strbuild.Append(getParNamesEx(colnames))
        strbuild.Append(")")
        sqlString = strbuild.ToString()
        Dim sqlcn As New SqlConnection(constr)
        Dim cmd As SqlCommand = Nothing
        Dim therow As DataRow
        Try
            sqlcn.Open()
            For Each therow In records.Rows
                cmd = sqlcn.CreateCommand
                cmd.Parameters.Clear()
                cmd.CommandText = sqlString
                Dim i As Integer
                For i = 1 To colnames.Length - 1
                    'Dim para As New SqlParameter(colnames(i), therow(i))
                    ' nay replace with above code
                    Dim para As New SqlParameter(colnames(i), records.Columns(i).DataType)
                    para.Value = therow(i)
                    cmd.Parameters.Add(para)
                Next
                cmd.ExecuteNonQuery()
                numbers += 1
            Next
        Catch ex As Exception
            hasError = True
            If cmd IsNot Nothing Then
                cmd.Parameters.Clear()
                cmd.Dispose()
            End If
            sqlcn.Close()
            Console.WriteLine(tbnm & "add iden:" & ex.ToString)
            dblog.Log("Error in AddIdentifiedRecords.")
            dblog.Log(tbnm & " add iden:" & ex.ToString)
        Finally
            If hasError = False Then
                cmd.Parameters.Clear()
                cmd.Dispose()
                sqlcn.Close()
            End If
        End Try
        Return numbers
    End Function

    Private Function updateRecords(ByVal records As DataTable, ByVal tbnm As String, ByVal constr As String) As Integer
        Dim sqlString As String
        Dim numbers As Integer = 0
        Dim hasError As Boolean = False

        Dim colnames() As String = getColumnName(tbnm)
        Dim strbuild As New Text.StringBuilder(10)
        strbuild.Append("UPDATE ")
        strbuild.Append(tbnm)
        strbuild.Append(" set ")
        Dim i As Integer
        For i = 1 To colnames.Length - 2
            strbuild.Append(colnames(i))
            strbuild.Append("=@")
            strbuild.Append(colnames(i))
            strbuild.Append(", ")
        Next
        strbuild.Append(colnames(colnames.Length - 1))
        strbuild.Append("=@")
        strbuild.Append(colnames(colnames.Length - 1))
        strbuild.Append(" where ")
        strbuild.Append(colnames(0))
        strbuild.Append("=@")
        strbuild.Append(colnames(0))
        sqlString = strbuild.ToString()

        Dim sqlcn As New SqlConnection(constr)
        Dim cmd As SqlCommand
        Dim therow As DataRow

        Try
            sqlcn.Open()
            For Each therow In records.Rows
                cmd = sqlcn.CreateCommand
                cmd.Parameters.Clear()
                cmd.CommandText = sqlString
                For i = 1 To colnames.Length - 1
                    Dim para As New SqlParameter(colnames(i), therow(i))
                    cmd.Parameters.Add(para)
                Next
                Dim para1 As New SqlParameter(colnames(0), therow(0))
                cmd.Parameters.Add(para1)
                cmd.ExecuteNonQuery()
                numbers += 1
            Next
        Catch ex As Exception
            hasError = True
            cmd.Parameters.Clear()
            cmd.Dispose()
            sqlcn.Close()
            Console.WriteLine(tbnm & "Update :" & ex.ToString)

            dblog.Log("Error in updateRecords.")
            dblog.Log(tbnm & "Update :" & ex.ToString)
        Finally
            If hasError = False Then
                cmd.Parameters.Clear()
                cmd.Dispose()
                sqlcn.Close()
            End If
        End Try
        Return numbers
    End Function

    Private Function getColumnName(ByVal tablename As String) As String()
        Select Case tablename
            Case "IUSeasonInfo"
                Return New String() {"IULabel", "CashCardNo", "ValidFrom", "ValidTo", "LicenceNo", _
                "Tenant", "Price", "Name", "NRIC", "SeasonType", "GroupNo", "Freeze", "IOCheck", "Remark"}
            Case "CashCardSeasonInfo"
                Return New String() {"CashCardNo", "ValidFrom", "ValidTo", "LicenceNo", _
                "Tenant", "Price", "Name", "NRIC", "SeasonType", "GroupNo", "Freeze", "IOCheck", "Remark"}
            Case "ProxSeasonInfo"
                Return New String() {"ProximityNo", "ValidFrom", "ValidTo", "LicenceNo", _
                "Tenant", "Price", "Name", "NRIC", "SeasonType", "GroupNo", "Freeze", "IOCheck", "Remark"}
            Case "REMSeasonInfo"
                Return New String() {"REMNo", "ValidFrom", "ValidTo", "LicenceNo", _
                "Tenant", "Price", "Name", "NRIC", "SeasonType", "GroupNo", "Freeze", "IOCheck", "Remark"}
            Case "SeasonGroup"
                Return New String() {"GroupNo", "GroupName", "Maximum", "Threshold", "CurrentCount", _
                "Phone", "Fax", "Address", "Remark"}
            Case "CHUConfig"
                Return New String() {"CHUID", "Description", "CPTID", "SPID", "IPAddress", "ControlLine", _
                "ParameterLine", "LogLine", "DataLine"}
            Case "WorkingMode"
                Return New String() {"ID", "WorkingMode", "ParkingType", "Description", "SubCarparkNo", "CarparkNo"}
            Case "Complimentary"
                Return New String() {"ComplimentaryNo", "ValidFrom", "ValidTo", "ComplimentaryType"}
            Case "Redemption"
                Return New String() {"RedemptionNo", "ValidFrom", "ValidTo", "Value", "ValueType", "RedemptionType"}
            Case "Valet"
                Return New String() {"CashCardNo", "ValidFrom", "ValidTo", "Value", "ValetType"}
            Case "IUSeasonPermits"
                Return New String() {"Id", "IULabel", "CarparkNo", "SubCarparkNo"}
            Case "CashCardSeasonPermits"
                Return New String() {"Id", "CashCardNo", "CarparkNo", "SubCarparkNo"}
            Case "ProxSeasonPermits"
                Return New String() {"Id", "ProximityNo", "CarparkNo", "SubCarparkNo"}
            Case "GroupSeasonPermits"
                Return New String() {"Id", "GroupNo", "CarparkNo", "SubCarparkNo"}
            Case "REMSeasonPermits"
                Return New String() {"Id", "REMNo", "CarparkNo", "SubCarparkNo"}
            Case "SeasonType"
                Return New String() {"SeasonType", "RateType", "Description"}
            Case "SubCarpark"
                Return New String() {"SubCarparkNo", "SubCPName", "TotalSpace", "SeasonSpace", "ExtraSeasSpace", _
                "FullsignReserved", "CarparkNo"}
            Case "Terminal"
                Return New String() {"TerminalNo", "TerminalName", "TerminalType", "SourceSubCarpark", _
                "DestinationSubCarpark", "CarparkNo", "IPAddress", "Description", "RateSetNo", "SeasonOnly"}
            Case "TerminalRelation"
                Return New String() {"ID", "FromTerminal", "ToTerminal", "Duration", "ChargeBy"}
            Case "IUCarpkSubCarpkMap"
                Return New String() {"RecordID", "IULabel", "CarparkNo", "SubCarparkNo"}
            Case "AntennaConfig"
                Return New String() {"AntNo", "AntennaID", "AntennaName", "TerminalNo", "ComPort", "CHUID"}
            Case "CyclingBlockList", "DiscreteBlockList"
                Return New String() {"Block_ID", "Block_Type", "FromTime", "EndTime", "PerMinute", "FlatValue", _
                "RegularValue", "RegularUnitAmount", "RegularUnitType", "RateID"}
            Case "RateComposite"
                Return New String() {"CompositeID", "AutoSwitch", "Name"}
            Case "ReceiptMessage"
                Return New String() {"ReceiptHeader1", "ReceiptHeader2", "ReceiptFooter1", "ReceiptFooter2", "IncludeGST"}
            Case "PGS"
                Return New String() {"TerminalNO", "Connected", "InformedBy"}
            Case "FullStatus"
                Return New String() {"TerminalNo", "FullSign"}
            Case "CyclingCapList"
                Return New String() {"CapID", "FromTime", "EndTime", "Amount", "RateID"}
            Case "CrossOverList"
                Return New String() {"CrossOverID", "CrossOverType", "Period", "FirstBlock", "SecondBlock", "RateID"}
            Case "CyclingRateList"
                Return New String() {"RateID", "RateType", "FreePass", "PerMinute", "CountOfBlocks", "CountOfCaps", _
                "CountOfCrossOver", "CompositeID", "DayType", "CapType"}
            Case "AdvancedBlockList"
                Return New String() {"ID", "Duration", "RateValue", "UnitAmount", "UnitType", "Block_ID"}
            Case "RateSet"
                Return New String() {"RateSetNo", "Car", "Lorry", "Motorcycle", "Taxi", "Container", "Reserve1", "Reserve2"}
            Case "RunningHourly"
                Return New String() {"Id", "HourlyNo", "EntryTime", "TerminalNo", "HourlyType", "Status"}
            Case "RunningSeason"
                Return New String() {"Id", "SeasonNo", "EntryTime", "TerminalNo", "SeasonOption", "SeasonType", "Status"}
            Case "Holiday"
                Return New String() {"HolidayID", "HDate"}
            Case "MainConfig"
                Return New String() {"CarparkNo", "CarparkName", "MinUnit", "GST", "GSTNo", "StartOperation", "StartIOCheck"}
            Case "AddonOption"
                Return New String() {"LEDEnabled", "ShutterEnabled", "CashcardConfirm", "PowerFailAlarm", "SeasonAllowedWhenFull", _
                "TailGateSensor", "SubCPWithinSubCP"}
            Case "BlacklistedTicket"
                Return New String() {"TicketNo", "TicketType", "ListedDate", "EffectiveDate", "ListedBy", "Remark"}
            Case "AccessViolation"
                Return New String() {"ID", "TicketNo", "EntryTime", "TerminalNo", "RelationID"}
            Case "AccessViolationDebit"
                Return New String() {"ID", "TicketNo", "EntryTime", "ExitTime", "EntryTerminalNo", "ExitTerminalNo"}
        End Select
    End Function

    Private Function getMQuestionMarksStr(ByVal num As Integer) As String
        Dim i As Integer
        Dim str As String
        Dim strbiuld As New Text.StringBuilder(100)
        For i = 0 To num - 2
            strbiuld.Append("@" & i & ",")
        Next
        Return strbiuld.Append("@" & i).ToString()
    End Function

    Private Function getParNames(ByVal cols() As String) As String
        Dim strbiuld As New Text.StringBuilder(100)
        For Each col As String In cols
            strbiuld.Append("@")
            strbiuld.Append(col)
            strbiuld.Append(","c)
        Next
        Return strbiuld.Remove(strbiuld.Length - 1, 1).ToString()
    End Function

    Private Function getParNamesEx(ByVal cols() As String) As String
        Dim strbiuld As New Text.StringBuilder(100)
        Dim i As Integer = 0
        For Each col As String In cols
            If i <> 0 Then
                strbiuld.Append("@")
                strbiuld.Append(col)
                strbiuld.Append(","c)
            End If
            i += 1
        Next
        Return strbiuld.Remove(strbiuld.Length - 1, 1).ToString()
    End Function

    Private Function getParametersString(ByVal nm As String) As String
        Dim str() As String = getColumnName(nm)
        Dim strbiuld As New Text.StringBuilder(100)
        Dim i As Integer
        For i = 1 To str.Length - 2
            strbiuld.Append(str(i))
            strbiuld.Append(",")
        Next
        Return strbiuld.Append(str(i)).ToString
    End Function

    Private Function executeCmd(ByVal constr As String, ByVal sqlstr As String) As DataTable
        Dim hasError As Boolean = False
        Dim thetable As New DataTable
        Dim adpt As New SqlDataAdapter(sqlstr, constr)
        Try
            adpt.Fill(thetable)

        Catch ex As Exception


            dblog.Log("Error in executeCmd.")
            dblog.Log(sqlstr & ":" & ex.ToString)

            Console.WriteLine(sqlstr & ":" & ex.ToString)
            hasError = True
        Finally

        End Try

        Return thetable
    End Function

    Private Function executeCmd(ByVal constr As String, ByVal sqlstr As String, ByVal para() As SqlParameter) As Integer
        Dim hasError As Boolean = False

        Dim sqlcn As New SqlConnection(constr)
        Dim cmd As SqlCommand
        Try
            sqlcn.Open()
            cmd = sqlcn.CreateCommand
            cmd.CommandText = sqlstr
            For Each p As SqlParameter In para
                cmd.Parameters.Add(p)
            Next
            Return cmd.ExecuteNonQuery()
        Catch ex As Exception
            hasError = True
            cmd.Parameters.Clear()
            cmd.Dispose()
            sqlcn.Close()

            dblog.Log("Error in executeCmd.")
            dblog.Log(sqlstr & ":" & ex.ToString)


            Console.WriteLine(sqlstr & ":" & ex.ToString)
            Return Nothing
        Finally
            If hasError = False Then
                cmd.Parameters.Clear()
                cmd.Dispose()
                sqlcn.Close()
            End If
        End Try

    End Function

    Private Function executeCmd(ByVal constr As String, ByVal sqlstr As String, ByVal para As SqlParameter, ByRef conn As SqlConnection, Optional ByVal singlerow As Boolean = True) As SqlDataReader
        Dim hasError As Boolean = False

        Dim sqlcn As New SqlConnection(constr)
        Dim cmd As SqlCommand
        Try
            sqlcn.Open()
            cmd = sqlcn.CreateCommand
            cmd.CommandText = sqlstr
            cmd.Parameters.Add(para)
            If singlerow = True Then
                Return cmd.ExecuteReader(CommandBehavior.SingleRow)
            Else
                Return cmd.ExecuteReader()
            End If
        Catch ex As Exception
            hasError = True
            cmd.Parameters.Clear()
            cmd.Dispose()
            sqlcn.Close()

            dblog.Log("Error in executeCmd.")
            dblog.Log(sqlstr & ":" & ex.ToString)

            Console.WriteLine(sqlstr & ":" & ex.ToString)
            Return Nothing
        Finally
            If hasError = False Then
                cmd.Parameters.Clear()
                cmd.Dispose()
                conn = sqlcn
            End If
        End Try

    End Function

    Private Function executeCmd(ByVal constr As String, ByVal sqlstr As String, ByVal para() As SqlParameter, ByRef conn As SqlConnection, Optional ByVal singlerow As Boolean = True) As SqlDataReader
        Dim hasError As Boolean = False

        Dim sqlcn As New SqlConnection(constr)
        Dim cmd As SqlCommand
        Try
            sqlcn.Open()
            cmd = sqlcn.CreateCommand
            cmd.CommandText = sqlstr
            For Each p As SqlParameter In para
                cmd.Parameters.Add(p)
            Next
            If singlerow = True Then
                Return cmd.ExecuteReader(CommandBehavior.SingleRow)
            Else
                Return cmd.ExecuteReader()
            End If
        Catch ex As Exception
            hasError = True
            cmd.Parameters.Clear()
            cmd.Dispose()
            sqlcn.Close()
            dblog.Log("Error in executeCmd.")
            dblog.Log(sqlstr & ":" & ex.ToString)

            Console.WriteLine(sqlstr & ":" & ex.ToString)
            Return Nothing
        Finally
            If hasError = False Then
                cmd.Parameters.Clear()
                cmd.Dispose()
                conn = sqlcn
            End If
        End Try

    End Function

    Private Function executeCmdEx(ByVal constr As String, ByVal sqlstr As String, ByVal para() As SqlParameter) As Object
        Dim hasError As Boolean = False

        Dim sqlcn As New SqlConnection(constr)
        Dim cmd As SqlCommand
        Try
            sqlcn.Open()
            cmd = sqlcn.CreateCommand
            cmd.CommandText = sqlstr
            For Each p As SqlParameter In para
                cmd.Parameters.Add(p)
            Next
            Return cmd.ExecuteScalar()
        Catch ex As Exception
            hasError = True
            cmd.Parameters.Clear()
            cmd.Dispose()
            sqlcn.Close()

            dblog.Log("Error in executeCmd.")
            dblog.Log(sqlstr & ":" & ex.ToString)

            Console.WriteLine(sqlstr & ":" & ex.ToString)
            Return Nothing
        Finally
            If hasError = False Then
                cmd.Parameters.Clear()
                cmd.Dispose()
                sqlcn.Close()
            End If
        End Try

    End Function

    Private Function executeCmdNonQuery(ByVal constr As String, ByVal sqlstr As String) As Integer
        Dim hasError As Boolean = False
        Dim sqlcn As New SqlConnection(constr)
        Dim cmd As SqlCommand
        Try
            sqlcn.Open()
            cmd = sqlcn.CreateCommand
            cmd.CommandText = sqlstr
            Return cmd.ExecuteNonQuery()
        Catch ex As Exception
            hasError = True
            cmd.Parameters.Clear()
            cmd.Dispose()
            sqlcn.Close()

            dblog.Log("Error in executeCmdNonQuery.")
            dblog.Log(sqlstr & ":" & ex.ToString)

            Console.WriteLine(sqlstr & ":" & ex.ToString)
            Return Nothing
        Finally
            If hasError = False Then
                cmd.Parameters.Clear()
                cmd.Dispose()
                sqlcn.Close()
            End If
        End Try
    End Function

    Private Function executeCmdScalar(ByVal constr As String, ByVal sqlstr As String) As Object
        Dim hasError As Boolean = False
        Dim sqlcn As New SqlConnection(constr)
        Dim cmd As SqlCommand
        Try
            sqlcn.Open()
            cmd = sqlcn.CreateCommand
            cmd.CommandText = sqlstr
            Return cmd.ExecuteScalar()
        Catch ex As Exception
            hasError = True
            cmd.Parameters.Clear()
            cmd.Dispose()
            sqlcn.Close()

            dblog.Log("Error in executeCmdScalar.")
            dblog.Log(sqlstr & ":" & ex.ToString)

            Console.WriteLine(sqlstr & ":" & ex.ToString)
            Return Nothing
        Finally
            If hasError = False Then
                cmd.Parameters.Clear()
                cmd.Dispose()
                sqlcn.Close()
            End If
        End Try
    End Function

    Public Function AddIUSeasonRecordsEx(ByVal tb As DataTable)
        Dim sqlString As String
        Dim numbers As Integer = 0
        Dim hasError As Boolean = False

        'sqlString = "INSERT INTO TransactionLog (IULabel, CashCardNo, ValidFrom, " & _
        ' "ValidTo, Freeze, Price, IOCheck, Liense, Tenant, Name, NRIC, SeasonType, GroupNo, Remark) " & _
        ' "VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)"

        Console.WriteLine("Add record EX")

        Dim tbnm As String = "IUSeasonInfo"
        Dim colnames() As String = getColumnName(tbnm)
        'sqlString = "INSERT INTO IUSeasonInfo VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?)"
        Dim strbuild As New Text.StringBuilder(10)
        strbuild.Append("INSERT INTO ")
        strbuild.Append(tbnm)
        strbuild.Append(" VALUES(")
        strbuild.Append(getParNames(colnames))
        strbuild.Append(")")
        sqlString = strbuild.ToString()
        Dim sqlcn As New SqlConnection(connectionstr)
        Dim trans As SqlTransaction
        Dim cmd As SqlCommand
        Dim therow As DataRow
        SyncLock connectionstr
            Try
                sqlcn.Open()
                cmd = sqlcn.CreateCommand
                trans = sqlcn.BeginTransaction()
                For Each therow In tb.Rows
                    cmd.Parameters.Clear()
                    cmd.Transaction = trans
                    cmd.CommandText = sqlString
                    Dim i As Integer
                    For i = 0 To colnames.Length - 1
                        Dim para As New SqlParameter(colnames(i), therow(i))
                        cmd.Parameters.Add(para)
                    Next
                    cmd.ExecuteNonQuery()
                    numbers += 1
                Next
            Catch ex As SqlException
                hasError = True
                Console.WriteLine(tbnm & ":" & ex.ToString)

                dblog.Log("Error in AddIUSeasonRecordsEx.")
                dblog.Log(tbnm & ":" & ex.ToString)

                If ex.ErrorCode = -2147024882 Then
                    Console.WriteLine("Total memory before collection is " & GC.GetTotalMemory(False))
                    GC.Collect()
                    Console.WriteLine("Total memory after collection is " & GC.GetTotalMemory(False))
                End If
            Catch ex As Exception
                hasError = True
                Console.WriteLine(tbnm & ":" & ex.ToString)
            Finally
                If hasError = False Then
                    trans.Commit()
                Else
                    numbers = 0
                    trans.Rollback()
                End If
                trans.Dispose()
                trans = Nothing
                sqlcn.Close()
                sqlcn = Nothing
                cmd.Dispose()
                cmd = Nothing
            End Try
        End SyncLock
        Return numbers
    End Function

    Public Function AddRunningHourlyRecordsEx(ByVal tb As DataTable)
        Dim sqlString As String
        Dim numbers As Integer = 0
        Dim hasError As Boolean = False

        'sqlString = "INSERT INTO TransactionLog (IULabel, CashCardNo, ValidFrom, " & _
        ' "ValidTo, Freeze, Price, IOCheck, Liense, Tenant, Name, NRIC, SeasonType, GroupNo, Remark) " & _
        ' "VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)"

        Dim tbnm As String = "RunningHourly"
        Dim colnames() As String = getColumnName(tbnm)
        'sqlString = "INSERT INTO IUSeasonInfo VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?)"
        Dim strbuild As New Text.StringBuilder(10)
        strbuild.Append("INSERT INTO ")
        strbuild.Append(tbnm)
        strbuild.Append("(")
        strbuild.Append(getParametersString(tbnm))
        strbuild.Append(") VALUES(")
        strbuild.Append(getMQuestionMarksStr(colnames.Length - 1))
        strbuild.Append(")")
        sqlString = strbuild.ToString()
        Dim sqlcn As New SqlConnection(connectionstr)
        Dim trans As SqlTransaction
        Dim cmd As SqlCommand
        Dim therow As DataRow
        SyncLock connectionstr
            Try
                sqlcn.Open()
                cmd = sqlcn.CreateCommand
                trans = sqlcn.BeginTransaction()
                For Each therow In tb.Rows
                    cmd.Parameters.Clear()
                    cmd.Transaction = trans
                    cmd.CommandText = sqlString
                    Dim i As Integer
                    For i = 1 To colnames.Length - 1
                        Dim para As New SqlParameter(colnames(i), therow(i))
                        cmd.Parameters.Add(para)
                    Next
                    cmd.ExecuteNonQuery()
                    numbers += 1
                Next
            Catch ex As SqlException
                hasError = True
                Console.WriteLine(tbnm & ":" & ex.ToString)
                dblog.Log("Error in AddRunningHourlyRecordsEx.")
                dblog.Log(tbnm & ":" & ex.ToString)
                If ex.ErrorCode = -2147024882 Then
                    Console.WriteLine("Total memory before collection is " & GC.GetTotalMemory(False))
                    GC.Collect()
                    Console.WriteLine("Total memory after collection is " & GC.GetTotalMemory(False))
                End If
            Catch ex As Exception
                hasError = True
                Console.WriteLine(tbnm & ":" & ex.ToString)
            Finally
                If hasError = False Then
                    trans.Commit()
                Else
                    numbers = 0
                    trans.Rollback()
                End If
                trans.Dispose()
                trans = Nothing
                sqlcn.Close()
                sqlcn = Nothing
                cmd.Dispose()
                cmd = Nothing
            End Try
        End SyncLock
        Return numbers
    End Function

    Public Function AddRunningSeasonRecordsEx(ByVal tb As DataTable)
        Dim sqlString As String
        Dim numbers As Integer = 0
        Dim hasError As Boolean = False

        'sqlString = "INSERT INTO TransactionLog (IULabel, CashCardNo, ValidFrom, " & _
        ' "ValidTo, Freeze, Price, IOCheck, Liense, Tenant, Name, NRIC, SeasonType, GroupNo, Remark) " & _
        ' "VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)"

        Dim tbnm As String = "RunningSeason"
        Dim colnames() As String = getColumnName(tbnm)
        'sqlString = "INSERT INTO IUSeasonInfo VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?)"
        Dim strbuild As New Text.StringBuilder(10)
        strbuild.Append("INSERT INTO ")
        strbuild.Append(tbnm)
        strbuild.Append("(")
        strbuild.Append(getParametersString(tbnm))
        strbuild.Append(") VALUES(")
        strbuild.Append(getParNamesEx(colnames))
        strbuild.Append(")")
        sqlString = strbuild.ToString()
        Dim sqlcn As New SqlConnection(connectionstr)
        Dim trans As SqlTransaction
        Dim cmd As SqlCommand
        Dim therow As DataRow
        SyncLock connectionstr
            Try
                sqlcn.Open()
                cmd = sqlcn.CreateCommand
                trans = sqlcn.BeginTransaction()
                For Each therow In tb.Rows
                    cmd.Parameters.Clear()
                    cmd.Transaction = trans
                    cmd.CommandText = sqlString
                    Dim i As Integer
                    For i = 1 To colnames.Length - 1
                        Dim para As New SqlParameter(colnames(i), therow(i))
                        cmd.Parameters.Add(para)
                    Next
                    cmd.ExecuteNonQuery()
                    numbers += 1
                Next
            Catch ex As SqlException
                hasError = True
                Console.WriteLine(tbnm & ":" & ex.ToString)

                dblog.Log("Error in AddRunningSeasonRecordsEx.")
                dblog.Log(tbnm & ":" & ex.ToString)

                If ex.ErrorCode = -2147024882 Then
                    dblog.Log("Total memory before collection is " & GC.GetTotalMemory(False))
                    Console.WriteLine("Total memory before collection is " & GC.GetTotalMemory(False))
                    GC.Collect()
                    Console.WriteLine("Total memory after collection is " & GC.GetTotalMemory(False))
                    dblog.Log("Total memory after collection is " & GC.GetTotalMemory(False))
                End If
            Catch ex As Exception
                hasError = True
                Console.WriteLine(tbnm & ":" & ex.ToString)
            Finally
                If hasError = False Then
                    trans.Commit()
                Else
                    numbers = 0
                    trans.Rollback()
                End If
                trans.Dispose()
                trans = Nothing
                sqlcn.Close()
                sqlcn = Nothing
                cmd.Dispose()
                cmd = Nothing
            End Try
        End SyncLock
        Return numbers
    End Function

    '*********************************************************************
    'Malbinda 2014-10-27
    Public Sub UpdateSeasonAuthorizeIU(ByVal strIU As String)
        SyncLock connectionstr
            executeCmdNonQuery(connectionstr, "UPDATE IUSeasonInfo SET Remark='' WHERE IULabel='" & strIU & "'")
        End SyncLock
    End Sub

    Public Sub InsertExitSeasonRunningRecord(ByVal strIU As String, ByVal strDate As String)
        SyncLock connectionstr
            Dim strSQL As String
            strSQL = "INSERT INTO RunningSeasonExit(IUNo,ExitTime) VALUES " & _
            "('" & strIU & "','" & strDate & "')"
            executeCmdNonQuery(connectionstr, strSQL)
        End SyncLock
    End Sub
    '*********************************************************************
    'Malbinda 2014-10-27
    Public Sub InsertEntrySeasonRunningRecord(ByVal strIU As String, ByVal strDate As String)
        SyncLock connectionstr
            Dim strSQL As String
            strSQL = "INSERT INTO RunningSeasonEntry(IUNo,ExitTime) VALUES " & _
            "('" & strIU & "','" & strDate & "')"
            executeCmdNonQuery(connectionstr, strSQL)
        End SyncLock
    End Sub
    '*********************************************************************

    'Added by Jun Shong 2025-09-25
    Public Function IsValidSeasonPlate(ByVal plateNumber As String) As Boolean
        Dim con As SqlConnection
        Dim para1 As New SqlParameter("PlateNumber", plateNumber)
        SyncLock connectionstr
            Dim thereader As SqlDataReader = executeCmd(connectionstr, "SELECT LicenceNo FROM IUSeasonInfo WHERE LicenceNo = @PlateNumber AND SeasonType = -1 AND GETDATE() BETWEEN ValidFrom AND ValidTo",
                            New SqlParameter() {para1}, con)
            If thereader Is Nothing Then
                IsValidSeasonPlate = False
            Else
                If thereader.Read() = False Then
                    thereader.Close()
                    con.Close()
                    'Error - No matching record found
                    IsValidSeasonPlate = False
                Else
                    ' Found a matching record - valid season plate
                    thereader.Close()
                    con.Close()
                    IsValidSeasonPlate = True
                End If
            End If
        End SyncLock
    End Function


    Public Function GetIUForLicensePlate(ByVal plateNumber As String) As String
        Dim con As SqlConnection
        Dim para1 As New SqlParameter("PlateNumber", plateNumber)
        SyncLock connectionstr
            Dim thereader As SqlDataReader = executeCmd(connectionstr,
            "SELECT IULabel FROM IUSeasonInfo WHERE LicenceNo = @PlateNumber AND SeasonType = -1 AND GETDATE() BETWEEN ValidFrom AND ValidTo",
            New SqlParameter() {para1}, con)
            If thereader Is Nothing Then
                Return String.Empty
            Else
                If thereader.Read() = False Then
                    thereader.Close()
                    con.Close()
                    Return String.Empty
                Else
                    Dim iuLabel As String = thereader("IULabel").ToString()
                    thereader.Close()
                    con.Close()
                    Return iuLabel
                End If
            End If
        End SyncLock
    End Function
End Class
