Imports Parkrite.SystemShared
Imports System.Data

Public Enum TypeOfService
    FULLEPS
    SEMIEPS
    CASHCARD
    REMTICKET
End Enum
Public Interface ITerminalService
    ReadOnly Property SeasonCount()
    Function StartIdentityAuthenticate(Optional ByVal autoactive As Boolean = True) As Boolean
    Function Identity() As String
    ReadOnly Property ServiceType() As TypeOfService
    Function CanProceed() As Boolean
    Function CheckMultipleUsage() As Boolean
    Function selftest() As SelfTestResult
    Sub PauseIdentityAuthenticate()
    Sub init()
    Sub UpdateSeason(ByVal rw As ClsSeasonInfo)
    Sub UpdateSeason(ByVal rw As DataRow)
    Sub UpdateSeasonPermits(ByVal rw As ClsSeasonPermits)
    Sub UpdateSeasonPermits(ByVal rw As DataRow)
    Sub DeleteSeason(ByVal ticket As String)
    Sub DeleteSeasonPermits(ByVal ticket As String, ByVal subcpno As Short)
    Sub ClearSeason()
    Event IdentityAuthenticateSucceed(ByVal service As ITerminalService, ByVal ticket As String, ByVal runstat As RunningStatus)
    Event IndentityAuthenticateFailed(ByVal service As ITerminalService)
    Function IsSeason(ByVal ticket As String, ByRef seasoninfo As ClsSeasonInfo) As RunningStatus
End Interface

Public Interface ITerminalManager
    Sub init()
    Sub Run()
    Sub Cancel()
End Interface

