Imports System.IO
Imports Parkrite
Imports Parkrite.ClientBackbone
Imports Parkrite.SystemShared
Imports Parkrite.SystemShared.Messaging

Public Class EPSEntryService
    Inherits EpsService

    Private mIUwhencpfull As String = String.Empty

    Public Property IUWhenFull() As String
        Get
            Return mIUwhencpfull
        End Get
        Set(ByVal Value As String)
            mIUwhencpfull = Value
        End Set
    End Property

    Sub New(ByVal backboneMnger As ParkriteClient, ByVal termmgr As TerminalManager)
        MyBase.New(backboneMnger, termmgr)
        'AddHandler mTermMgr.MessageHandledByEntryFullEPS, AddressOf HandleEntryFullEpsMsg
        AddHandler termmgr.IUIsDetected, AddressOf IUdetectedhandler
    End Sub

    Private Sub IUdetectedhandler(ByVal iu As String)
        Try
            Console.WriteLine("Entry IU = " & iu) 'Malbinda 2014-10-27
            'TerminalSite.TerminalLog.Log("Entry IUdetectedhandler") 'Malbinda 2015-05-20
            If mTermMgr.IsProcessed(iu) Then
                '************************************************************
                'Dim iuseas1 As ClsSeasonInfo
                'Dim theresult1 As RunningStatus = IsSeason(iu, iuseas1)
                'raiseIdentityAuthenticateSucceed(Me, iu, theresult1)
                'AssemblyTransaction(iu, theresult1, iuseas1.SeasonType)
                '************************************************************
                HandleProcessedEntity(iu)
                Return


                'zaw commented on below 25/08/2016
            ElseIf mTermMgr.IsInProcessing(iu) Then
                DoIfInProcessing(iu)
                Return
            End If

            Threading.Thread.CurrentThread.Priority = Threading.ThreadPriority.Highest
            mTermMgr.AddToInProcessing(iu) 'Malbinda 2014-10-27
            'Console.WriteLine("Entry IUdetectedhandler 1") 'Malbinda 2014-10-27
            'TerminalSite.TerminalLog.Log("Entry IUdetectedhandler 1") 'Malbinda 2015-05-20

            Dim theblk As New BlacklistedTicket
            If mTermMgr.IsBlacklisted(iu, theblk) = True Then
                If Today >= theblk.EffectiveDate Then
                    mTermMgr.HDManager.DisplayLED("Blacklisted IU", Now.ToString("dd/MM/yyyy HH:mm:ss")) 'swap on  13/04/06
                    mTermMgr.HDManager.DisplayLCD("Blacklisted IU", 1)
                    mTermMgr.HDManager.DisplayLCD(Now.ToString("dd/MM/yyyy HH:mm:ss"), 2)
                    mTermMgr.SendSimpleErrorToSMC(iu, ComCodes.TransactionTypeSelections.EntryIUBlacklisted)
                    'mIsAuthenticating = False
                    Return
                End If
            End If

            Dim iuseas As ClsSeasonInfo
            Dim theresult As RunningStatus = IsSeason(iu, iuseas)


            ''jswei
            '' Get LicenceNo from your own source, NOT from ClsSeasonInfo
            'Dim licenceNo As String = mTermMgr.DBManager.GetLicenceNoForIU(iu) ' <-- Replace with your actual method or variable

            'Dim dbManager As DatabaseManager = mTermMgr.DBManager
            'Dim iuSeasonTable As New DataTable()
            'iuSeasonTable.Columns.AddRange(dbManager.GetIUSeasonDataColumns())

            'Dim row As DataRow = iuSeasonTable.NewRow()
            'row("IULabel") = iu
            'row("LicenceNo") = licenceNo ' <-- Use your external value here
            'row("ValidFrom") = iuseas.ValidFrom
            'row("ValidTo") = iuseas.ValidTo
            'row("SeasonType") = iuseas.SeasonType
            'row("GroupNo") = iuseas.GroupNo
            'row("Freeze") = iuseas.Freeze
            'row("IOCheck") = iuseas.IOCheck
            'row("CashCardNo") = DBNull.Value
            'row("Tenant") = DBNull.Value
            'row("Price") = DBNull.Value
            'row("Name") = DBNull.Value
            'row("NRIC") = DBNull.Value
            'row("Remark") = DBNull.Value

            'iuSeasonTable.Rows.Add(row)
            'dbManager.AddIUSeasonInfoRecords(iuSeasonTable)
            '' --- End of block ---

            TerminalSite.TerminalLog.Log("Detect IU: " & iu) 'Malbinda 2015-07-08
            Select Case theresult
                Case Enumeration.RunningStatus.ORIGINALSEASON, Enumeration.RunningStatus.SPECIALSEASON
                    If mParkingType = Enumeration.PARKINGTYPENUM.HOURLY Then
                        mTermMgr.HDManager.DisplayLED("Hourly Only", Now.ToString("dd/MM/yyyy HH:mm:ss")) 'swap on  13/04/06
                        mTermMgr.HDManager.DisplayLCD("Hourly Only", 1)
                        mTermMgr.HDManager.DisplayLCD(Now.ToString("dd/MM/yyyy HH:mm:ss"), 2)
                        mTermMgr.SendSimpleErrorToSMC(iu, ComCodes.TransactionTypeSelections.EntryIUNotAllowedDueToHourlyOnly)
                        Return
                    Else
                        If mTermMgr.AddOnOption.SubCPWithinSubCP = True Then
                            If mTermMgr.IsToTerminal Then
                                Dim avrd As AccessViolation
                                If mTermMgr.GetAccessViolation(iu, avrd) = True Then
                                    Dim index As Short = mTermMgr.IsRelatedFromTerminal(avrd.TerminalNo)
                                    If index <> -1 Then
                                        Dim TermRel As TerminalRelation = mTermMgr.RelatedFromTerminal(index)
                                        If Now.Subtract(avrd.EntryTime).TotalMinutes > TermRel.Duration Then
                                            If mTermMgr.ForbidParkingDueToTimeOut = True Then
                                                mTermMgr.HDManager.DisplayLED("Grace over", "Park at " & TermRel.FromTerminalName)
                                                mTermMgr.HDManager.DisplayLCD("Grace over", 1)
                                                mTermMgr.HDManager.DisplayLCD("Park at " & TermRel.FromTerminalName, 2)
                                                Return
                                            Else
                                                'Not implemented
                                            End If
                                        Else
                                            mTermMgr.RemoveAccessViolation(iu)
                                            mTermMgr.DBManager.DoRunningAccessViolation(iu, Parkrite.DatabaseManager.DBAction.DELETE)
                                        End If
                                    End If
                                End If
                            End If
                        End If
                        '
                        TerminalSite.TerminalLog.Log("IUdetectedhandler - Open Barrier") 'Malbinda 2015-07-08
                        mTermMgr.HDManager.OpenBarrier()
                        'HandleProcessedEntity(iu)
                        '***********************
                        raiseIdentityAuthenticateSucceed(Me, iu, theresult)
                        AssemblyTransaction(iu, theresult, iuseas.SeasonType)

                        'mTermMgr.HDManager.DisplayLED("Season entry", Now.ToString("dd/MM/yyyy HH:mm:ss"))
                        'mTermMgr.HDManager.DisplayLCD("Season entry", 1)
                        'mTermMgr.HDManager.DisplayLCD(Now.ToString("dd/MM/yyyy HH:mm:ss"), 2)

                        If DateDiff(DateInterval.Day, Now, iuseas.ValidTo) <= mTermMgr.expday Then
                            mTermMgr.HDManager.DisplayLED("Season entry", "Expire:" & iuseas.ValidTo.ToString("dd/MM/yy"))
                            mTermMgr.HDManager.DisplayLCD("Season entry", 1)
                            mTermMgr.HDManager.DisplayLCD("Expire:" & iuseas.ValidTo.ToString("dd/MM/yy"), 2)
                        Else
                            mTermMgr.HDManager.DisplayLED("Season entry", Now.ToString("dd/MM/yyyy HH:mm:ss"))
                            mTermMgr.HDManager.DisplayLCD("Season entry", 1)
                            mTermMgr.HDManager.DisplayLCD(Now.ToString("dd/MM/yyyy HH:mm:ss"), 2)
                        End If

                    End If
                Case Else
                    If mParkingType = Enumeration.PARKINGTYPENUM.SEASON Then
                        mTermMgr.HDManager.DisplayLED("Season Only", Now.ToString("dd/MM/yyyy HH:mm:ss")) 'swap on  13/04/06
                        mTermMgr.HDManager.DisplayLCD("Season Only", 1)
                        mTermMgr.HDManager.DisplayLCD(Now.ToString("dd/MM/yyyy HH:mm:ss"), 2)
                        mTermMgr.SendSimpleErrorToSMC(iu, ComCodes.TransactionTypeSelections.EntryIUNotAllowedDueToSeasonOnly)
                        mTermMgr.UpdateInProcessing(iu, ComCodes.TransactionTypeSelections.EntryIUNotAllowedDueToSeasonOnly)
                        Return
                    Else
                        If mTermMgr.FullSignOn = True And mTermMgr.FullSignBuffer = 0 Then
                            mIUwhencpfull = iu
                            If mTermMgr.fullmsg = True Then
                                mTermMgr.HDManager.DisplayLED("Please stop", "Security check")
                                mTermMgr.HDManager.DisplayLCD("Please stop", 1)
                                mTermMgr.HDManager.DisplayLCD("Security check", 2)
                            Else
                                mTermMgr.HDManager.DisplayLED("Carpark Full", Now.ToString("dd/MM/yyyy HH:mm:ss"))
                                mTermMgr.HDManager.DisplayLCD("Carpark Full", 1)
                                mTermMgr.HDManager.DisplayLCD(Now.ToString("dd/MM/yyyy HH:mm:ss"), 2)
                            End If
                            'If mTermMgr.DBManager.IsTerminalBlockedByFull(mTermMgr.TermInfo.TerminalNo) = True And mTermMgr.FullSignBuffer = 0 Then
                            mTermMgr.SendSimpleErrorToSMC(iu, ComCodes.TransactionTypeSelections.EntryIUCarparkFull)
                        Else
                            raiseIdentityAuthenticateSucceed(Me, iu, theresult)
                            Select Case theresult
                                Case Enumeration.RunningStatus.SEASONACCESSVIOLATED
                                    mTermMgr.HDManager.DisplayLED("Hourly entry", "Access Denied!")
                                    mTermMgr.HDManager.DisplayLCD("Hourly entry", 1)
                                    mTermMgr.HDManager.DisplayLCD("Access Denied!", 2)
                                    mTermMgr.SendSimpleErrorToSMC(iu, ComCodes.TransactionTypeSelections.EntryIUSeasonWrongSubCarpark)
                                Case Enumeration.RunningStatus.SEASONEXPIRED
                                    mTermMgr.HDManager.DisplayLED("Hourly entry", "Season Expired!")
                                    mTermMgr.HDManager.DisplayLCD("Hourly entry", 1)
                                    mTermMgr.HDManager.DisplayLCD("Season Expired!", 2)
                                    mTermMgr.SendSimpleErrorToSMC(iu, ComCodes.TransactionTypeSelections.EntryIUSeasonExpired)
                                Case Enumeration.RunningStatus.SEASONFROZEN
                                    mTermMgr.HDManager.DisplayLED("Hourly entry", "Season Frozen!")
                                    mTermMgr.HDManager.DisplayLCD("Hourly entry", 1)
                                    mTermMgr.HDManager.DisplayLCD("Season Frozen!", 2)
                                    mTermMgr.SendSimpleErrorToSMC(iu, ComCodes.TransactionTypeSelections.EntryIUSeasonFrozen)
                                Case Enumeration.RunningStatus.SEASONINVALID
                                    mTermMgr.HDManager.DisplayLED("Hourly entry", "Season Invalid!")
                                    mTermMgr.HDManager.DisplayLCD("Hourly entry", 1)
                                    mTermMgr.HDManager.DisplayLCD("Season Invalid Yet!", 2)
                                    mTermMgr.SendSimpleErrorToSMC(iu, ComCodes.TransactionTypeSelections.EntryIUSeasonInvalid)
                                Case Enumeration.RunningStatus.MULTIPLESEASON
                                    mTermMgr.HDManager.DisplayLED("Hourly entry", "Multiple season!")
                                    mTermMgr.HDManager.DisplayLCD("Hourly entry", 1)
                                    mTermMgr.HDManager.DisplayLCD("Multiple season!", 2)
                                    mTermMgr.SendSimpleErrorToSMC(iu, ComCodes.TransactionTypeSelections.EntryIUSeasonMultipleSeasons)
                                Case Enumeration.RunningStatus.ORIGINALHOURLY
                                    mTermMgr.HDManager.DisplayLED("Hourly entry", Now.ToString("dd/MM/yyyy HH:mm:ss"))
                                    mTermMgr.HDManager.DisplayLCD("Hourly entry", 1)
                                    mTermMgr.HDManager.DisplayLCD(Now.ToString("dd/MM/yyyy HH:mm:ss"), 2)
                            End Select
                            AssemblyTransaction(iu, theresult)
                        End If
                    End If
            End Select
            Threading.Thread.CurrentThread.Priority = Threading.ThreadPriority.Normal
        Catch ex As Exception
            TerminalSite.ErrorLog.Log("Exception on EPSEntryService.IUdetectedhandler : " & ex.Message)
        End Try

    End Sub

    Protected Overrides Sub HandleProcessedEntity(ByVal ticket As String)
        If mTermMgr.HDManager.BarrierIsClose = True Then
            mTermMgr.HDManager.OpenBarrier()
        Else        'add 24/06/08
            mTermMgr.HDManager.OpenBarrier()
        End If
    End Sub

    Public Overrides ReadOnly Property ServiceType() As TypeOfService
        Get

        End Get
    End Property

    Public Overrides Sub init()

    End Sub

    Protected Overrides Sub AssemblyTransaction(ByVal ticket As String, ByVal runstat As RunningStatus, Optional ByVal runningInfo As Object = Nothing)
        Try
            ' Console.WriteLine("Entry AssemblyTransaction") 'Malbinda 2015-02-14
            'TerminalSite.TerminalLog.Log("Entry AssemblyTransaction") 'Malbinda 2015-05-20
            Dim trans As New TransactionMsg
            trans.message1 = ticket
            trans.message2 = mTermMgr.TermID
            trans.message3 = Now.ToString("dd/MM/yyyy HH:mm:ss")
            trans.mMulticast = True
            trans.mLog = True
            Select Case runstat
                Case Enumeration.RunningStatus.ORIGINALSEASON
                    trans.message4 = 0 'To prevent SMC runtime error
                    trans.TransactionType = ComCodes.TransactionTypeSelections.EntryIUSeason
                    mTermMgr.AddTranAndSend(ticket, trans, ComCodes.TransactionTypeSelections.EntryIUSeasonRemoved)
                    ProcessRecvedEntryTransaction(trans, runstat, Enumeration.SEASONTOPTIONS.IU)
                Case Enumeration.RunningStatus.SPECIALSEASON
                    trans.message4 = 0 'To prevent SMC runtime error
                    trans.message5 = runningInfo
                    trans.TransactionType = ComCodes.TransactionTypeSelections.EntryIUSeasonFlexible
                    mTermMgr.AddTranAndSend(ticket, trans, ComCodes.TransactionTypeSelections.EntryIUSeasonFlexibleRemoved)
                    ProcessRecvedEntryTransaction(trans, runstat, Enumeration.SEASONTOPTIONS.IU)
                    '***********************************************************
                    'Malbinda 2015-02-15
                    'Put here the source code for the exit transaction to be process of the WebServiceInterface.exe
                    mTermMgr.DBManager.InsertEntrySeasonRunningRecord(ticket, Now.ToString("yyyy-MM-dd HH:mm:ss"))
                    '***********************************************************
                Case Else
                    trans.message4 = CType(runstat, Integer)
                    Select Case Integer.Parse(Left(ticket, 3))
                        Case Is = 105
                            trans.TransactionType = ComCodes.TransactionTypeSelections.EntryIUHourlyTaxi
                            mTermMgr.AddTranAndSend(ticket, trans, ComCodes.TransactionTypeSelections.EntryIUHourlyTaxiRemoved)
                        Case Is = 2, 4
                            trans.TransactionType = ComCodes.TransactionTypeSelections.EntryAuthorizedVehicle
                            mTermMgr.AddTranAndSend(ticket, trans, ComCodes.TransactionTypeSelections.EntryAuthorizedVehicleRemoved)
                            mTermMgr.HDManager.DisplayLED("Authorized Entry", Now.ToString("dd/MM/yyyy HH:mm:ss"))
                            mTermMgr.HDManager.DisplayLCD("Authorized Entry", 1)
                            mTermMgr.HDManager.DisplayLCD(Now.ToString("dd/MM/yyyy HH:mm:ss"), 2)
                        Case Is >= 150
                            trans.TransactionType = ComCodes.TransactionTypeSelections.EntryIUHourlyLorry
                            mTermMgr.AddTranAndSend(ticket, trans, ComCodes.TransactionTypeSelections.EntryIUHourlyLorryRemoved)
                        Case Is < 100
                            trans.TransactionType = ComCodes.TransactionTypeSelections.EntryIUHourlyMotorcycle
                            mTermMgr.AddTranAndSend(ticket, trans, ComCodes.TransactionTypeSelections.EntryIUHourlyMotorcycleRemoved)
                        Case Else
                            trans.TransactionType = ComCodes.TransactionTypeSelections.EntryIUHourlyCar
                            mTermMgr.AddTranAndSend(ticket, trans, ComCodes.TransactionTypeSelections.EntryIUHourlyCarRemoved)
                    End Select
                    ProcessRecvedEntryTransaction(trans, runstat)
            End Select
        Catch ex As Exception
            TerminalSite.ErrorLog.Log("Entry AssemblyTransaction : " & ex.Message)
            '************************************************************************************
            'Malbinda 2015-08-21
            'Note: Call the RestartApp.exe to restart the SeasonTerminal Application
            '************************************************************************************
            Dim restfile As String = My.Computer.FileSystem.CurrentDirectory & "\RestartApp.exe"
            Dim thisprogram As String = Process.GetCurrentProcess.ProcessName
            If File.Exists(restfile) Then
                System.Diagnostics.Process.Start(restfile, thisprogram)
            End If
            '************************************************************************************
        End Try
    End Sub

    Protected Overrides Sub keepIUDetectionEx()
        'While True
        '    Try
        '        If IsNothing(mTermMgr.HDManager) = False And mTermMgr.CurrentSystemMode = Enumeration.SystemMode.ACTIVE Then
        '            'If mTermMgr.HDManager.VloopStatus = Enumeration.LoopState.LOOPON And mTermMgr.CountOfEntitiesProcessing = 0 Then
        '            If mTermMgr.HDManager.VloopStatus = Enumeration.LoopState.LOOPON Then
        '                mTermMgr.LastVLoopOnTime = Now
        '                Try
        '                    mIULabel = mTermMgr.HDManager.ReadIUFromSerialPort()
        '                Catch ex As Exception
        '                    TerminalSite.TerminalLog.Log("Exception during detection")
        '                    TerminalSite.TerminalLog.Log(Ex.Message)
        '                End Try
        '                If Not mIULabel Is Nothing Then
        '                    If mIULabel = "OFFLINE" Then
        '                        'mIULabel = Nothing
        '                        If mTermMgr.HasCashCardSys Then
        '                            If mTermMgr.CashCardSys.StartIdentityAuthenticate() = True Then
        '                                If mTermMgr.HDManager.VloopStatus = Enumeration.LoopState.LOOPON Then
        '                                    mTermMgr.HDManager.DisplayLED("Pls insert card", "")    'update 21/05/07
        '                                    mTermMgr.HDManager.DisplayLCD("Pls insert cashcard", 2)
        '                                Else
        '                                    mTermMgr.HDManager.ClearLED()
        '                                End If
        '                            End If
        '                        End If
        '                    ElseIf mIULabel = "1096000001" Then
        '                        mFaltyIUTimes += 1
        '                        If mFaltyIUTimes = 1 Then
        '                            Dim mm As New TransactionMsg
        '                            mm.TransactionType = ComCodes.TransactionTypeSelections.FaultyIU
        '                            mm.message1 = mFaltyIUTimes
        '                            mm.message2 = mTermMgr.TermID
        '                            mTermMgr.SendMsgToSMC(mm)
        '                        End If
        '                        If mTermMgr.HasCashCardSys Then
        '                            If mTermMgr.CashCardSys.StartIdentityAuthenticate() = True Then
        '                                mTermMgr.HDManager.DisplayLED("Pls insert card", "")    'update 21/05/07
        '                                mTermMgr.HDManager.DisplayLCD("Pls insert cashcard", 2)
        '                            End If
        '                        End If
        '                    Else
        '                        mDetectFailTimes = 0
        '                        mFaltyIUTimes = 0
        '                        mContinuousDetection = False
        '                        If mTermMgr.IsProcessed(mIULabel) Then
        '                            'Handle processed Entity
        '                            HandleProcessedEntity(mIULabel)
        '                        ElseIf mTermMgr.IsInProcessing(mIULabel) Then
        '                            DoIfInProcessing(mIULabel)

        '                            'If mTermMgr.TerminalType = NetworkReferences.DeviceServiceDefinition.DeviceType.Exit_Device Then
        '                            '    If mTermMgr.mRecordNotFounds.Contains(mIULabel) Then
        '                            '        SyncLock mTermMgr.mRecordNotFounds.SyncRoot
        '                            '            Dim info As TerminalManager.RecordNotFoundInfo = mTermMgr.mRecordNotFounds(mIULabel)
        '                            '            If info.Processing = False Then
        '                            '                info.Processing = True
        '                            '                'mLastDetecetdIU = iulabel
        '                            '                RaiseEvent IUDetected(mIULabel)
        '                            '                'RaiseEPSIUDetected(mIULabel)
        '                            '                'DetectInterval = 100
        '                            '            End If
        '                            '        End SyncLock
        '                            '    Else
        '                            '        'Process iudetected
        '                            '        RaiseEvent IUDetected(mIULabel)
        '                            '    End If
        '                            'End If
        '                        Else
        '                            If mTermMgr.CCTVEnabled = True Then
        '                                mTermMgr.ReportIUTag(mIULabel)
        '                            End If
        '                            RaiseIUDetectedEvent(mIULabel)
        '                            TerminalSite.TerminalLog.Log("keepIUDetectionEx(continuous) - Will process detected IU : " & mIULabel)
        '                        End If
        '                    End If
        '                Else
        '                    mDetectFailTimes += 1
        '                    mFaltyIUTimes = 0
        '                    'Trigger cashcard system if any
        '                    If mTermMgr.HasCashCardSys Then
        '                        If mTermMgr.CashCardSys.StartIdentityAuthenticate() = True Then
        '                            If mTermMgr.HDManager.VloopStatus = Enumeration.LoopState.LOOPON Then
        '                                mTermMgr.HDManager.DisplayLED("Pls insert card", "")    'update 21/05/07
        '                                mTermMgr.HDManager.DisplayLCD("Pls insert cashcard", 2)
        '                            Else
        '                                mTermMgr.HDManager.ClearLED()
        '                            End If
        '                        End If
        '                    End If
        '                    If mDetectFailTimes = 8 Then
        '                        Dim mm As New TransactionMsg
        '                        mm.TransactionType = ComCodes.TransactionTypeSelections.AntennaCannotDetectIU
        '                        mm.message1 = mDetectFailTimes
        '                        mm.message2 = mTermMgr.TermID
        '                        mTermMgr.SendMsgToSMC(mm)
        '                    End If
        '                End If
        '            End If
        '        End If
        '    Catch ex As Exception
        '        TerminalSite.TerminalLog.Log("There is error in detection proc")
        '        TerminalSite.TerminalLog.Log(ex.Message)
        '    End Try
        '    Threading.Thread.Sleep(200)
        'End While
        Console.WriteLine("I will quit the keepIUDetection thread...")
    End Sub

    Public Sub IUDetectedwhenBypassFull(ByVal iu As String)
        If iu Is Nothing Then
            If mIUwhencpfull <> String.Empty Then
                If mTermMgr.IsProcessed(mIUwhencpfull) = True Then
                    HandleProcessedEntity(mIUwhencpfull)
                ElseIf mTermMgr.IsInProcessing(mIUwhencpfull) = True Then
                    DoIfInProcessing(mIUwhencpfull)
                Else
                    mIULabel = mIUwhencpfull
                    RaiseIUDetectedEvent(mIULabel)
                End If
            Else
                TerminalSite.TerminalLog.Log("IUDetectedwhenBypassFull: mIUwhencpfull is empty")
            End If
        Else
            mIULabel = iu
            If mTermMgr.IsProcessed(mIULabel) = True Then
                HandleProcessedEntity(mIULabel)
            ElseIf mTermMgr.IsInProcessing(mIULabel) = True Then
                DoIfInProcessing(mIULabel)
            Else
                RaiseIUDetectedEvent(mIULabel)
            End If
        End If
    End Sub

    Protected Overrides Sub DoIfInProcessing(ByVal iu As String)
        Dim temp As TerminalManager.EntityProcessing = mTermMgr.GetObjFromProcessing(iu)
        If Not temp.obj Is Nothing Then
            If temp.obj.ToString = "EntryIUNotAllowedDueToSeasonOnly" Then
                mTermMgr.SendSimpleErrorToSMC(iu, ComCodes.TransactionTypeSelections.EntryIUNotAllowedDueToSeasonOnly)
            End If
        End If
    End Sub

End Class
