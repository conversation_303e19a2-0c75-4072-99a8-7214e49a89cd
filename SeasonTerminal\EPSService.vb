Imports Parkrite.ClientBackbone
Imports Parkrite.SystemShared
Imports Parkrite.SystemShared.Messaging
Imports System.Data
Imports System.Threading

Public MustInherit Class EpsService
    Inherits BasicService

    Protected backbone As ParkriteClient
    Protected mParkingType As PARKINGTYPENUM
    Protected mHasCHU As Boolean
    Protected mIULabel As String
    Protected mDownloadGeneralInfo As New Hashtable
    Protected Delegate Function RetrieveFromDB() As DataTable
    Private mIUSeasonTB As New DataTable

    Protected Event IUDetected(ByVal iu As String)
    Protected mContinuousDetection As Boolean = False
    Protected mDetectFailTimes As Integer = 0
    Private mOfflineTimes As Integer = 0
    Protected mFaltyIUTimes As Integer = 0
    Private mLastRestartAntennaTime As DateTime
    Protected mLastestDetectedIU As String
    Protected mLastestDetectedTime As DateTime

    Public Property ContunueDetect() As Boolean
        Get
            Return mContinuousDetection
        End Get
        Set(ByVal Value As Boolean)
            mContinuousDetection = Value
        End Set
    End Property

    Public Property ParkingType() As PARKINGTYPENUM
        Get
            Return mParkingType
        End Get
        Set(ByVal Value As PARKINGTYPENUM)
            mParkingType = Value
        End Set
    End Property

    Public Property HasCHU() As Boolean
        Get
            Return mHasCHU
        End Get
        Set(ByVal Value As Boolean)
            mHasCHU = Value
        End Set
    End Property

    'Public ReadOnly Property CurrentEntity() As String
    '    Get
    '        Return mIULabel
    '    End Get
    'End Property

    Public ReadOnly Property CurrentEntity(Optional ByVal processed As Boolean = True) As String
        Get
            Dim iu As String = mIULabel
            If IsNothing(iu) = True Then
                GoTo nextstep1
            ElseIf iu = "OFFLINE" Then
                GoTo nextstep1
            ElseIf iu = "1096000001" Then
                GoTo nextstep1
            Else
                GoTo validiu
            End If
nextstep1:
            iu = RealTimeEntity()
            If IsNothing(iu) = True Then
                GoTo nextstep2
            Else
                GoTo validiu
            End If
nextstep2:
            iu = mLastestDetectedIU
            If iu Is Nothing Then
                Return Nothing
            Else
                If Now.Subtract(mLastestDetectedTime).Minutes >= 10 Then
                    Return Nothing
                End If
            End If
validiu:
            If processed = True Then
                If mTermMgr.IsProcessed(iu) Then
                    Return iu
                Else
                    Return Nothing
                End If
            Else
                If mTermMgr.IsInProcessing(iu) Then
                    Return iu
                Else
                    Return Nothing
                End If
            End If

        End Get
    End Property

    Protected Class DownloadGeneralinfo
        Public currentcnt As Integer
        Public maxcnt As Integer
    End Class

    Public Shared Event EPSIUDetected(ByVal iu As String)

    Public Sub New(ByVal backboneMnger As ParkriteClient, ByVal mTerm As TerminalManager)
        MyBase.New(mTerm)
        If mTermMgr.HasEps = False Then
            AddHandler mTermMgr.MessageHandledByEPS, AddressOf HandleEpsMsg
        End If
        backbone = backboneMnger
        Dim i As Integer
        For i = 0 To 13
            mIUSeasonTB.Columns.Add()
        Next
        AddHandler mTermMgr.VloopIsOn, AddressOf VloopIsOnHandler

        'Dim DetectProc As New Thread(AddressOf keepIUDetectionEx)
        'DetectProc.Start()
    End Sub

    Public Event StartReaderAction(ByVal iulabel As String, ByVal fee As Integer)

    Protected Overridable Sub VloopIsOnHandler()
        'StartIdentityAuthenticate()
    End Sub

    Public Function RealTimeEntity() As String
        'Dim iu As String = Nothing
        'Try
        '    iu = mTermMgr.HDManager.ReadIUFromSerialPort()
        '    If iu = "OFFLINE" Then
        '        iu = Nothing
        '    ElseIf iu = "1096000001" Then
        '        iu = Nothing
        '    End If
        'Catch ex As Exception

        'End Try

        'Return iu
    End Function

    'Protected Sub raiseIdentityAuthenticateSucceed(ByVal i As ITerminalService, ByVal ticket As String, ByVal runstat As RunningStatus)
    '    RaiseEvent IdentityAuthenticateSucceed(i, ticket, runstat)
    'End Sub

    'Protected Sub raiseIndentityAuthenticateFailed(ByVal i As ITerminalService)
    '    RaiseEvent IndentityAuthenticateFailed(i)
    'End Sub

    'Public Overridable Sub init() Implements ITerminalService.init

    'End Sub

    Public Overrides Sub PauseIdentityAuthenticate()
        mContinuousDetection = False
    End Sub

    Public Overrides Function selftest() As Parkrite.SystemShared.SelfTestResult
        Dim ret As SelfTestResult

        'If mTermMgr.HDManager.CreateAntenna(mTermMgr.AntennaID) = False Then
        '    Dim trans As New TransactionMsg
        '    trans.mLog = False
        '    If mTermMgr.HasEps Then
        '        trans.TransactionType = ComCodes.TransactionTypeSelections.CEHardWareSelfTestError
        '        ret = SelfTestResult.EPSERROR
        '    Else
        '        trans.TransactionType = ComCodes.TransactionTypeSelections.CEHardWareSelfTestFailed
        '        ret = SelfTestResult.FATALEPSERROR
        '    End If
        '    mTermMgr.SendMsgToSMC(trans)
        '    Return ret
        'End If
        Return SelfTestResult.FINE
    End Function


    'Public MustOverride ReadOnly Property ServiceType() As TypeOfService Implements ITerminalService.ServiceType


    Public Overrides Function StartIdentityAuthenticate(Optional ByVal autoactive As Boolean = True) As Boolean
        'Threading.Thread.CurrentThread.Priority = Threading.ThreadPriority.Highest
        'mDetectFailTimes = 0
        'Try
        '    mIULabel = mTermMgr.HDManager.ReadIUFromSerialPort()
        'Catch ex As Exception

        'End Try
        'If mIULabel = "OFFLINE" Then
        '    mIULabel = Nothing
        'ElseIf mIULabel = "1096000001" Then
        '    mIULabel = Nothing
        'End If

        'If IsNothing(mIULabel) = True Then
        '    mContinuousDetection = True
        '    mTermMgr.HDManager.DisplayLED("Reading IU ...", "")
        '    mTermMgr.HDManager.DisplayLCD("Reading IU ...")
        'Else
        '    If mTermMgr.IsProcessed(mIULabel) Then
        '        'Handle Processed Entity
        '        HandleProcessedEntity(mIULabel)
        '    ElseIf mTermMgr.IsInProcessing(mIULabel) Then
        '        'do nothing
        '        DoIfInProcessing(mIULabel)
        '    Else
        '        'Process iudetected
        '        If mTermMgr.CCTVEnabled = True Then
        '            mTermMgr.ReportIUTag(mIULabel)
        '        End If
        '        RaiseEvent IUDetected(mIULabel)
        '        TerminalSite.TerminalLog.Log("StartIdentityAuthenticate - Will process detected IU : " & mIULabel)
        '    End If
        'End If

        'mTermMgr.LastVLoopOnTime = Now

    End Function


    'Protected Sub RaiseEventStartReaderAction(ByVal iulabel As String, ByVal fee As Integer)
    '    mTermMgr.HDManager.OpenShutter()  'Added by Tan wei
    '    RaiseEvent StartReaderAction(iulabel, fee)
    'End Sub

    Protected Overridable Sub keepIUDetectionEx()
        'While True
        '    Try
        '        If mContinuousDetection = False Then
        '            If mTermMgr.HDManager.VloopStatus = Enumeration.LoopState.LOOPON And Now.Subtract(mTermMgr.LastVLoopOnTime).TotalSeconds >= 4 Then
        '                If mTermMgr.CurrentSystemMode = Enumeration.SystemMode.ACTIVE Then 'And Now.Subtract(mLastRestartAntennaTime).TotalMinutes > 1 Then
        '                    mTermMgr.LastVLoopOnTime = Now
        '                    'Dim iu As String
        '                    Try
        '                        mIULabel = mTermMgr.HDManager.ReadIUFromSerialPort()
        '                    Catch ex As Exception

        '                    End Try
        '                    If Not mIULabel Is Nothing Then
        '                        If mIULabel = "OFFLINE" Then
        '                            'Antenna offline
        '                            mOfflineTimes += 1
        '                            If mOfflineTimes = 5 Then
        '                                mTermMgr.HDManager.RestartAntenna()
        '                                'mLastRestartAntennaTime = Now
        '                            End If
        '                            If mTermMgr.HasCashCardSys Then
        '                                If mTermMgr.CashCardSys.StartIdentityAuthenticate() = True Then
        '                                    If mTermMgr.HDManager.VloopStatus = Enumeration.LoopState.LOOPON Then
        '                                        mTermMgr.HDManager.DisplayLED("Pls insert card", "")    'update 21/05/07
        '                                        mTermMgr.HDManager.DisplayLCD("Pls insert cashcard", 2)
        '                                    Else
        '                                        mTermMgr.HDManager.ClearLED()
        '                                    End If
        '                                End If
        '                            End If
        '                        ElseIf mIULabel = "1096000001" Then
        '                            mFaltyIUTimes += 1
        '                            If mFaltyIUTimes = 1 Then
        '                                Dim mm As New TransactionMsg
        '                                mm.TransactionType = ComCodes.TransactionTypeSelections.FaultyIU
        '                                mm.message1 = mFaltyIUTimes
        '                                mm.message2 = mTermMgr.TermID
        '                                mTermMgr.SendMsgToSMC(mm)
        '                            End If
        '                            If mTermMgr.HasCashCardSys Then
        '                                If mTermMgr.CashCardSys.StartIdentityAuthenticate() = True Then
        '                                    mTermMgr.HDManager.DisplayLED("Pls insert card", "")    'update 21/05/07
        '                                    mTermMgr.HDManager.DisplayLCD("Pls insert cashcard", 2)
        '                                End If
        '                            End If
        '                        Else    'iulabel has
        '                            mOfflineTimes = 0
        '                            mDetectFailTimes = 0
        '                            mFaltyIUTimes = 0
        '                            If mTermMgr.IsProcessed(mIULabel) Then
        '                                HandleProcessedEntity(mIULabel)
        '                            ElseIf mTermMgr.IsInProcessing(mIULabel) Then
        '                                DoIfInProcessing(mIULabel)
        '                            Else
        '                                If mTermMgr.CCTVEnabled = True Then
        '                                    mTermMgr.ReportIUTag(mIULabel)
        '                                End If
        '                                RaiseEvent IUDetected(mIULabel)
        '                                TerminalSite.TerminalLog.Log("keepIUDetectionEx(noncontinuous) - Will process detected IU : " & mIULabel)
        '                            End If
        '                        End If
        '                    Else    'iulabel is nothing
        '                        mOfflineTimes = 0
        '                        mFaltyIUTimes = 0
        '                        mDetectFailTimes += 1
        '                        'Trigger cashcard system if any
        '                        If mTermMgr.HasCashCardSys Then
        '                            If mTermMgr.CashCardSys.StartIdentityAuthenticate() = True Then
        '                                If mTermMgr.HDManager.VloopStatus = Enumeration.LoopState.LOOPON Then
        '                                    mTermMgr.HDManager.DisplayLED("Pls insert card", "") 'update 21/05/07
        '                                    mTermMgr.HDManager.DisplayLCD("Pls insert cashcard", 2)
        '                                Else
        '                                    mTermMgr.HDManager.ClearLED()
        '                                End If
        '                            End If
        '                        End If
        '                        If mDetectFailTimes = 5 Then
        '                            Dim mm As New TransactionMsg
        '                            mm.TransactionType = ComCodes.TransactionTypeSelections.AntennaCannotDetectIU
        '                            mm.message1 = mDetectFailTimes
        '                            mm.message2 = mTermMgr.TermID
        '                            mTermMgr.SendMsgToSMC(mm)
        '                        End If
        '                    End If
        '                End If
        '                Threading.Thread.Sleep(100)
        '            End If
        '        Else    'continuous detection is true
        '            If IsNothing(mTermMgr.HDManager) = False And mTermMgr.CurrentSystemMode = Enumeration.SystemMode.ACTIVE Then
        '                'If mTermMgr.HDManager.VloopStatus = Enumeration.LoopState.LOOPON And mTermMgr.CountOfEntitiesProcessing = 0 Then
        '                If mTermMgr.HDManager.VloopStatus = Enumeration.LoopState.LOOPON Then 'And Now.Subtract(mLastRestartAntennaTime).TotalMinutes > 1 Then
        '                    mTermMgr.LastVLoopOnTime = Now
        '                    Try
        '                        mIULabel = mTermMgr.HDManager.ReadIUFromSerialPort()
        '                    Catch ex As Exception

        '                    End Try
        '                    If Not mIULabel Is Nothing Then
        '                        If mIULabel = "OFFLINE" Then
        '                            'Antenna offline
        '                            mOfflineTimes += 1
        '                            If mOfflineTimes = 5 Then
        '                                mTermMgr.HDManager.RestartAntenna()
        '                                'mLastRestartAntennaTime = Now
        '                            End If
        '                            If mTermMgr.HasCashCardSys Then
        '                                If mTermMgr.CashCardSys.StartIdentityAuthenticate() = True Then
        '                                    If mTermMgr.HDManager.VloopStatus = Enumeration.LoopState.LOOPON Then
        '                                        mTermMgr.HDManager.DisplayLED("Pls insert card", "")    'update 21/05/07
        '                                        mTermMgr.HDManager.DisplayLCD("Pls insert cashcard", 2)
        '                                    Else
        '                                        mTermMgr.HDManager.ClearLED()
        '                                    End If
        '                                End If
        '                            End If
        '                        ElseIf mIULabel = "1096000001" Then
        '                            mFaltyIUTimes += 1
        '                            If mFaltyIUTimes = 1 Then
        '                                Dim mm As New TransactionMsg
        '                                mm.TransactionType = ComCodes.TransactionTypeSelections.FaultyIU
        '                                mm.message1 = mFaltyIUTimes
        '                                mm.message2 = mTermMgr.TermID
        '                                mTermMgr.SendMsgToSMC(mm)
        '                            End If
        '                            If mTermMgr.HasCashCardSys Then
        '                                If mTermMgr.CashCardSys.StartIdentityAuthenticate() = True Then
        '                                    mTermMgr.HDManager.DisplayLED("Pls insert card", "")    'update 21/05/07
        '                                    mTermMgr.HDManager.DisplayLCD("Pls insert cashcard", 2)
        '                                End If
        '                            End If
        '                        Else    'iulabel has
        '                            mOfflineTimes = 0
        '                            mDetectFailTimes = 0
        '                            mFaltyIUTimes = 0
        '                            mContinuousDetection = False
        '                            If mTermMgr.IsProcessed(mIULabel) Then
        '                                'Handle processed Entity
        '                                HandleProcessedEntity(mIULabel)
        '                            ElseIf mTermMgr.IsInProcessing(mIULabel) Then
        '                                DoIfInProcessing(mIULabel)
        '                            Else
        '                                If mTermMgr.CCTVEnabled = True Then
        '                                    mTermMgr.ReportIUTag(mIULabel)
        '                                End If
        '                                RaiseEvent IUDetected(mIULabel)
        '                                TerminalSite.TerminalLog.Log("keepIUDetectionEx(continuous) - Will process detected IU : " & mIULabel)
        '                            End If
        '                        End If
        '                    Else    'iulabel is nothing
        '                        mOfflineTimes = 0
        '                        mFaltyIUTimes = 0
        '                        mDetectFailTimes += 1
        '                        'Trigger cashcard system if any
        '                        If mTermMgr.HasCashCardSys Then
        '                            If mTermMgr.CashCardSys.StartIdentityAuthenticate() = True Then
        '                                If mTermMgr.HDManager.VloopStatus = Enumeration.LoopState.LOOPON Then
        '                                    mTermMgr.HDManager.DisplayLED("Pls insert card", "")    'update 21/05/07
        '                                    mTermMgr.HDManager.DisplayLCD("Pls insert cashcard", 2)
        '                                Else
        '                                    mTermMgr.HDManager.ClearLED()
        '                                End If
        '                            End If
        '                        End If
        '                        If mDetectFailTimes = 5 Then
        '                            Dim mm As New TransactionMsg
        '                            mm.TransactionType = ComCodes.TransactionTypeSelections.AntennaCannotDetectIU
        '                            mm.message1 = mDetectFailTimes
        '                            mm.message2 = mTermMgr.TermID
        '                            mTermMgr.SendMsgToSMC(mm)
        '                        End If
        '                    End If
        '                End If
        '            End If
        '        End If
        '    Catch ex As Exception
        '        Console.WriteLine("There is error in detection proc")
        '    End Try

        '    Threading.Thread.Sleep(500)
        'End While

        'Console.WriteLine("I will quit the keepIUDetection thread...")
    End Sub


    Protected Sub modifySeasonIUInfoDB(ByVal msg As TransactionMsg, ByVal transtype As ComCodes.TransactionTypeSelections)
        Dim theinfo As New ClsSeasonInfo
        Dim strr() As String = CType(msg.message1, String).Split(";"c)
        theinfo.Ticket = strr(0)
        theinfo.ValidFrom = DateTime.ParseExact(CType(msg.message2, String), "dd/MM/yyyy HH:mm:ss", Nothing)
        theinfo.ValidTo = DateTime.ParseExact(CType(msg.message3, String), "dd/MM/yyyy HH:mm:ss", Nothing)
        strr = CType(msg.message5, String).Split(";"c)
        theinfo.SeasonType = IIf(strr(2) = String.Empty, -1, CType(strr(2), Short))
        theinfo.GroupNo = IIf(strr(3) = String.Empty, -1, CType(strr(3), Short))
        theinfo.Freeze = CType(strr(4), Boolean)
        theinfo.IOCheck = CType(strr(5), Boolean)
        strr = CType(msg.message4, String).Split(";"c)
        Dim licenceNo As String = strr(0)

        UpdateSeason(theinfo)
        mTermMgr.DBManager.DoSeasonDBOps(theinfo, Parkrite.DatabaseManager.DBAction.UPDATE, Parkrite.DatabaseManager.DBSeasonDataType.IUSeason, licenceNo)

        mTermMgr.RemoveFromProcessing(theinfo.Ticket)
    End Sub

    Protected Sub modifySeasonIUPermitsDB(ByVal msg As TransactionMsg, ByVal transtype As ComCodes.TransactionTypeSelections)
        Dim theinfo As New ClsSeasonPermits
        theinfo.Id = CType(msg.message1, Integer)
        theinfo.Ticket = msg.message2
        theinfo.CarparkNo = CType(msg.message3, Integer)
        theinfo.SubCarparkNo = CType(msg.message4, Integer)
        
        UpdateSeasonPermits(theinfo)
        mTermMgr.DBManager.DoSeasonDBOps(theinfo, Parkrite.DatabaseManager.DBAction.UPDATE, Parkrite.DatabaseManager.DBSeasonDataType.IUSeasonPermit)

        mTermMgr.RemoveFromProcessing(theinfo.Ticket)
    End Sub

    Protected Sub modifyAntennaInfoDB(ByVal msg As TransactionMsg, ByVal transtype As ComCodes.TransactionTypeSelections)
        Dim tbtmp As New DataTable
        Dim i As Integer
        For i = 0 To 8
            tbtmp.Columns.Add()
        Next
        Dim rw As DataRow = tbtmp.NewRow
        Dim strr() As String = CType(msg.message1, String).Split(";"c)
        rw(0) = strr(0)
        rw(1) = strr(1)
        rw(2) = strr(2)
        strr = CType(msg.message2, String).Split(";"c)
        rw(3) = strr(0)
        rw(4) = strr(1)
        rw(5) = IIf(strr(2) = "", DBNull.Value, strr(2))
        tbtmp.Rows.Add(rw)
        If transtype = ComCodes.TransactionTypeSelections.DownloadAntennaInfo Then
            If mTermMgr.DBManager.AddAntennaConfigRecords(tbtmp) <> 1 Then
                Dim str As String = String.Format("Type: {0}, AntNo: {1}, CHUID: {2}", _
                                msg.TransactionType, rw(0), rw(5))
                mTermMgr.replyDownloadMsgError(msg.TransactionType, rw(0), str)
            Else
                Dim funct1 As New RetrieveFromDB(AddressOf mTermMgr.DBManager.RetrieveAllAntennaConfig)
                VerifyIfFinished(ComCodes.TransactionTypeSelections.DownloadAntennaInfo, ComCodes.TransactionTypeSelections.ReplyAntennaCount, funct1)
            End If
        Else
            If mTermMgr.DBManager.UpdateAntennaConfigRecords(tbtmp) <> 1 Then
                Dim str As String = String.Format("Type: {0}, AntNo: {1}, CHUID: {2}", _
                                msg.TransactionType, rw(0), rw(5))
                mTermMgr.replyDownloadMsgError(msg.TransactionType, rw(0), str)
            End If
        End If
    End Sub

    'Protected Sub AssembleSendHourlyExitTrans(ByVal termmgr As TerminalManager, _
    '                                          ByVal iu As String, _
    '                                          ByVal beforeBalance As Integer, _
    '                                          ByVal afterBanlance As Integer, _
    '                                          ByVal fee As Integer, _
    '                                          ByVal debitTime As String)
    '    Dim trans As New TransactionMsg
    '    trans.message1 = iu
    '    trans.message2 = termmgr.TermID
    '    trans.message3 = Now.ToString("dd/MM/yyyy HH:mm:ss")
    '    Dim strbuild As New Text.StringBuilder(5)
    '    strbuild.Append(beforeBalance)
    '    strbuild.Append(";")
    '    strbuild.Append(afterBanlance)
    '    strbuild.Append(";")
    '    strbuild.Append(fee)
    '    strbuild.Append(";")
    '    strbuild.Append(fee)
    '    trans.message4 = strbuild.ToString
    '    trans.message5 = debitTime
    '    Select Case Integer.Parse(Left(iu, 3))
    '        Case Is = 105
    '            trans.TransactionType = ComCodes.TransactionTypeSelections.ExitIUHourlyTaxi
    '            termmgr.AddTranAndSend(trans, ComCodes.TransactionTypeSelections.ExitIUHourlyTaxiRemoved)
    '        Case Is >= 150
    '            trans.TransactionType = ComCodes.TransactionTypeSelections.ExitIUHourlyLorry
    '            termmgr.AddTranAndSend(trans, ComCodes.TransactionTypeSelections.ExitIUHourlyLorryRemoved)
    '        Case Is < 100
    '            trans.TransactionType = ComCodes.TransactionTypeSelections.ExitIUHourlyMotocycle
    '            termmgr.AddTranAndSend(trans, ComCodes.TransactionTypeSelections.ExitIUHourlyMotocycleRemoved)
    '        Case Else
    '            trans.TransactionType = ComCodes.TransactionTypeSelections.ExitIUHourlyCar
    '            termmgr.AddTranAndSend(trans, ComCodes.TransactionTypeSelections.ExitIUHourlyCarRemoved)
    '    End Select
    'End Sub

    Protected Sub AssembleSendSeasonExitTrans(ByVal termmgr As TerminalManager, ByVal seas As IUSeasonInfo, ByVal seasrecord As RunningSeason)
        Dim trans As New TransactionMsg
        trans.message1 = seas.IULabel
        trans.message2 = termmgr.TermID
        trans.message3 = Now.ToString("dd/MM/yyyy HH:mm:ss")
        trans.message6 = seasrecord.EntryTime.ToString("dd/MM/yyyy HH:mm:ss")
        trans.mMulticast = True

        trans.TransactionType = ComCodes.TransactionTypeSelections.ExitIUSeason
        termmgr.AddTranAndSend(seas.IULabel, trans, ComCodes.TransactionTypeSelections.ExitIUSeasonRemoved)
    End Sub

    'Protected Sub AssembleSendHourlyEntryTrans(ByVal termmgr As TerminalManager, ByVal iu As String, Optional ByVal stat As RunningHourlyStatus = RunningHourlyStatus.ORIGINALHOURLY)
    '    Dim trans As New TransactionMsg
    '    trans.message1 = iu
    '    trans.message2 = termmgr.TermID
    '    trans.message3 = Now.ToString("dd/MM/yyyy HH:mm:ss")
    '    trans.message4 = CType(stat, Integer)
    '    trans.mMulticast = True
    '    'Dim termtype As DeviceServiceDefinition.DeviceType = termmgr.TerminalType
    '    Select Case Integer.Parse(Left(iu, 3))
    '        Case Is = 105
    '            'If termtype = NetworkReferences.DeviceServiceDefinition.DeviceType.Entry_Device Then
    '            trans.TransactionType = ComCodes.TransactionTypeSelections.EntryIUHourlyTaxi
    '            termmgr.AddTranAndSend(trans, ComCodes.TransactionTypeSelections.EntryIUHourlyTaxiRemoved)
    '            'ElseIf termtype = NetworkReferences.DeviceServiceDefinition.DeviceType.Exit_Device Then
    '            '    trans.TransactionType = ComCodes.TransactionTypeSelections.ExitIUHourlyTaxi
    '            '    termmgr.AddTranAndSend(trans, ComCodes.TransactionTypeSelections.ExitIUHourlyTaxiRemoved)
    '            'Else
    '            '    Throw New ApplicationException("The type terminal not supported!")
    '            'End If
    '        Case Is >= 150
    '            'If termtype = NetworkReferences.DeviceServiceDefinition.DeviceType.Entry_Device Then
    '            trans.TransactionType = ComCodes.TransactionTypeSelections.EntryIUHourlyLorry
    '            termmgr.AddTranAndSend(trans, ComCodes.TransactionTypeSelections.EntryIUHourlyLorryRemoved)
    '            'ElseIf termtype = NetworkReferences.DeviceServiceDefinition.DeviceType.Exit_Device Then
    '            '    trans.TransactionType = ComCodes.TransactionTypeSelections.ExitIUHourlyLorry
    '            '    termmgr.AddTranAndSend(trans, ComCodes.TransactionTypeSelections.ExitIUHourlyLorryRemoved)
    '            'Else
    '            '    Throw New ApplicationException("The type terminal not supported!")
    '            'End If

    '        Case Is < 100
    '            'If termtype = NetworkReferences.DeviceServiceDefinition.DeviceType.Entry_Device Then
    '            trans.TransactionType = ComCodes.TransactionTypeSelections.EntryIUHourlyMotorcycle
    '            termmgr.AddTranAndSend(trans, ComCodes.TransactionTypeSelections.EntryIUHourlyMotorcycleRemoved)
    '            'ElseIf termtype = NetworkReferences.DeviceServiceDefinition.DeviceType.Exit_Device Then
    '            '    trans.TransactionType = ComCodes.TransactionTypeSelections.ExitIUHourlyMotocycle
    '            '    termmgr.AddTranAndSend(trans, ComCodes.TransactionTypeSelections.ExitIUHourlyMotocycleRemoved)
    '            'Else
    '            '    Throw New ApplicationException("The type terminal not supported!")
    '            'End If
    '        Case Else
    '            'If termtype = NetworkReferences.DeviceServiceDefinition.DeviceType.Entry_Device Then
    '            trans.TransactionType = ComCodes.TransactionTypeSelections.EntryIUHourlyCar
    '            termmgr.AddTranAndSend(trans, ComCodes.TransactionTypeSelections.EntryIUHourlyCarRemoved)
    '            'ElseIf termtype = NetworkReferences.DeviceServiceDefinition.DeviceType.Exit_Device Then
    '            '    trans.TransactionType = ComCodes.TransactionTypeSelections.ExitIUHourlyCar
    '            '    termmgr.AddTranAndSend(trans, ComCodes.TransactionTypeSelections.ExitIUHourlyCarRemoved)
    '            'Else
    '            '    Throw New ApplicationException("The type terminal not supported!")
    '            'End If
    '    End Select
    'End Sub

    'Protected Overridable Function AssembleSendSeasonEntryTrans(ByVal termmgr As TerminalManager, ByVal seas As IUSeasonInfo) As Boolean
    '    Dim trans As New TransactionMsg
    '    trans.message1 = seas.IULabel
    '    trans.message2 = termmgr.TermID
    '    trans.message3 = Now.ToString("dd/MM/yyyy HH:mm:ss")
    '    trans.message4 = 0 'To prevent SMC runtime error
    '    trans.mLog = True

    '    'Multiple season
    '    If seas.GroupNo <> -1 Then
    '        Dim runningcnt As Integer = 0
    '        Dim seasinfos() As IUSeasonInfo = Nothing
    '        If termmgr.DBManager.RetrieveIUSeasonInfo(seas.GroupNo, seasinfos) = True Then
    '            For Each singleseas As IUSeasonInfo In seasinfos
    '                Dim hourlysig As RunningSeason
    '                If termmgr.GetRunningSeasonRecord(singleseas.IULabel, hourlysig) = True Then
    '                    runningcnt += 1
    '                End If
    '            Next
    '        End If

    '        If runningcnt >= 0 Then
    '            Dim sesgrp As SeasonGroup = Nothing
    '            If termmgr.DBManager.RetrieveSeasonGroup(seas.GroupNo, sesgrp) = True Then
    '                Dim countTrans As New TransactionMsg
    '                countTrans.mMulticast = False
    '                countTrans.mLog = False
    '                countTrans.TransactionType = ComCodes.TransactionTypeSelections.UpdateGroupSeasonCount
    '                countTrans.message1 = seas.IULabel
    '                If sesgrp.Threshold <= runningcnt Then
    '                    countTrans.message2 = runningcnt
    '                Else
    '                    countTrans.message2 = runningcnt + 1
    '                End If
    '                countTrans.message3 = seas.GroupNo
    '                mTermMgr.SendMsgToSMC(countTrans)
    '                If sesgrp.Threshold <= runningcnt Then
    '                    If mTermMgr.FullSignOn = True And mTermMgr.FullSignBuffer = 0 Then
    '                        'If mTermMgr.DBManager.IsTerminalBlockedByFull(mTermMgr.TermInfo.TerminalNo) = True And mTermMgr.FullSignBuffer = 0 Then
    '                        mTermMgr.HDManager.DisplayLED("Carpark Full", Now.ToString("dd/MM/yyyy HH:mm:ss"))
    '                        mTermMgr.HDManager.DisplayLCD("Carpark Full", 1)
    '                        mTermMgr.HDManager.DisplayLCD(Now.ToString("dd/MM/yyyy HH:mm:ss"), 2)
    '                        mTermMgr.SendSimpleErrorToSMC(seas.IULabel, ComCodes.TransactionTypeSelections.EntryIUCarparkFull)
    '                        Return False
    '                    ElseIf mParkingType = Enumeration.PARKINGTYPENUM.SEASON Then
    '                        mTermMgr.HDManager.DisplayLED("Season Only", Now.ToString("dd/MM/yyyy HH:mm:ss")) 'swap on  13/04/06
    '                        mTermMgr.HDManager.DisplayLCD("Season Only", 1)
    '                        mTermMgr.HDManager.DisplayLCD(Now.ToString("dd/MM/yyyy HH:mm:ss"), 2)
    '                        mTermMgr.SendSimpleErrorToSMC(seas.IULabel, ComCodes.TransactionTypeSelections.EntryIUNotAllowedDueToSeasonOnly)
    '                        Return False
    '                    Else
    '                        mTermMgr.HDManager.DisplayLED("Hourly entry", "Multiple season!")
    '                        mTermMgr.HDManager.DisplayLCD("Hourly entry", 1)
    '                        mTermMgr.HDManager.DisplayLCD("Multiple season!", 2)
    '                        mTermMgr.SendSimpleErrorToSMC(seas.IULabel, ComCodes.TransactionTypeSelections.EntryIUSeasonMultipleSeasons)
    '                        AssembleSendHourlyEntryTrans(termmgr, seas.IULabel, RunningHourlyStatus.MULTIPLESEASON) 'Treated it as an hourly
    '                        Return True
    '                    End If
    '                End If
    '            End If
    '        End If
    '    End If

    '    If seas.ValidFrom > Today Or seas.ValidTo < Today Then
    '        'expired
    '        'trans.TransactionType = ComCodes.TransactionTypeSelections.EntryIUSeasonExpired
    '        'termmgr.SendMsgToSMC(trans)
    '        If mTermMgr.FullSignOn = True And mTermMgr.FullSignBuffer = 0 Then
    '            'If mTermMgr.DBManager.IsTerminalBlockedByFull(mTermMgr.TermInfo.TerminalNo) = True And mTermMgr.FullSignBuffer = 0 Then
    '            mTermMgr.HDManager.DisplayLED("Carpark Full", Now.ToString("dd/MM/yyyy HH:mm:ss"))
    '            mTermMgr.HDManager.DisplayLCD("Carpark Full", 1)
    '            mTermMgr.HDManager.DisplayLCD(Now.ToString("dd/MM/yyyy HH:mm:ss"), 2)
    '            mTermMgr.SendSimpleErrorToSMC(seas.IULabel, ComCodes.TransactionTypeSelections.EntryIUCarparkFull)
    '            Return False
    '        ElseIf mParkingType = Enumeration.PARKINGTYPENUM.SEASON Then
    '            mTermMgr.HDManager.DisplayLED("Season Only", Now.ToString("dd/MM/yyyy HH:mm:ss")) 'swap on  13/04/06
    '            mTermMgr.HDManager.DisplayLCD("Season Only", 1)
    '            mTermMgr.HDManager.DisplayLCD(Now.ToString("dd/MM/yyyy HH:mm:ss"), 2)
    '            mTermMgr.SendSimpleErrorToSMC(seas.IULabel, ComCodes.TransactionTypeSelections.EntryIUNotAllowedDueToSeasonOnly)
    '            Return False
    '        Else
    '            mTermMgr.HDManager.DisplayLED("Hourly entry", "")
    '            mTermMgr.HDManager.DisplayLCD("Hourly entry", 1)
    '            'mTermMgr.HDManager.DisplayLCD(Now.ToString("dd/MM/yyyy HH:mm:ss"), 2)
    '            If seas.ValidFrom > Today Then
    '                mTermMgr.HDManager.DisplayLED("Hourly entry", "Season Invalid!") 'change on 13/04/06
    '                mTermMgr.HDManager.DisplayLCD("Season Invalid Yet!", 2)
    '                mTermMgr.SendSimpleErrorToSMC(seas.IULabel, ComCodes.TransactionTypeSelections.EntryIUSeasonInvalid)
    '                AssembleSendHourlyEntryTrans(termmgr, seas.IULabel, RunningHourlyStatus.SEASONINVALID) 'Treated it as an hourly
    '            ElseIf seas.ValidTo < Today Then
    '                mTermMgr.HDManager.DisplayLED("Hourly entry", "Season Expired!")
    '                mTermMgr.HDManager.DisplayLCD("Season Expired!", 2)
    '                mTermMgr.SendSimpleErrorToSMC(seas.IULabel, ComCodes.TransactionTypeSelections.EntryIUSeasonExpired)
    '                AssembleSendHourlyEntryTrans(termmgr, seas.IULabel, RunningHourlyStatus.SEASONEXPIRED) 'Treated it as an hourly
    '            End If
    '            Return True
    '        End If
    '    End If
    '    If seas.Freeze = True Then
    '        'freezed
    '        If mTermMgr.FullSignOn = True And mTermMgr.FullSignBuffer = 0 Then
    '            'If mTermMgr.DBManager.IsTerminalBlockedByFull(mTermMgr.TermInfo.TerminalNo) = True And mTermMgr.FullSignBuffer = 0 Then
    '            mTermMgr.HDManager.DisplayLED("Carpark Full", Now.ToString("dd/MM/yyyy HH:mm:ss"))
    '            mTermMgr.HDManager.DisplayLCD("Carpark Full", 1)
    '            mTermMgr.HDManager.DisplayLCD(Now.ToString("dd/MM/yyyy HH:mm:ss"), 2)
    '            mTermMgr.SendSimpleErrorToSMC(seas.IULabel, ComCodes.TransactionTypeSelections.EntryIUCarparkFull)
    '            Return False
    '        ElseIf mParkingType = Enumeration.PARKINGTYPENUM.SEASON Then
    '            mTermMgr.HDManager.DisplayLED("Season Only", Now.ToString("dd/MM/yyyy HH:mm:ss")) 'swap on  13/04/06
    '            mTermMgr.HDManager.DisplayLCD("Season Only", 1)
    '            mTermMgr.HDManager.DisplayLCD(Now.ToString("dd/MM/yyyy HH:mm:ss"), 2)
    '            mTermMgr.SendSimpleErrorToSMC(seas.IULabel, ComCodes.TransactionTypeSelections.EntryIUNotAllowedDueToSeasonOnly)
    '            Return False
    '        Else
    '            mTermMgr.HDManager.DisplayLED("Hourly entry", "Season Frozen!")
    '            mTermMgr.HDManager.DisplayLCD("Hourly entry", 1)
    '            mTermMgr.HDManager.DisplayLCD("Season Frozen!", 2)
    '            mTermMgr.SendSimpleErrorToSMC(seas.IULabel, ComCodes.TransactionTypeSelections.EntryIUSeasonFrozen)
    '            AssembleSendHourlyEntryTrans(termmgr, seas.IULabel, RunningHourlyStatus.SEASONFROZEN) 'Treated it as an hourly
    '            Return True
    '        End If
    '    End If
    '    If seas.IOCheck = True And termmgr.IOCheckBypass = False Then
    '        'search season running database to check if the season is there
    '        If True Then
    '            'do not let in
    '            trans.TransactionType = ComCodes.TransactionTypeSelections.EntryIUSeasonDoubleEntry
    '            termmgr.SendMsgToSMC(trans)
    '            'termmgr.SendMsgToSMC(trans)
    '        End If
    '        Return True
    '    End If

    '    Dim iuperm() As IUSeasonPermits
    '    Dim info As IUSeasonPermits
    '    If termmgr.DBManager.RetrieveIUSeasonPermit(seas.IULabel, termmgr.TermInfo.CarparkNo, 0, info) = True Then
    '        'trans.TransactionType = ComCodes.TransactionTypeSelections.EntryIUSeasonWrongCarpark
    '        'termmgr.SendMsgToSMC(trans)
    '        If mTermMgr.FullSignOn = True And mTermMgr.FullSignBuffer = 0 Then
    '            'If mTermMgr.DBManager.IsTerminalBlockedByFull(mTermMgr.TermInfo.TerminalNo) = True And mTermMgr.FullSignBuffer = 0 Then
    '            mTermMgr.HDManager.DisplayLED("Carpark Full", Now.ToString("dd/MM/yyyy HH:mm:ss"))
    '            mTermMgr.HDManager.DisplayLCD("Carpark Full", 1)
    '            mTermMgr.HDManager.DisplayLCD(Now.ToString("dd/MM/yyyy HH:mm:ss"), 2)
    '            mTermMgr.SendSimpleErrorToSMC(seas.IULabel, ComCodes.TransactionTypeSelections.EntryIUCarparkFull)
    '            Return False
    '        ElseIf mParkingType = Enumeration.PARKINGTYPENUM.SEASON Then
    '            mTermMgr.HDManager.DisplayLED("Season Only", Now.ToString("dd/MM/yyyy HH:mm:ss")) 'swap on  13/04/06
    '            mTermMgr.HDManager.DisplayLCD("Season Only", 1)
    '            mTermMgr.HDManager.DisplayLCD(Now.ToString("dd/MM/yyyy HH:mm:ss"), 2)
    '            mTermMgr.SendSimpleErrorToSMC(seas.IULabel, ComCodes.TransactionTypeSelections.EntryIUNotAllowedDueToSeasonOnly)
    '            Return False
    '        Else
    '            mTermMgr.HDManager.DisplayLED("Hourly entry", Now.ToString("dd/MM/yyyy HH:mm:ss"))
    '            mTermMgr.HDManager.DisplayLCD("Hourly entry", 1)
    '            AssembleSendHourlyEntryTrans(termmgr, seas.IULabel) 'Treated it as an hourly
    '            mTermMgr.SendSimpleErrorToSMC(seas.IULabel, ComCodes.TransactionTypeSelections.EntryIUCarparkFull)
    '            Return True
    '        End If
    '    Else
    '        Dim subcbn As Short = IIf(mTermMgr.TermInfo.TerminalType = Enumeration.TERMINALOPTION.ENTRYTERM, mTermMgr.TermInfo.DestinationSubCarpark, mTermMgr.TermInfo.SourceSubCarpark)
    '        If termmgr.DBManager.RetrieveIUSeasonPermit(seas.IULabel, termmgr.TermInfo.CarparkNo, subcbn, info) = True Then
    '            'trans.TransactionType = ComCodes.TransactionTypeSelections.EntryIUSeasonWrongSubCarpark
    '            'termmgr.SendMsgToSMC(trans)
    '            If mTermMgr.FullSignOn = True And mTermMgr.FullSignBuffer = 0 Then
    '                'If mTermMgr.DBManager.IsTerminalBlockedByFull(mTermMgr.TermInfo.TerminalNo) = True And mTermMgr.FullSignBuffer = 0 Then
    '                mTermMgr.HDManager.DisplayLED("Carpark Full", Now.ToString("dd/MM/yyyy HH:mm:ss"))
    '                mTermMgr.HDManager.DisplayLCD("Carpark Full", 1)
    '                mTermMgr.HDManager.DisplayLCD(Now.ToString("dd/MM/yyyy HH:mm:ss"), 2)
    '                mTermMgr.SendSimpleErrorToSMC(seas.IULabel, ComCodes.TransactionTypeSelections.EntryIUCarparkFull)
    '                Return False
    '            ElseIf mParkingType = Enumeration.PARKINGTYPENUM.SEASON Then
    '                mTermMgr.HDManager.DisplayLED("Season Only", Now.ToString("dd/MM/yyyy HH:mm:ss")) 'swap on  13/04/06
    '                mTermMgr.HDManager.DisplayLCD("Season Only", 1)
    '                mTermMgr.HDManager.DisplayLCD(Now.ToString("dd/MM/yyyy HH:mm:ss"), 2)
    '                mTermMgr.SendSimpleErrorToSMC(seas.IULabel, ComCodes.TransactionTypeSelections.EntryIUNotAllowedDueToSeasonOnly)
    '                Return False
    '            Else
    '                mTermMgr.HDManager.DisplayLED("Hourly entry", Now.ToString("dd/MM/yyyy HH:mm:ss"))
    '                mTermMgr.HDManager.DisplayLCD("Hourly entry", 1)
    '                mTermMgr.HDManager.DisplayLCD(Now.ToString("dd/MM/yyyy HH:mm:ss"), 2)
    '                ' mTermMgr.HDManager.DisplayLED("Hourly entry", 1)
    '                AssembleSendHourlyEntryTrans(termmgr, seas.IULabel) 'Treated it as an hourly
    '                If mTermMgr.AddOnOption.SubCPWithinSubCP = True Then
    '                    If mTermMgr.IsFromTerminal = True Then
    '                        Dim themsg As New TransactionMsg
    '                        themsg.TransactionType = ComCodes.TransactionTypeSelections.NotifySeasonEntryNonAccessedTerminal
    '                        themsg.message1 = seas.IULabel
    '                        themsg.message2 = Now.ToString("dd/MM/yyyy HH:mm:ss")
    '                        themsg.message3 = mTermMgr.TermInfo.TerminalNo
    '                        themsg.mLog = False
    '                        themsg.mMulticast = True
    '                        mTermMgr.SendMsgToSMC(themsg)
    '                    End If
    '                End If
    '                Return True
    '            End If
    '        End If
    '    End If

    '    Dim SeasonRecord As RunningSeason
    '    Dim res As Boolean = mTermMgr.GetRunningSeasonRecord(seas.IULabel, SeasonRecord)
    '    If res = True And seas.IOCheck = True Then
    '        mTermMgr.HDManager.DisplayLED("Double entry", "")
    '        mTermMgr.HDManager.DisplayLCD("Double entry")
    '        mTermMgr.SendSimpleErrorToSMC(seas.IULabel, ComCodes.TransactionTypeSelections.EntryIUSeasonDoubleEntry)
    '        Return False
    '    End If

    '    If mTermMgr.AddOnOption.SubCPWithinSubCP = True Then
    '        Dim isSpecialSeason = False
    '        If mTermMgr.IsToTerminal = True Then
    '            Dim avrd As AccessViolation
    '            If mTermMgr.DBManager.RetrieveAccessViolationRecord(seas.IULabel, avrd) = True Then
    '                isSpecialSeason = True
    '                Dim index As Short = mTermMgr.IsRelatedFromTerminal(avrd.TerminalNo)
    '                If index <> -1 Then
    '                    mTermMgr.DBManager.DeleteAccessViolationRecord(seas.IULabel)
    '                    Dim themsg1 As New TransactionMsg
    '                    themsg1.TransactionType = ComCodes.TransactionTypeSelections.NotifySeasonEntryAccessedTerminal
    '                    themsg1.message1 = seas.IULabel
    '                    themsg1.message2 = Now.ToString("dd/MM/yyyy HH:mm:ss")
    '                    themsg1.message3 = mTermMgr.TermInfo.TerminalNo
    '                    themsg1.message4 = avrd.TerminalNo
    '                    themsg1.message5 = avrd.EntryTime.ToString("dd/MM/yyyy HH:mm:ss")
    '                    themsg1.mLog = False
    '                    themsg1.mMulticast = True
    '                    mTermMgr.SendMsgToSMC(themsg1)
    '                    Dim termrel As TerminalRelation = mTermMgr.RelatedFromTerminal(index)
    '                    If termrel.Duration <> -1 Then
    '                        If Now.Subtract(avrd.EntryTime).TotalMinutes > termrel.Duration Then
    '                            If termrel.ChargeBy = mTermMgr.TermInfo.TerminalNo Then
    '                                'Debit here
    '                            Else
    '                                Dim themsg2 As New TransactionMsg
    '                                themsg2.message1 = seas.IULabel
    '                                themsg2.message2 = avrd.EntryTime.ToString("dd/MM/yyyy HH:mm:ss")
    '                                themsg2.message3 = themsg1.message2
    '                                themsg2.message4 = avrd.TerminalNo
    '                                themsg2.message5 = mTermMgr.TermInfo.TerminalNo
    '                                themsg2.message6 = termrel.ChargeBy
    '                                themsg2.TransactionType = ComCodes.TransactionTypeSelections.NotifySeasonAccessViolationDebit
    '                                themsg2.mLog = False
    '                                themsg2.mMulticast = True
    '                                mTermMgr.SendMsgToSMC(themsg2)
    '                            End If
    '                            Dim transExit As New TransactionMsg
    '                            Dim RSet As Integer = 0
    '                            Dim transtype1 As ComCodes.TransactionTypeSelections
    '                            Dim transtype2 As ComCodes.TransactionTypeSelections
    '                            Select Case Integer.Parse(Left(seas.IULabel, 3))
    '                                Case Is = 105
    '                                    RSet = mTermMgr.LocalRateSet.Taxi
    '                                    transtype1 = ComCodes.TransactionTypeSelections.ExitIUHourlyTaxi
    '                                    transtype2 = ComCodes.TransactionTypeSelections.ExitIUHourlyTaxiRemoved
    '                                Case Is >= 150
    '                                    RSet = mTermMgr.LocalRateSet.Lorry
    '                                    transtype1 = ComCodes.TransactionTypeSelections.ExitIUHourlyLorry
    '                                    transtype2 = ComCodes.TransactionTypeSelections.ExitIUHourlyLorryRemoved
    '                                Case Is < 100
    '                                    RSet = mTermMgr.LocalRateSet.Motorcycle
    '                                    transtype1 = ComCodes.TransactionTypeSelections.ExitIUHourlyMotocycle
    '                                    transtype2 = ComCodes.TransactionTypeSelections.ExitIUHourlyMotocycleRemoved
    '                                Case Else
    '                                    RSet = mTermMgr.LocalRateSet.Car
    '                                    transtype1 = ComCodes.TransactionTypeSelections.ExitIUHourlyCar
    '                                    transtype2 = ComCodes.TransactionTypeSelections.ExitIUHourlyCarRemoved
    '                            End Select
    '                            Dim thepaid As Integer = mTermMgr.CalculateFee(RSet, avrd.EntryTime, Now)
    '                            transExit.TransactionType = transtype1
    '                            transExit.message1 = seas.IULabel
    '                            transExit.message2 = mTermMgr.TermID & ";" & String.Empty
    '                            transExit.message3 = avrd.EntryTime.ToString("dd/MM/yyyy HH:mm:ss")
    '                            'Dim ss() As String = CType(msg1.message4, String).Split(";"c)
    '                            'trans.message4 = CType(msg1.message4, String) & ";" & ss(2)
    '                            'trans.message5 = typestrs(4) & ";" & msg1.message5
    '                            transExit.message6 = themsg1.message2
    '                            mTermMgr.AddTranAndSend(transExit, transtype2)
    '                        Else
    '                            Dim transExit As New TransactionMsg
    '                            transExit.TransactionType = ComCodes.TransactionTypeSelections.ExitIUHourlyFreePass
    '                            transExit.message1 = seas.IULabel
    '                            transExit.message2 = mTermMgr.TermID & ";" & String.Empty
    '                            transExit.message3 = avrd.EntryTime.ToString("dd/MM/yyyy HH:mm:ss")
    '                            transExit.message6 = themsg1.message2
    '                            transExit.message5 = 0
    '                            mTermMgr.AddTranAndSend(transExit, ComCodes.TransactionTypeSelections.ExitIUHourlyFreePassRemoved)
    '                        End If
    '                    End If
    '                End If
    '            End If
    '        End If
    '        If isSpecialSeason = False Then
    '            If mTermMgr.TermInfo.SourceSubCarpark <> -1 Then
    '                Dim transExit As New TransactionMsg
    '                transExit.TransactionType = ComCodes.TransactionTypeSelections.ExitIUSeason
    '                transExit.message1 = seas.IULabel
    '                transExit.message2 = mTermMgr.TermID
    '                transExit.message3 = Now.ToString("dd/MM/yyyy HH:mm:ss")
    '                transExit.message6 = Now.ToString("dd/MM/yyyy HH:mm:ss")
    '                transExit.mLog = True
    '                transExit.mMulticast = True
    '                mTermMgr.AddTranAndSend(transExit, ComCodes.TransactionTypeSelections.ExitIUSeason)
    '            End If
    '        End If
    '    End If

    '    trans.mMulticast = True
    '    mTermMgr.HDManager.DisplayLED("Season entry", Now.ToString("dd/MM/yyyy HH:mm:ss"))
    '    mTermMgr.HDManager.DisplayLCD("Season entry", 1)
    '    mTermMgr.HDManager.DisplayLCD(Now.ToString("dd/MM/yyyy HH:mm:ss"), 2)
    '    'mTermMgr.HDManager.DisplayLED("Season entry", 1)
    '    'If termmgr.TerminalType = NetworkReferences.DeviceServiceDefinition.DeviceType.Entry_Device Then
    '    If seas.SeasonType = -1 Then
    '        trans.TransactionType = ComCodes.TransactionTypeSelections.EntryIUSeason
    '        termmgr.AddTranAndSend(trans, ComCodes.TransactionTypeSelections.EntryIUSeasonRemoved)
    '    Else
    '        trans.TransactionType = ComCodes.TransactionTypeSelections.EntryIUSeasonFlexible
    '        termmgr.AddTranAndSend(trans, ComCodes.TransactionTypeSelections.EntryIUSeasonFlexibleRemoved)
    '    End If

    '    Return True

    '    'ElseIf termmgr.TerminalType = NetworkReferences.DeviceServiceDefinition.DeviceType.Exit_Device Then
    '    '    trans.TransactionType = ComCodes.TransactionTypeSelections.ExitIUSeason
    '    '    termmgr.AddTranAndSend(trans, ComCodes.TransactionTypeSelections.ExitIUSeasonRemoved)
    '    'End If

    'End Function

    'Protected Function PrepareHourly(ByVal mdeduct As Hashtable, ByVal iulabel As String, ByVal hourlyrd As RunningHourly) As Integer
    '    Dim typestr As String
    '    Dim fee As Integer
    '    Dim curtime As DateTime = Now
    '    Select Case Integer.Parse(Left(iulabel, 3))
    '        Case Is = 105
    '            Dim transtype1 As ComCodes.TransactionTypeSelections = ComCodes.TransactionTypeSelections.ExitIUHourlyTaxi
    '            Dim transtype2 As ComCodes.TransactionTypeSelections = ComCodes.TransactionTypeSelections.ExitIUHourlyTaxiRemoved
    '            typestr = CType(transtype1, Integer) & ";" & CType(transtype2, Integer) & ";" & curtime.ToString("dd/MM/yyyy HH:mm:ss") & ";" & hourlyrd.EntryTime.ToString("dd/MM/yyyy HH:mm:ss") & ";" & hourlyrd.Status
    '            fee = mTermMgr.CalculateFee(mTermMgr.LocalRateSet.Taxi, hourlyrd.EntryTime, curtime)
    '            mTermMgr.HDManager.DisplayLED("Fee = $" & String.Format("{0:f}", CDbl(fee / 100)), "")
    '            mTermMgr.HDManager.DisplayLCD("Fee = $" & String.Format("{0:f}", CDbl(fee / 100)), 1) 'changed
    '        Case Is >= 150
    '            Dim transtype1 As ComCodes.TransactionTypeSelections = ComCodes.TransactionTypeSelections.ExitIUHourlyLorry
    '            Dim transtype2 As ComCodes.TransactionTypeSelections = ComCodes.TransactionTypeSelections.ExitIUHourlyLorryRemoved
    '            typestr = CType(transtype1, Integer) & ";" & CType(transtype2, Integer) & ";" & curtime.ToString("dd/MM/yyyy HH:mm:ss") & ";" & hourlyrd.EntryTime.ToString("dd/MM/yyyy HH:mm:ss") & ";" & hourlyrd.Status
    '            fee = mTermMgr.CalculateFee(mTermMgr.LocalRateSet.Lorry, hourlyrd.EntryTime, curtime)
    '            mTermMgr.HDManager.DisplayLED("Fee = $" & String.Format("{0:f}", CDbl(fee / 100)), "")
    '            mTermMgr.HDManager.DisplayLCD("Fee = $" & String.Format("{0:f}", CDbl(fee / 100)), 1) 'changed
    '        Case Is < 100
    '            Dim transtype1 As ComCodes.TransactionTypeSelections = ComCodes.TransactionTypeSelections.ExitIUHourlyMotocycle
    '            Dim transtype2 As ComCodes.TransactionTypeSelections = ComCodes.TransactionTypeSelections.ExitIUHourlyMotocycleRemoved
    '            typestr = CType(transtype1, Integer) & ";" & CType(transtype2, Integer) & ";" & curtime.ToString("dd/MM/yyyy HH:mm:ss") & ";" & hourlyrd.EntryTime.ToString("dd/MM/yyyy HH:mm:ss") & ";" & hourlyrd.Status
    '            fee = mTermMgr.CalculateFee(mTermMgr.LocalRateSet.Motorcycle, hourlyrd.EntryTime, curtime)
    '            mTermMgr.HDManager.DisplayLED("Fee = $" & String.Format("{0:f}", CDbl(fee / 100)), "")
    '            mTermMgr.HDManager.DisplayLCD("Fee = $" & String.Format("{0:f}", CDbl(fee / 100)), 1)
    '        Case Else
    '            Dim transtype1 As ComCodes.TransactionTypeSelections = ComCodes.TransactionTypeSelections.ExitIUHourlyCar
    '            Dim transtype2 As ComCodes.TransactionTypeSelections = ComCodes.TransactionTypeSelections.ExitIUHourlyCarRemoved
    '            typestr = CType(transtype1, Integer) & ";" & CType(transtype2, Integer) & ";" & curtime.ToString("dd/MM/yyyy HH:mm:ss") & ";" & hourlyrd.EntryTime.ToString("dd/MM/yyyy HH:mm:ss") & ";" & hourlyrd.Status
    '            fee = mTermMgr.CalculateFee(mTermMgr.LocalRateSet.Car, hourlyrd.EntryTime, curtime)
    '            mTermMgr.HDManager.DisplayLED("Fee = $" & String.Format("{0:f}", CDbl(fee / 100)), "")
    '            mTermMgr.HDManager.DisplayLCD("Fee = $" & String.Format("{0:f}", CDbl(fee / 100)), 1)
    '    End Select

    '    If fee = 0 Then
    '        Dim transtype1 As ComCodes.TransactionTypeSelections = ComCodes.TransactionTypeSelections.ExitIUHourlyFreePass
    '        Dim transtype2 As ComCodes.TransactionTypeSelections = ComCodes.TransactionTypeSelections.ExitIUHourlyFreePassRemoved
    '        typestr = CType(transtype1, Integer) & ";" & CType(transtype2, Integer) & ";" & curtime.ToString("dd/MM/yyyy HH:mm:ss") & ";" & hourlyrd.EntryTime.ToString("dd/MM/yyyy HH:mm:ss") & ";" & hourlyrd.Status
    '    End If
    '    If mdeduct.Contains(iulabel) = False Then
    '        mdeduct.Add(iulabel, typestr)
    '    End If

    '    Return fee

    'End Function

    Protected Sub HandleEpsMsg(ByVal msg As TransactionMsg, ByVal fromIP As String)
        Select Case msg.TransactionType


            ''ZAW Commented to disable since Season Only, 25/08/2016
            'Case ComCodes.TransactionTypeSelections.EntryIUHourlyCar, _
            '    ComCodes.TransactionTypeSelections.EntryIUHourlyTaxi, _
            '    ComCodes.TransactionTypeSelections.EntryIUHourlyLorry, _
            '    ComCodes.TransactionTypeSelections.EntryIUHourlyMotorcycle, _
            '    ComCodes.TransactionTypeSelections.EntryAuthorizedVehicle


            '    Console.WriteLine(">>>>>> EntryIUHourlyCar...")
            '    ProcessRecvedEntryTransaction(msg, Enumeration.RunningStatus.ORIGINALHOURLY)
            '    If msg.FromIP = mTermMgr.SMCIPAddress Then
            '        Console.WriteLine(msg.TransactionType.ToString & " received from SMC" & msg.message1)
            '        TerminalSite.TerminalLog.Log(msg.TransactionType.ToString & " received from SMC" & msg.message1)
            '    End If
            '    mTermMgr.RemoveFromProcessed(msg.message1)

            '''''''''''''''''''''''''''''''''''''''''''''



            ''Original Comment
            'Dim tmp As New RunningHourly
            'tmp.HourlyNo = msg.message1
            'tmp.TerminalNo = msg.message2
            'tmp.HourlyType = HOURLYTYPEOPTION.NORMAL
            'tmp.EntryTime = DateTime.ParseExact(msg.message3, "dd/MM/yyyy HH:mm:ss", Nothing)
            'tmp.Status = CType(msg.message4, Integer)
            'Dim subcbn As Short = IIf(mTermMgr.TermInfo.TerminalType = Enumeration.TERMINALOPTION.ENTRYTERM, mTermMgr.TermInfo.DestinationSubCarpark, mTermMgr.TermInfo.SourceSubCarpark)
            'If mTermMgr.DBManager.IsTerminalInsideThisCarpark(tmp.TerminalNo, mTermMgr.TermInfo.CarparkNo, subcbn, True) Then
            '    Dim tmpt As Object = mTermMgr.DBManager.FindRunningHourlyRecord(tmp.HourlyNo, subcbn, mTermMgr.TermInfo.CarparkNo)
            '    If tmpt Is Nothing Then
            '        mTermMgr.DBManager.AddRunningHourlyRecords(tmp)
            '    Else
            '        tmp.ID = CType(tmpt, RunningHourly).ID
            '        mTermMgr.DBManager.UpdateRunningHourlyRecords(tmp)
            '    End If
            'End If
            'Case ComCodes.TransactionTypeSelections.EntryIUHourlyLorry
            '    Dim tmp As New RunningHourly
            '    tmp.HourlyNo = msg.message1
            '    tmp.TerminalNo = msg.message2
            '    tmp.HourlyType = HOURLYTYPEOPTION.NORMAL
            '    tmp.EntryTime = DateTime.ParseExact(msg.message3, "dd/MM/yyyy HH:mm:ss", Nothing)
            '    tmp.Status = CType(msg.message4, Integer)
            '    Dim subcbn As Short = IIf(mTermMgr.TermInfo.TerminalType = Enumeration.TERMINALOPTION.ENTRYTERM, mTermMgr.TermInfo.DestinationSubCarpark, mTermMgr.TermInfo.SourceSubCarpark)
            '    If mTermMgr.DBManager.IsTerminalInsideThisCarpark(tmp.TerminalNo, mTermMgr.TermInfo.CarparkNo, subcbn, True) Then
            '        Dim tmpt As Object = mTermMgr.DBManager.FindRunningHourlyRecord(tmp.HourlyNo, subcbn, mTermMgr.TermInfo.CarparkNo)
            '        If tmpt Is Nothing Then
            '            mTermMgr.DBManager.AddRunningHourlyRecords(tmp)
            '        Else
            '            tmp.ID = CType(tmpt, RunningHourly).ID
            '            mTermMgr.DBManager.UpdateRunningHourlyRecords(tmp)
            '        End If
            '    End If

            'Case ComCodes.TransactionTypeSelections.EntryIUHourlyMotorcycle
            '    Dim tmp As RunningHourly
            '    tmp.HourlyNo = msg.message1
            '    tmp.TerminalNo = msg.message2
            '    tmp.HourlyType = HOURLYTYPEOPTION.NORMAL
            '    tmp.EntryTime = DateTime.ParseExact(msg.message3, "dd/MM/yyyy HH:mm:ss", Nothing)
            '    tmp.Status = CType(msg.message4, Integer)
            '    Dim subcbn As Short = IIf(mTermMgr.TermInfo.TerminalType = Enumeration.TERMINALOPTION.ENTRYTERM, mTermMgr.TermInfo.DestinationSubCarpark, mTermMgr.TermInfo.SourceSubCarpark)
            '    If mTermMgr.DBManager.IsTerminalInsideThisCarpark(tmp.TerminalNo, mTermMgr.TermInfo.CarparkNo, subcbn, True) Then
            '        Dim tmpt As Object = mTermMgr.DBManager.FindRunningHourlyRecord(tmp.HourlyNo, subcbn, mTermMgr.TermInfo.CarparkNo)
            '        If tmpt Is Nothing Then
            '            mTermMgr.DBManager.AddRunningHourlyRecords(tmp)
            '        Else
            '            tmp.ID = CType(tmpt, RunningHourly).ID
            '            mTermMgr.DBManager.UpdateRunningHourlyRecords(tmp)
            '        End If
            '    End If

            'Case ComCodes.TransactionTypeSelections.EntryIUHourlyTaxi
            '    Dim tmp As RunningHourly
            '    tmp.HourlyNo = msg.message1
            '    tmp.TerminalNo = msg.message2
            '    tmp.HourlyType = HOURLYTYPEOPTION.NORMAL
            '    tmp.EntryTime = DateTime.ParseExact(msg.message3, "dd/MM/yyyy HH:mm:ss", Nothing)
            '    tmp.Status = CType(msg.message4, Integer)
            '    Dim subcbn As Short = IIf(mTermMgr.TermInfo.TerminalType = Enumeration.TERMINALOPTION.ENTRYTERM, mTermMgr.TermInfo.DestinationSubCarpark, mTermMgr.TermInfo.SourceSubCarpark)
            '    If mTermMgr.DBManager.IsTerminalInsideThisCarpark(tmp.TerminalNo, mTermMgr.TermInfo.CarparkNo, subcbn, True) Then
            '        Dim tmpt As Object = mTermMgr.DBManager.FindRunningHourlyRecord(tmp.HourlyNo, subcbn, mTermMgr.TermInfo.CarparkNo)
            '        If tmpt Is Nothing Then
            '            mTermMgr.DBManager.AddRunningHourlyRecords(tmp)
            '        Else
            '            tmp.ID = CType(tmpt, RunningHourly).ID
            '            mTermMgr.DBManager.UpdateRunningHourlyRecords(tmp)
            '        End If
            '    End If

            Case ComCodes.TransactionTypeSelections.EntryIUSeason
                ProcessRecvedEntryTransaction(msg, Enumeration.RunningStatus.ORIGINALSEASON, Enumeration.SEASONTOPTIONS.IU)
                mTermMgr.RemoveFromProcessed(msg.message1)
            Case ComCodes.TransactionTypeSelections.EntryIUSeasonFlexible
                ProcessRecvedEntryTransaction(msg, Enumeration.RunningStatus.SPECIALSEASON, Enumeration.SEASONTOPTIONS.IU)
                mTermMgr.RemoveFromProcessed(msg.message1)
                'Dim tmp As RunningSeason
                'tmp.SeasonNo = msg.message1
                'tmp.TerminalNo = msg.message2
                'tmp.SeasonOption = Enumeration.SEASONTOPTIONS.IU
                'tmp.SeasonType = -1
                'tmp.EntryTime = DateTime.ParseExact(msg.message3, "dd/MM/yyyy HH:mm:ss", Nothing)
                'Dim subcbn As Short = IIf(mTermMgr.TermInfo.TerminalType = Enumeration.TERMINALOPTION.ENTRYTERM, mTermMgr.TermInfo.DestinationSubCarpark, mTermMgr.TermInfo.SourceSubCarpark)
                'If mTermMgr.DBManager.IsTerminalInsideThisCarpark(tmp.TerminalNo, mTermMgr.TermInfo.CarparkNo, subcbn, True) Then
                '    Dim tmpt As Object = mTermMgr.DBManager.FindRunningSeasonRecord(tmp.SeasonNo, subcbn, mTermMgr.TermInfo.CarparkNo)
                '    If tmpt Is Nothing Then
                '        mTermMgr.DBManager.AddRunningSeasonRecords(tmp)
                '    Else
                '        tmp.ID = CType(tmpt, RunningSeason).ID
                '        mTermMgr.DBManager.UpdateRunningSeasonRecords(tmp)
                '    End If
                'End If

                'Case ComCodes.TransactionTypeSelections.EntryIUSeasonFlexible
                '    Dim tmp As RunningSeason
                '    tmp.SeasonNo = msg.message1
                '    tmp.TerminalNo = msg.message2
                '    tmp.SeasonOption = Enumeration.SEASONTOPTIONS.IU
                '    tmp.SeasonType = 1
                '    tmp.EntryTime = DateTime.ParseExact(msg.message3, "dd/MM/yyyy HH:mm:ss", Nothing)
                '    Dim subcbn As Short = IIf(mTermMgr.TermInfo.TerminalType = Enumeration.TERMINALOPTION.ENTRYTERM, mTermMgr.TermInfo.DestinationSubCarpark, mTermMgr.TermInfo.SourceSubCarpark)
                '    If mTermMgr.DBManager.IsTerminalInsideThisCarpark(tmp.TerminalNo, mTermMgr.TermInfo.CarparkNo, subcbn, True) Then
                '        Dim tmpt As Object = mTermMgr.DBManager.FindRunningSeasonRecord(tmp.SeasonNo, subcbn, mTermMgr.TermInfo.CarparkNo)
                '        If tmpt Is Nothing Then
                '            mTermMgr.DBManager.AddRunningSeasonRecords(tmp)
                '        Else
                '            tmp.ID = CType(tmpt, RunningSeason).ID
                '            mTermMgr.DBManager.UpdateRunningSeasonRecords(tmp)
                '        End If
                '    End If


                ' ZAW Commented to disable since Season Only, 25/08/2016
                'Case ComCodes.TransactionTypeSelections.ExitIUHourlyCar, _
                '    ComCodes.TransactionTypeSelections.ExitIUHourlyLorry, _
                '    ComCodes.TransactionTypeSelections.ExitIUHourlyMotocycle, _
                '    ComCodes.TransactionTypeSelections.ExitIUHourlyTaxi, _
                '    ComCodes.TransactionTypeSelections.ExitIUHourlyFreePass, _
                '     ComCodes.TransactionTypeSelections.ExitIUTicketComplimentary, _
                '     ComCodes.TransactionTypeSelections.ExitIUTicketValueRedemption, _
                '    ComCodes.TransactionTypeSelections.ExitIUComplimentary
                '    Console.WriteLine(">>>>>> Exit IUHourlyCar...")

                '    Dim runrdtermno As Integer = CType(msg.message2, String).Split(";")(0)
                '    If mTermMgr.IsTerminalInsideThisCarpark(runrdtermno) Then
                '        mTermMgr.RemoveRunning(msg.message1, True)
                '        mTermMgr.DBManager.DoRunningHourRecord(msg.message1, Parkrite.DatabaseManager.DBAction.DELETE)
                '        Select Case msg.TransactionType
                '            Case ComCodes.TransactionTypeSelections.ExitIUHourlyCar, _
                '                ComCodes.TransactionTypeSelections.ExitIUHourlyLorry, _
                '                ComCodes.TransactionTypeSelections.ExitIUHourlyMotocycle, _
                '                ComCodes.TransactionTypeSelections.ExitIUHourlyTaxi
                '                'Try to remove cashcard running record if exist
                '                Dim mess22 As String = CType(msg.message2, String)
                '                Dim mss22() As String = mess22.Split(";")
                '                If mss22.Length > 1 Then
                '                    mTermMgr.RemoveRunning(mss22(1), True)
                '                    mTermMgr.DBManager.DoRunningHourRecord(mss22(1), Parkrite.DatabaseManager.DBAction.DELETE)
                '                End If
                '        End Select
                '    End If
                '    If msg.FromIP = mTermMgr.SMCIPAddress Then
                '        Console.WriteLine(msg.TransactionType.ToString & " received from SMC" & msg.message1)
                '        TerminalSite.TerminalLog.Log(msg.TransactionType.ToString & " received from SMC" & msg.message1)
                '    End If
                '    mTermMgr.RemoveFromProcessed(msg.message1)
                '    If msg.TransactionType = ComCodes.TransactionTypeSelections.ExitIUComplimentary Then
                '        'mTermMgr.RemoveComplimentary(msg.message1)
                '        'mTermMgr.DBManager.DoSeasonDBOps(msg.message1, Parkrite.DatabaseManager.DBAction.DELETE, Parkrite.DatabaseManager.DBSeasonDataType.Complementary)
                '    End If
                '    If mTermMgr.AddOnOption.SubCPWithinSubCP = True Then
                '        If mTermMgr.TerminalType = NetworkReferences.DeviceServiceDefinition.DeviceType.Entry_Device Then
                '            If mTermMgr.IsInnerCarpark = True Then
                '                'Dim avrd As AccessViolation
                '                mTermMgr.RemoveAccessViolation(msg.message1)
                '                mTermMgr.DBManager.DoRunningAccessViolation(msg.message1, Parkrite.DatabaseManager.DBAction.DELETE)
                '            End If
                '        End If
                '    End If

                ''''''''''''''''''''''''End of comment''''''''''''''''''''''''''''''''''''''''''



            Case ComCodes.TransactionTypeSelections.ExitIUSeason, _
                ComCodes.TransactionTypeSelections.ExitIUSeasonFlexible
                Dim runrdtermno As Integer = CType(msg.message2, String).Split(";")(0)
                If mTermMgr.IsTerminalInsideThisCarpark(runrdtermno) Then
                    mTermMgr.RemoveRunning(msg.message1, False)
                    mTermMgr.DBManager.DoRunningSeasonRecord(msg.message1, Parkrite.DatabaseManager.DBAction.DELETE)
                    If msg.TransactionType = ComCodes.TransactionTypeSelections.ExitIUSeasonFlexible Then
                        Dim mess22 As String = CType(msg.message2, String)
                        Dim mss22() As String = mess22.Split(";")
                        If mss22.Length > 1 Then
                            mTermMgr.RemoveRunning(mss22(1), True)
                            mTermMgr.DBManager.DoRunningHourRecord(mss22(1), Parkrite.DatabaseManager.DBAction.DELETE)
                        End If
                    End If
                    If mTermMgr.AddOnOption.SubCPWithinSubCP = True Then
                        If mTermMgr.TermInfo.TerminalType = Enumeration.TERMINALOPTION.EXITTERM Then
                            If mTermMgr.IsRelatedFromTerminal(runrdtermno) <> -1 Then
                                Dim clssn As New ClsSeasonInfo
                                Dim rt As RunningStatus = IsSeason(msg.message1, clssn)
                                If rt <> Enumeration.RunningStatus.ORIGINALSEASON And rt <> Enumeration.RunningStatus.SPECIALSEASON Then
                                    Dim run As New RunningHourly
                                    run.EntryTime = Now
                                    run.HourlyNo = msg.message1
                                    run.HourlyType = Enumeration.HOURLYTYPEOPTION.NORMAL
                                    run.Status = CType(RunningHourlyStatus.SEASONACCESSVIOLATED, Integer)
                                    run.TerminalNo = runrdtermno
                                    mTermMgr.UpdateRunning(run)
                                    mTermMgr.DBManager.DoRunningHourRecord(run, Parkrite.DatabaseManager.DBAction.UPDATE)
                                Else
                                    Dim run As New RunningSeason
                                    run.EntryTime = Now
                                    run.SeasonNo = msg.message1
                                    run.SeasonOption = Enumeration.SEASONTOPTIONS.IU
                                    run.Status = 0
                                    run.SeasonType = -1
                                    run.TerminalNo = runrdtermno
                                    mTermMgr.UpdateRunning(run)
                                    mTermMgr.DBManager.DoRunningSeasonRecord(run, Parkrite.DatabaseManager.DBAction.UPDATE)
                                End If
                            End If
                        End If
                    End If
                End If
                mTermMgr.RemoveFromProcessed(msg.message1)
            Case ComCodes.TransactionTypeSelections.ExitIURedemptionByValue
                'Dim runrdtermno As Integer = CType(msg.message2, String).Split(";")(0)
                'If mTermMgr.IsTerminalInsideThisCarpark(runrdtermno) Then
                '    mTermMgr.RemoveRunning(msg.message1, True)
                '    mTermMgr.DBManager.DoRunningHourRecord(msg.message1, Parkrite.DatabaseManager.DBAction.DELETE)
                '    mTermMgr.RemoveRedemption(msg.message1)
                '    mTermMgr.DBManager.DoSeasonDBOps(msg.message1, Parkrite.DatabaseManager.DBAction.DELETE, Parkrite.DatabaseManager.DBSeasonDataType.Redemption)
                'End If
            Case ComCodes.TransactionTypeSelections.DownloadIUSeasonInfo
                modifySeasonIUInfoDB(msg, ComCodes.TransactionTypeSelections.DownloadIUSeasonInfo)
            Case ComCodes.TransactionTypeSelections.UpdateIUSeasonInfo
                modifySeasonIUInfoDB(msg, ComCodes.TransactionTypeSelections.UpdateIUSeasonInfo)
            Case ComCodes.TransactionTypeSelections.DownloadIUSeasonPermits
                modifySeasonIUPermitsDB(msg, ComCodes.TransactionTypeSelections.DownloadIUSeasonPermits)
            Case ComCodes.TransactionTypeSelections.UpdateIUSeasonPermits
                modifySeasonIUPermitsDB(msg, ComCodes.TransactionTypeSelections.UpdateIUSeasonPermits)
            Case ComCodes.TransactionTypeSelections.DownloadAntennaInfo
                modifyAntennaInfoDB(msg, ComCodes.TransactionTypeSelections.DownloadAntennaInfo)
            Case ComCodes.TransactionTypeSelections.UpdateAntennaInfo
                modifyAntennaInfoDB(msg, ComCodes.TransactionTypeSelections.UpdateAntennaInfo)
            Case ComCodes.TransactionTypeSelections.ClearAllIUSeasonInfo
                If mTermMgr.TerminalType <> NetworkReferences.DeviceServiceDefinition.DeviceType.UNCONFIGURED Then
                    mTermMgr.DBManager.DeleteAllIUSeasonInfo()
                End If
            Case ComCodes.TransactionTypeSelections.ClearAllIUSeasonPermits
                If mTermMgr.TerminalType <> NetworkReferences.DeviceServiceDefinition.DeviceType.UNCONFIGURED Then
                    mTermMgr.DBManager.DeleteAllIUSeasonPermits()
                End If
            Case ComCodes.TransactionTypeSelections.CheckIUSeasonCount
                mTermMgr.ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyIUSeasonCount, mTermMgr.DBManager.RetrieveAllIUSeasonInfo.Rows.Count)
            Case ComCodes.TransactionTypeSelections.CheckIUSeasonPermitsCount
                mTermMgr.ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyIUSeasonPermitsCount, mTermMgr.DBManager.RetrieveAllIUSeasonPermits.Rows.Count)
            Case ComCodes.TransactionTypeSelections.CheckAntennaCount
                mTermMgr.ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyAntennaCount, mTermMgr.DBManager.RetrieveAllAntennaConfig.Rows.Count)
            Case ComCodes.TransactionTypeSelections.DeleteIUSeasonInfo
                DeleteSeason(msg.message1)
                mTermMgr.DBManager.DoSeasonDBOps(msg.message1, Parkrite.DatabaseManager.DBAction.DELETE, Parkrite.DatabaseManager.DBSeasonDataType.IUSeason)
                'If mTermMgr.DBManager.DeleteIUSeasonInfo(msg.message1) <> 1 Then
                '    Dim str As String = String.Format("Type: {0}, IULabel: {1}", _
                '                        msg.TransactionType, msg.message1)
                '    mTermMgr.replyDownloadMsgError(msg.TransactionType, msg.message1, str)
                'End If
            Case ComCodes.TransactionTypeSelections.DeleteIUSeasonPermits
                DeleteSeasonPermits(msg.message2, msg.message4)
                mTermMgr.DBManager.DoSeasonDBOps(msg.message1, Parkrite.DatabaseManager.DBAction.DELETE, Parkrite.DatabaseManager.DBSeasonDataType.IUSeasonPermit)
                'If mTermMgr.DBManager.DeleteIUSeasonPermit(msg.message1) <> 1 Then
                '    Dim str As String = String.Format("Type: {0}, ID: {1}", _
                '                        msg.TransactionType, msg.message1)
                '    mTermMgr.replyDownloadMsgError(msg.TransactionType, msg.message1, str)
                'End If
            Case ComCodes.TransactionTypeSelections.DeleteAntennaInfo
                If mTermMgr.DBManager.DeleteAntennaInfo(msg.message1) <> 1 Then
                    Dim str As String = String.Format("Type: {0}, Antenna No: {1}", _
                                        msg.TransactionType, msg.message1)
                    mTermMgr.replyDownloadMsgError(msg.TransactionType, msg.message1, str)
                End If
            Case ComCodes.TransactionTypeSelections.ReplyIULabelFromSMC
                mTermMgr.ProcessResponseFromSMC(msg)
            Case ComCodes.TransactionTypeSelections.TotalIUSeasonCount, ComCodes.TransactionTypeSelections.TotalIUSeasonPermitsCount, _
                 ComCodes.TransactionTypeSelections.TotalAntennaCount
                Dim tmpcntx As New DownloadGeneralinfo
                tmpcntx.currentcnt = 0
                tmpcntx.maxcnt = CType(msg.message1, Integer)
                'SyncLock mDownloadGeneralInfo.SyncRoot
                mDownloadGeneralInfo(CType(msg.message2, ComCodes.TransactionTypeSelections)) = tmpcntx
            Case ComCodes.TransactionTypeSelections.ReplySeasonIUFromSMC
                mTermMgr.ProcessResponseSeasonIUFromSMC(msg)
        End Select
    End Sub

    Protected Shared Sub RaiseEPSIUDetected(ByVal iu As String)
        'Console.WriteLine("Detected IU :" & iu)
        RaiseEvent EPSIUDetected(iu)
    End Sub

    Protected Sub VerifyIfFinished(ByVal type1 As ComCodes.TransactionTypeSelections, ByVal type2 As ComCodes.TransactionTypeSelections, ByVal func As RetrieveFromDB)
        'Dim cntcnx As DownloadGeneralinfo = mDownloadGeneralInfo(type1)
        'Dim finished As Boolean
        'SyncLock cntcnx
        '    cntcnx.currentcnt += 1
        '    If cntcnx.currentcnt = cntcnx.maxcnt Then
        '        finished = True
        '    End If
        'End SyncLock
        'If finished = True Then
        '    mTermMgr.ReplyAllRecordsCount(type2, func.Invoke().Rows.Count)
        'End If
    End Sub

    Protected Overrides Sub AssemblyTransaction(ByVal ticket As String, ByVal runstat As RunningStatus, Optional ByVal runningInfo As Object = Nothing)
        'Not Implemented here
    End Sub

    Protected Overridable Sub DoIfInProcessing(ByVal iu As String)
        'Not Implemented here
    End Sub

    Protected Overridable Sub RaiseIUDetectedEvent(ByVal iu As String)
        RaiseEvent IUDetected(iu)
    End Sub

End Class
