Imports System.IO.Ports
Public Class SerialDevice
    Implements ICommunicatedevice

    Private mComport As SerialPort

    Public Event DataReceived(ByVal data As List(Of Byte), ByVal receivedTime As DateTime) Implements ICommunicatedevice.DataReceived

    Public Function Open(ByVal portnm As String) As ICommunicatedevice Implements ICommunicatedevice.Open
        mComport = My.Computer.Ports.OpenSerialPort(portnm, 19200, Parity.Even, 8, StopBits.One)
        AddHandler mComport.DataReceived, AddressOf ProcessReceivedData
        Return Me
    End Function

    Public Sub Write(ByVal Buffer() As Byte, ByVal offset As Integer, ByVal count As Integer) Implements ICommunicatedevice.Write
        mComport.Write(Buffer, offset, count)
    End Sub

    Private Sub ProcessReceivedData(ByVal sender As Object, ByVal e As SerialDataReceivedEventArgs)
        Dim tm As DateTime = Now
        Dim p As SerialPort = sender
        Dim hh As IO.BinaryReader = New IO.BinaryReader(p.BaseStream)

        Dim isEnd As Boolean = False
        Dim numofDEL As Byte = 0
        Dim b As Byte
        Dim aaa As New List(Of Byte)

        Do
            b = hh.ReadByte()
            If aaa.Count = 1 Then
                If b <> &H2 Then
                    aaa.Clear()
                    p.DiscardInBuffer()
                    Return
                End If
            End If
            If b = &H10 Then
                numofDEL += 1
                If numofDEL = 2 Then
                    numofDEL = 0
                Else
                    aaa.Add(b)
                End If
            Else
                If aaa.Count = 0 Then
                    p.DiscardInBuffer()
                    'Try
                    '    hh.ReadByte()
                    'Catch ex As Exception

                    'End Try
                    Console.WriteLine("I am out due to timeout")
                    Return
                End If
                If numofDEL = 1 Then
                    If b = &H3 Then
                        isEnd = True
                    Else
                        numofDEL = 0
                    End If
                End If
                aaa.Add(b)
            End If
        Loop Until isEnd = True

        If aaa.Count < 7 Then
            Return
        End If
        aaa.Add(p.ReadByte)
        aaa.Add(p.ReadByte)

        RaiseEvent DataReceived(aaa, tm)
    End Sub

    Public Sub Close() Implements ICommunicatedevice.Close
        mComport.Close()
    End Sub

End Class
