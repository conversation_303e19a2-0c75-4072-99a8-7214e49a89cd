Imports Parkrite.SystemShared
Imports Parkrite.SystemShared.Messaging

Public MustInherit Class BasicService
    Implements ITerminalService
    Protected mSeasonHash As New Hashtable
    Protected mTermMgr As TerminalManager

    Public Sub New(ByVal term As TerminalManager)
        mTermMgr = term
    End Sub

    Public Function CanProceed() As Boolean Implements ITerminalService.CanProceed

    End Function

    Public Function CheckMultipleUsage() As Boolean Implements ITerminalService.CheckMultipleUsage

    End Function

    Public Function Identity() As String Implements ITerminalService.Identity

    End Function

    Public Event IdentityAuthenticateSucceed(ByVal service As ITerminalService, ByVal ticket As String, ByVal runstat As Parkrite.SystemShared.Enumeration.RunningStatus) Implements ITerminalService.IdentityAuthenticateSucceed

    Public Event IndentityAuthenticateFailed(ByVal service As ITerminalService) Implements ITerminalService.IndentityAuthenticateFailed

    Public MustOverride Sub init() Implements ITerminalService.init

    Public Function IsSeason(ByVal ticket As String, ByRef seasoninfo As Parkrite.SystemShared.GlobalVariables.ClsSeasonInfo) As Parkrite.SystemShared.Enumeration.RunningStatus Implements ITerminalService.IsSeason
        SyncLock mSeasonHash.SyncRoot
            If mSeasonHash.Contains(ticket) = True Then
                seasoninfo = mSeasonHash(ticket)
            Else
                Dim flag As Boolean = False
                If mTermMgr.PartialSeasonDownload Then
                    If mTermMgr.CheckSeasonMotorCycleIUfromSMC AndAlso Integer.Parse(Left(ticket, 3)) < 100 AndAlso Integer.Parse(Left(ticket, 3)) > 4 Then
                        flag = mTermMgr.ProcessRequestSeasonIUFromSMC(ticket, ComCodes.TransactionTypeSelections.RequestSeasonIUFromSMC, ComCodes.TransactionTypeSelections.RequestSeasonIUFromSMC, seasoninfo)
                    ElseIf mTermMgr.CheckSeasonCarIUfromSMC = True Then
                        flag = mTermMgr.ProcessRequestSeasonIUFromSMC(ticket, ComCodes.TransactionTypeSelections.RequestSeasonIUFromSMC, ComCodes.TransactionTypeSelections.RequestSeasonIUFromSMC, seasoninfo)
                    End If
                End If
                If flag = False Then
                    IsSeason = Enumeration.RunningHourlyStatus.ORIGINALHOURLY
                End If
            End If
        End SyncLock

        If IsNothing(seasoninfo) = False Then
            Dim countTrans As New TransactionMsg
            countTrans.mMulticast = False
            countTrans.mLog = False
            countTrans.TransactionType = ComCodes.TransactionTypeSelections.UpdateGroupSeasonCount
            countTrans.message1 = seasoninfo.Ticket
            countTrans.message3 = seasoninfo.GroupNo

            Dim runningcnt As Integer = 0

            'Multiple season
            If seasoninfo.GroupNo <> -1 Then
                If mTermMgr.TerminalType = NetworkReferences.DeviceServiceDefinition.DeviceType.Entry_Device Then
                    Dim seasinfos As ArrayList = GetSeasonsWithSameGrpNo(seasoninfo.GroupNo)
                    If seasinfos.Contains(ticket) Then
                        seasinfos.Remove(ticket)
                    End If
                    If seasinfos.Count > 0 Then
                        For Each singleseas As String In seasinfos
                            Dim hourlysig As New RunningSeason
                            If mTermMgr.GetRunningSeasonRecord(singleseas, hourlysig) = True Then
                                If singleseas <> ticket Then
                                    runningcnt += 1
                                End If
                            End If
                        Next
                    End If

                    If runningcnt >= 0 Then
                        Dim sesgrp As ClsSeasonGroup = Nothing
                        If mTermMgr.GetGroupSeasonInfo(seasoninfo.GroupNo, sesgrp) = True Then
                            If sesgrp.Threshold <= runningcnt Then
                                countTrans.message2 = runningcnt
                                mTermMgr.SendMsgToSMC(countTrans)
                                Return Enumeration.RunningStatus.MULTIPLESEASON
                            End If
                        End If
                    End If
                Else
                    Dim hourlysig As New RunningHourly
                    If mTermMgr.GetRunningHourlyRecord(ticket, hourlysig) = True Then
                        If CType(hourlysig.Status, RunningStatus) = Enumeration.RunningStatus.MULTIPLESEASON Then
                            Return Enumeration.RunningStatus.MULTIPLESEASON
                        End If
                    End If
                End If
            End If

            If seasoninfo.ValidFrom > Today Then
                Return Enumeration.RunningStatus.SEASONINVALID
            ElseIf seasoninfo.ValidTo < Today Then
                Return Enumeration.RunningStatus.SEASONEXPIRED
            End If

            If seasoninfo.Freeze = True Then
                Return Enumeration.RunningStatus.SEASONFROZEN
            End If

            If mTermMgr.TermInfo.CarparkNo = seasoninfo.CarparkNo Then
                Return Enumeration.RunningStatus.SEASONACCESSVIOLATED
            Else
                Dim subcpno As Short = IIf(mTermMgr.TerminalType = NetworkReferences.DeviceServiceDefinition.DeviceType.Entry_Device, mTermMgr.TermInfo.DestinationSubCarpark, _
                        mTermMgr.TermInfo.SourceSubCarpark)
                If seasoninfo.Accesses.Contains(subcpno) = True Then
                    Return Enumeration.RunningStatus.SEASONACCESSVIOLATED
                End If
            End If

            If seasoninfo.GroupNo <> -1 Then
                If mTermMgr.TerminalType = NetworkReferences.DeviceServiceDefinition.DeviceType.Entry_Device Then
                    countTrans.message2 = runningcnt + 1
                Else
                    If runningcnt > 0 Then
                        countTrans.message2 = runningcnt - 1
                    Else
                        countTrans.message2 = 0
                    End If
                End If

                mTermMgr.SendMsgToSMC(countTrans)
            End If

            If seasoninfo.SeasonType <> -1 Then
                Return Enumeration.RunningStatus.SPECIALSEASON
            Else
                Return Enumeration.RunningStatus.ORIGINALSEASON
            End If
        End If
    End Function

    Public MustOverride Sub PauseIdentityAuthenticate() Implements ITerminalService.PauseIdentityAuthenticate


    Public MustOverride Function selftest() As Parkrite.SystemShared.Enumeration.SelfTestResult Implements ITerminalService.selftest


    Public MustOverride ReadOnly Property ServiceType() As TypeOfService Implements ITerminalService.ServiceType

    Public MustOverride Function StartIdentityAuthenticate(Optional ByVal autoactive As Boolean = True) As Boolean Implements ITerminalService.StartIdentityAuthenticate

    Public Overloads Sub UpdateSeason(ByVal row As ClsSeasonInfo) Implements ITerminalService.UpdateSeason
        SyncLock mSeasonHash.SyncRoot
            If mSeasonHash.Contains(row.Ticket) Then
                mSeasonHash.Remove(row.Ticket)
            End If
            'Dim a As New ClsSeasonInfo
            'a.Ticket = row(0)
            'a.ValidFrom = row("ValidFrom")
            'a.ValidTo = row("ValidTo")
            'a.SeasonType = IIf(row("SeasonType") Is DBNull.Value, -1, row("SeasonType"))
            'a.GroupNo = IIf(row("GroupNo") Is DBNull.Value, -1, row("GroupNo"))
            'a.Freeze = row("Freeze")
            'a.IOCheck = row("IOCheck")
            mSeasonHash(row.Ticket) = row
        End SyncLock
    End Sub

    Protected Sub raiseIdentityAuthenticateSucceed(ByVal i As ITerminalService, ByVal ticket As String, ByVal runstat As RunningStatus)
        RaiseEvent IdentityAuthenticateSucceed(i, ticket, runstat)
    End Sub

    Protected Sub ProcessRecvedEntryTransaction(ByVal trans As TransactionMsg, ByVal stat As RunningStatus, Optional ByVal soption As SEASONTOPTIONS = Nothing)
        Try
            Select Case stat
                Case Enumeration.RunningStatus.ORIGINALSEASON, Enumeration.RunningStatus.SPECIALSEASON
                    Dim tmp As New RunningSeason
                    tmp.SeasonNo = trans.message1
                    tmp.TerminalNo = trans.message2
                    tmp.SeasonOption = soption
                    tmp.SeasonType = IIf(stat = Enumeration.RunningStatus.ORIGINALSEASON, -1, trans.message5)
                    tmp.EntryTime = DateTime.ParseExact(trans.message3, "dd/MM/yyyy HH:mm:ss", Nothing)
                    If mTermMgr.IsTerminalInsideThisCarpark(tmp.TerminalNo) Then
                        If mTermMgr.AddOnOption.SubCPWithinSubCP = True Then
                            If mTermMgr.TerminalType = NetworkReferences.DeviceServiceDefinition.DeviceType.Exit_Device Then
                                If mTermMgr.IsInnerCarpark = False And mTermMgr.IsInnerCarpark(tmp.TerminalNo) = True Then
                                    mTermMgr.RemoveRunning(trans.message1, True)
                                    mTermMgr.DBManager.DoRunningHourRecord(trans.message1, Parkrite.DatabaseManager.DBAction.DELETE)
                                    mTermMgr.RemoveRunning(trans.message1, False)
                                    mTermMgr.DBManager.DoRunningSeasonRecord(trans.message1, Parkrite.DatabaseManager.DBAction.DELETE)
                                    Return
                                End If
                            End If
                        End If
                        mTermMgr.UpdateRunning(tmp)
                        mTermMgr.RemoveRunning(tmp.SeasonNo, True)
                        ''Update Database
                        mTermMgr.DBManager.DoRunningSeasonRecord(tmp, Parkrite.DatabaseManager.DBAction.UPDATE)
                        'mTermMgr.DBManager.DoRunningHourRecord(tmp.SeasonNo, Parkrite.DatabaseManager.DBAction.DELETE)
                    End If
                Case Else
                    Dim tmp As New RunningHourly
                    tmp.HourlyNo = trans.message1
                    tmp.TerminalNo = trans.message2
                    tmp.HourlyType = HOURLYTYPEOPTION.NORMAL
                    tmp.EntryTime = DateTime.ParseExact(trans.message3, "dd/MM/yyyy HH:mm:ss", Nothing)
                    If Not trans.message4 Is DBNull.Value Then
                        tmp.Status = CType(trans.message4, Integer)
                    Else
                        tmp.Status = 0
                    End If

                    'tmp.Status = CType(trans.message4, Integer)

                    If mTermMgr.IsTerminalInsideThisCarpark(tmp.TerminalNo) Then
                        mTermMgr.UpdateRunning(tmp)
                        ''Update Database
                        mTermMgr.DBManager.DoRunningHourRecord(tmp, Parkrite.DatabaseManager.DBAction.UPDATE)
                    Else
                        If mTermMgr.AddOnOption.SubCPWithinSubCP = True And CType(tmp.Status, RunningStatus) = Enumeration.RunningStatus.SEASONACCESSVIOLATED Then
                            If mTermMgr.TerminalType = NetworkReferences.DeviceServiceDefinition.DeviceType.Entry_Device Then
                                If mTermMgr.IsInnerCarpark = True Then
                                    If mTermMgr.IsToTerminal = True Then
                                        If mTermMgr.IsRelatedFromTerminal(tmp.TerminalNo) <> -1 Then
                                            Dim clss As New ClsSeasonInfo
                                            Dim rt As RunningStatus = IsSeason(tmp.HourlyNo, clss)
                                            If rt = Enumeration.RunningStatus.ORIGINALSEASON Then
                                                Dim avrd As AccessViolation
                                                avrd.EntryTime = tmp.EntryTime
                                                avrd.RelationID = -1
                                                avrd.TerminalNo = tmp.TerminalNo
                                                avrd.TicketNo = tmp.HourlyNo
                                                mTermMgr.UpdateAccessViolation(avrd)
                                                mTermMgr.DBManager.DoRunningAccessViolation(avrd, Parkrite.DatabaseManager.DBAction.UPDATE)
                                            End If
                                        End If
                                    End If
                                End If
                            End If
                        End If
                    End If
            End Select
        Catch ex As Exception
            TerminalSite.ErrorLog.Log("ProcessRecvedEntryTransaction " & ex.Message)
        End Try
    End Sub

    Protected Sub ProcessRecvedExitTransaction(ByVal trans As TransactionMsg)

    End Sub

    Protected MustOverride Sub AssemblyTransaction(ByVal ticket As String, ByVal runstat As RunningStatus, Optional ByVal runningInfo As Object = Nothing)

    Protected Overridable Sub HandleProcessedEntity(ByVal ticket As String)

    End Sub

    Public Overloads Sub UpdateSeason(ByVal rw As System.Data.DataRow) Implements ITerminalService.UpdateSeason
        Dim strIU As String
        SyncLock mSeasonHash.SyncRoot
            If mSeasonHash.Contains(rw(0)) Then
                mSeasonHash.Remove(rw(0))
            End If
            Dim a As New ClsSeasonInfo
            a.Ticket = rw(0)
            a.ValidFrom = rw("ValidFrom")
            a.ValidTo = rw("ValidTo")
            a.SeasonType = IIf(rw("SeasonType") Is DBNull.Value, -1, rw("SeasonType"))
            a.GroupNo = IIf(rw("GroupNo") Is DBNull.Value, -1, rw("GroupNo"))
            a.Freeze = rw("Freeze")
            a.IOCheck = rw("IOCheck")
            mSeasonHash(rw(0)) = a

            'mSeasonHash(a.Ticket) = a 'Malbinda 2014-10-10
            strIU = rw(0)

            'Console.WriteLine("UpdateSeason: - " & strIU)
            mTermMgr.DBManager.UpdateSeasonAuthorizeIU(strIU)
        End SyncLock
    End Sub

    Public Overloads Sub UpdateSeasonPermits(ByVal rw As Parkrite.SystemShared.GlobalVariables.ClsSeasonPermits) Implements ITerminalService.UpdateSeasonPermits
        SyncLock mSeasonHash.SyncRoot
            If mSeasonHash.Contains(rw.Ticket) Then
                Dim aa As ClsSeasonInfo = mSeasonHash(rw.Ticket)
                If aa.Accesses.Contains(rw.SubCarparkNo) = False Then
                    aa.Accesses.Add(rw.SubCarparkNo)
                End If
            End If
        End SyncLock
    End Sub

    Public Overloads Sub UpdateSeasonPermits(ByVal rw As System.Data.DataRow) Implements ITerminalService.UpdateSeasonPermits
        SyncLock mSeasonHash.SyncRoot
            If mSeasonHash.Contains(rw(1)) Then
                Dim aa As ClsSeasonInfo = mSeasonHash(rw(1))
                Dim vl As Short = CType(rw(3), Short)
                If aa.Accesses.Contains(vl) = False Then
                    aa.Accesses.Add(vl)
                End If
            End If
        End SyncLock
    End Sub

    Public Sub DeleteSeason(ByVal ticket As String) Implements ITerminalService.DeleteSeason
        SyncLock mSeasonHash.SyncRoot
            If mSeasonHash.Contains(ticket) = True Then
                mSeasonHash.Remove(ticket)
            End If
        End SyncLock
    End Sub

    Public Sub DeleteSeasonPermits(ByVal ticket As String, ByVal subcpno As Short) Implements ITerminalService.DeleteSeasonPermits
        SyncLock mSeasonHash.SyncRoot
            If mSeasonHash.Contains(ticket) = True Then
                Dim aa As ClsSeasonInfo = mSeasonHash(ticket)
                If aa.Accesses.Contains(subcpno) = True Then
                    aa.Accesses.Remove(subcpno)
                End If
            End If
        End SyncLock
    End Sub

    Protected Function GetSeasonsWithSameGrpNo(ByVal grpno As Short) As ArrayList
        Dim result As New ArrayList
        SyncLock mSeasonHash.SyncRoot
            For Each key As String In mSeasonHash.Keys
                If CType(mSeasonHash(key), ClsSeasonInfo).GroupNo = grpno Then
                    result.Add(key)
                End If
            Next
        End SyncLock
        Return result
    End Function

    Public ReadOnly Property SeasonCount() As Object Implements ITerminalService.SeasonCount
        Get
            SyncLock mSeasonHash.SyncRoot
                SeasonCount = mSeasonHash.Count
            End SyncLock
        End Get
    End Property

    Public Sub ClearSeason() Implements ITerminalService.ClearSeason
        SyncLock mSeasonHash.SyncRoot
            mSeasonHash.Clear()
        End SyncLock
    End Sub
End Class
