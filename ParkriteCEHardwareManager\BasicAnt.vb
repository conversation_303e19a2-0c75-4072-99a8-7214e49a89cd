Imports System.IO.Ports
Imports System.Threading
Public Class BasicAnt
#Region "Delegate Definition"
    Private Delegate Sub executeRun()
#End Region
#Region "Private Variables"
    Private mSendQueryCmd As New List(Of Byte)
    Private mDevice As ICommunicatedevice
    Private mComportName As String
    Private mSendThrd As Thread
    Private mStopSend As Boolean = False
    Private mPauseSend As Boolean = False
    Private mOldIU As String = String.Empty
    Private mLastDetectedTime As DateTime
    Private mWaithandle As New AutoResetEvent(False)
    Protected mVloopOn As Boolean = False
    Protected mNloopOn As Boolean = False
    Private mBarrierOpen As Boolean = False
    Private mLastReceivedTime As DateTime
    Private mOnline As Boolean = False
    Private mID As Short
    Protected mJustStart As Boolean = True
    Private mName As String
    Private mLastLoopChangedTime As DateTime
    Private cnt As Integer
    Protected mLevelIsLow As Boolean = False
    Protected mEvtLevelIsLow As Threading.AutoResetEvent
    Protected mLockEvtLevelLow As New Object
    Private mProtocol As String
    Private mRemotePort As String
    Private mDetectInterval As Integer

#End Region

#Region "Events and others"
    Public Event IUDetected(ByVal sender As BasicAnt, ByVal iu As String, ByVal vlpon As Boolean)
    Public Event VloopON(ByVal sender As BasicAnt, ByVal iu As String)
    Public Event VloopOFF(ByVal sender As BasicAnt)
    Public Event NloopON(ByVal sender As BasicAnt)
    Public Event NloopOFF(ByVal sender As BasicAnt)
    Public Event BarrierOpen(ByVal sender As BasicAnt)
    Public Event BarrierClose(ByVal sender As BasicAnt)
    Public Event OnLine(ByVal sender As BasicAnt)
    Public Event OffLine(ByVal sender As BasicAnt)
    Public Event FaultyIUDetected(ByVal sender As BasicAnt, ByVal iu As String)
    Protected Event GetAntDataOK()
    Protected Event GetAntDataNAK()
    Protected Event SetAntDataOK()
    Protected Event SetAntDataNAK()
    Protected Event SetDONAK()
#End Region

#Region "Public Properties"
    Public ReadOnly Property IsVloopOn() As Boolean
        Get
            Return mVloopOn
        End Get
    End Property

    Public ReadOnly Property IsNloopOn() As Boolean
        Get
            Return mNloopOn
        End Get
    End Property

    Public ReadOnly Property IsBarrierOpen() As Boolean
        Get
            Return mBarrierOpen
        End Get
    End Property

    Public ReadOnly Property ID() As Short
        Get
            Return mID
        End Get
    End Property

    Public ReadOnly Property Name() As String
        Get
            Return mName
        End Get
    End Property

    'Public ReadOnly Property VloopIsOn() As Boolean
    '    Get
    '        Return mVloopOn
    '    End Get
    'End Property

    'Public ReadOnly Property NloopIsOff() As Boolean
    '    Get
    '        Return mNloopOn
    '    End Get
    'End Property

    Public ReadOnly Property BarrierIsOpen() As Boolean
        Get
            Return mBarrierOpen
        End Get
    End Property

    Public ReadOnly Property LastChangedTime() As DateTime
        Get
            Return mLastLoopChangedTime
        End Get
    End Property

    Public ReadOnly Property LatestIU() As String
        Get
            Return mOldIU
        End Get
    End Property

    Public WriteOnly Property DetectInterval() As Integer
        Set(ByVal value As Integer)
            mDetectInterval = value
        End Set
    End Property

#End Region


#Region "Constructor"
    Public Sub New(ByVal id As Short, _
                    ByVal nm As String, _
                    ByVal portName As String, _
                    ByVal antid As Byte, _
                    ByVal protocol As String, _
                    ByVal remote As String)
        mComportName = portName
        mID = id
        mName = nm
        mProtocol = protocol
        mRemotePort = remote
        Dim a() As Byte = {&H10, &H2, antid, &H0, &H3F, &H32, &H3, &H0, &H0, &H10, &H3}
        mSendQueryCmd.AddRange(a)
        BuildSendCmd(mSendQueryCmd)
        mSendThrd = New Thread(AddressOf SendProc)
    End Sub
#End Region

#Region "Public methods"

    Public Sub Run()
        If mProtocol = "serial" Then
            mDevice = New SerialDevice
        Else
            mDevice = New UDPDevice(mRemotePort)
        End If

        mDevice.Open(mComportName)
        Console.WriteLine(mComportName & " is open")
        AddHandler mDevice.DataReceived, AddressOf ProcessReceivedData
        'AddHandler mComPort.ErrorReceived, AddressOf ProcessError
        'AddHandler mComPort.PinChanged, AddressOf processpinchanged
        InitBeforeRun()
        mSendThrd.Start()
    End Sub

    Public Sub BeginRun()
        Dim bd As New executeRun(AddressOf Run)
        bd.BeginInvoke(Nothing, Nothing)
    End Sub

    Public Sub StopOperation()
        mStopSend = True
        Thread.Sleep(3000)
        mDevice.Close()
    End Sub

#End Region

#Region "Protected methods"
    Protected Sub BuildSendCmd(ByVal cmd As List(Of Byte))
        Dim b As List(Of Byte) = cmd.GetRange(2, cmd.Count - 4)
        b.Add(&H3)
        cmd.AddRange(ChangeCRC(b.ToArray()))
    End Sub

    Protected Sub SendMsg(ByVal b() As Byte)
        Try
            mDevice.Write(b, 0, b.Length)
        Catch ex As Exception
            Console.WriteLine(ex.Message)
        End Try

    End Sub

    Protected Overridable Sub PostHandle()
        'Not implemented here
    End Sub

    Protected Overridable Sub InitBeforeRun()
        'Not implemented here
    End Sub

    Protected Sub PauseQuery()
        mPauseSend = True
        Console.WriteLine("---- mWaithandle.WaitOne() will be called---")
        mWaithandle.WaitOne(9000, False)
        Console.WriteLine("---- mWaithandle.WaitOne() ---")
    End Sub

    Protected Sub ResumeQuery()
        mOldIU = String.Empty
        mPauseSend = False
    End Sub

#End Region

#Region "Private methods"

    Private Function ChangeCRC(ByVal dt() As Byte) As Byte()
        Dim j As Long
        Dim D0 As Long, D1 As Long, D3 As Long

        D0 = 65535
        For Each b As Byte In dt
            'If Mid(dt, i, 2) = "10" Then
            '   i = i + 2
            'End If
            D1 = CLng(b) And 255
            For j = 0 To 7
                D3 = D1
                D3 = D3 And &H1
                D3 = D3 Xor D0
                D3 = D3 And &H1
                D0 = Fix(D0 / 2)
                D1 = Fix(D1 / 2)

                If D3 <> 0 Then
                    D0 = D0 Xor 33800    '8408 (hex)
                End If
            Next j
            'Debug.Print D0
        Next
        D0 = Not D0
        'Debug.Print D0
        Dim result As Long = (((D0 And 255) * 256) Or ((D0 And 65280) \ 256))
        Return New Byte() {result \ 256, result Mod 256}
    End Function

    Private Sub SendProc(ByVal obj As Object)
        Static bcnt As Long = 0
        Static tosignal As Boolean = True
        While mStopSend = False
            If Now.Subtract(mLastReceivedTime).TotalSeconds > 10 Then
                If mOnline = True Then
                    mOnline = False
                    RaiseEvent OffLine(Me)
                End If
            End If
            If mID = 3 Then
                Dim ttt As Integer
                ttt = 0
            End If
            Thread.Sleep(mDetectInterval)
            While mPauseSend = True
                If tosignal = True Then
                    mWaithandle.Set()
                    Console.WriteLine("---------mWaithandle.Set() is called")
                End If
                tosignal = False
                Thread.Sleep(1000)
            End While
            tosignal = True
            mDevice.Write(mSendQueryCmd.ToArray, 0, mSendQueryCmd.Count)
            mSendQueryCmd.Item(6) = IIf(bcnt Mod 2 = 0, 3, 2)
            mSendQueryCmd = mSendQueryCmd.GetRange(0, 11)
            BuildSendCmd(mSendQueryCmd)
            bcnt += 1
            Thread.Sleep(50)
        End While
    End Sub

    Private Sub ProcessReceivedData(ByVal aaa As List(Of Byte), ByVal tm As DateTime)
        mLastReceivedTime = tm

        Dim aaa1 As List(Of Byte) = aaa.GetRange(2, aaa.Count - 6)
        aaa1.Add(&H3)
        Dim calCRC() As Byte = ChangeCRC(aaa1.ToArray)
        'Dim rCRC As Long = aaa.Item(aaa.Count - 1) + aaa.Item(aaa.Count - 2) * 256

        If calCRC(0) = aaa.Item(aaa.Count - 2) And calCRC(1) = aaa.Item(aaa.Count - 1) Then
            If aaa1.Item(2) = &H3F And aaa1.Item(3) = &HB2 Then
                CheckStatus(aaa1.Item(5))
                aaa1.Reverse()
                aaa1.RemoveAt(0)

                Dim iu As String = BitConverter.ToString(aaa1.GetRange(0, 5).ToArray).Replace("-"c, "")
                'Console.WriteLine("IU = " & iu)
                If iu <> "1096000001" Then
                    If iu <> mOldIU Then
                        mOldIU = iu
                        mLastDetectedTime = Now
                        RaiseEvent IUDetected(Me, iu, mVloopOn)
                    ElseIf Now.Subtract(mLastDetectedTime).TotalSeconds > 5 Then
                        mLastDetectedTime = Now
                        RaiseEvent IUDetected(Me, iu, mVloopOn)
                    End If
                Else
                    'Faulty IU
                    cnt = cnt + 1
                    If cnt = 1 Then
                        RaiseEvent FaultyIUDetected(Me, iu)
                    ElseIf cnt = 10 Then
                        RaiseEvent FaultyIUDetected(Me, iu)
                        cnt = 0
                    Else

                    End If
                End If
            ElseIf aaa1.Item(2) = &H32 And aaa1.Item(3) = &HC1 Then
                RaiseEvent GetAntDataOK()
            ElseIf aaa1.Item(3) = &H82 Then
                CheckStatus(aaa1.Item(5))
                If aaa1.Item(4) = &H5 Then
                    RaiseEvent GetAntDataNAK()
                ElseIf aaa1.Item(4) = &H6 Then
                    RaiseEvent SetAntDataNAK()
                ElseIf aaa1.Item(4) = &H1 Then
                    RaiseEvent SetDONAK()
                End If
                'Console.WriteLine("NAK received")
            ElseIf aaa1.Item(3) = &H81 Then
                CheckStatus(aaa1.Item(5))
                If aaa1.Item(4) = &H6 Then
                    RaiseEvent SetAntDataOK()
                End If
                Console.WriteLine("ACK received and seq = " & aaa1(4))
            End If

            'Console.WriteLine(True)
        Else
            'Error
        End If

        If mOnline = False Then
            mOnline = True
            If mID = 3 Then
                Dim ttt As Integer
                ttt = 0
            End If
            RaiseEvent OnLine(Me)
        End If

        If mJustStart = True Then
            mJustStart = False
        End If
    End Sub

    Private Sub ProcessError(ByVal sender As Object, ByVal e As SerialErrorReceivedEventArgs)
        Console.WriteLine(CType(sender, SerialPort).PortName & "(SerialErrorReceivedEventArgs): " & e.EventType.ToString)
    End Sub

    Private Sub processpinchanged(ByVal sender As Object, ByVal e As SerialPinChangedEventArgs)
        Console.WriteLine(CType(sender, SerialPort).PortName & "(SerialPinChangedEventArgs): " & e.EventType.ToString)
    End Sub

    Private Sub CheckStatus(ByVal b As Byte)
        If (b And &H20) = 0 Then
            mLevelIsLow = True
            SyncLock mLockEvtLevelLow
                If Not mEvtLevelIsLow Is Nothing Then
                    mEvtLevelIsLow.Set()
                End If
            End SyncLock
        Else
            mLevelIsLow = False
        End If
        If (b And &H1) > 0 Then
            If ID = 2 Then
                Console.WriteLine(True)
            End If
            If mVloopOn = False Then
                mVloopOn = True
                If Now.Subtract(mLastDetectedTime).Seconds < 5 Then
                    RaiseEvent VloopON(Me, mOldIU)
                Else
                    RaiseEvent VloopON(Me, Nothing)
                End If

                mLastLoopChangedTime = Now
            End If
        Else
            If mVloopOn = True Then
                mVloopOn = False
                RaiseEvent VloopOFF(Me)
                mLastLoopChangedTime = Now
            End If
        End If
        If (b And &H2) > 0 Then
            If mNloopOn = False Then
                mNloopOn = True
                RaiseEvent NloopON(Me)
                mLastLoopChangedTime = Now
            End If
            If (b And &H20) > 0 Then
                PostHandle()
            End If
        Else
            If mNloopOn = True Then
                mNloopOn = False
                RaiseEvent NloopOFF(Me)
                mLastLoopChangedTime = Now
            End If
        End If
        If (b And &H4) > 0 Then
            If mBarrierOpen = False Then
                mBarrierOpen = True
                RaiseEvent BarrierOpen(Me)
            End If
        Else
            If mBarrierOpen = True Then
                mBarrierOpen = False
                RaiseEvent BarrierClose(Me)
            End If
        End If
    End Sub

#End Region

End Class
