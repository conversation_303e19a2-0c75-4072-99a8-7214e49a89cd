Imports System.Net.Sockets
Imports System.Net
Imports Parkrite.SystemShared
Public Class UDPDevice
    Implements ICommunicatedevice

    Private mUdpClient As UdpClient
    Private mIsStop As Boolean = False
    Private mRemoteHost As String
    Private mRemotePort As Integer


    Public Sub New(ByVal remoteHost As String)
        Dim strs() As String = remoteHost.Split(":"c)
        mRemoteHost = strs(0)
        mRemotePort = strs(1)
    End Sub


    Public Sub Close() Implements ICommunicatedevice.Close
        mIsStop = True
        mUdpClient.Close()
    End Sub

    Public Event DataReceived(ByVal data As System.Collections.Generic.List(Of Byte), ByVal receivedTime As Date) Implements ICommunicatedevice.DataReceived

    Public Function Open(ByVal portnm As String) As ICommunicatedevice Implements ICommunicatedevice.Open
        Dim strs() As String = portnm.Split(":"c)
        mUdpClient = New UdpClient(New IPEndPoint(IPAddress.Parse(strs(0)), strs(1)))
        Dim thrd As New Threading.Thread(AddressOf ReceiveProc)
        thrd.Name = "UDPReceiveProc"
        thrd.Start()
        Return Me
    End Function

    Public Sub Write(ByVal Buffer() As Byte, ByVal offset As Integer, ByVal count As Integer) Implements ICommunicatedevice.Write
        Try
            mUdpClient.Send(Buffer, count, mRemoteHost, mRemotePort)
        Catch ex As Exception
            Console.Write(True)
        End Try
    End Sub


    Private Sub ReceiveProc()
        Dim n() As Byte
        Dim isEnd As Boolean = False
        Dim numofDEL As Byte = 0
        Dim i As Integer = 0
        While Not mIsStop
            Try
Start:
                If (n Is Nothing) OrElse (i = n.Length) Then
                    n = mUdpClient.Receive(New IPEndPoint(IPAddress.Parse(mRemoteHost), mRemotePort))
                    'Console.WriteLine("1--- " & BitConverter.ToString(n, 0, n.Length))
                    i = 0
                End If

                Dim aaa As New List(Of Byte)
                Dim b As Byte
                Do
                    If i = n.Length Then
                        n = mUdpClient.Receive(New IPEndPoint(IPAddress.Parse(mRemoteHost), mRemotePort))
                        'Console.WriteLine("2--- " & BitConverter.ToString(n, 0, n.Length))
                        i = 0
                    End If
                    b = n(i)
                    i += 1
                    If aaa.Count = 1 Then
                        If b <> &H2 Then
                            gLogger.Log("UDPDevice::ReceiveProc - exit because b <> 0x02")
                            If n.Length > 0 Then
                                gLogger.Log(BitConverter.ToString(n))
                            Else
                                gLogger.Log("n.Length = 0")
                            End If
                            gLogger.Log("numofDEL = " & numofDEL.ToString)
                            gLogger.Log("i = " & i.ToString)
                            gLogger.Log("isEnd = " & isEnd.ToString)
                            If aaa.Count > 0 Then
                                gLogger.Log(BitConverter.ToString(aaa.ToArray()))
                            Else
                                gLogger.Log("aaa.Count = 0")
                            End If
                            aaa.Clear()
                            GoTo Start 'Change from return to GoTo Start
                        End If
                    End If
                    If b = &H10 Then
                        numofDEL += 1
                        If numofDEL = 2 Then
                            numofDEL = 0
                        Else
                            aaa.Add(b)
                        End If
                    Else
                        If aaa.Count = 0 Then
                            gLogger.Log("UDPDevice::ReceiveProc - exit because of count of aaa.Count = 0")
                            If n.Length > 0 Then
                                gLogger.Log(BitConverter.ToString(n))
                            Else
                                gLogger.Log("n.Length = 0")
                            End If
                            gLogger.Log("numofDEL = " & numofDEL.ToString)
                            gLogger.Log("i = " & i.ToString)
                            gLogger.Log("isEnd = " & isEnd.ToString)
                            n = Nothing
                            GoTo Start
                        End If
                        If numofDEL = 1 Then
                            If b = &H3 Then
                                isEnd = True
                            Else
                                numofDEL = 0
                            End If
                        End If
                        aaa.Add(b)
                    End If
                Loop Until isEnd = True

                If i = n.Length() Then
                    n = mUdpClient.Receive(New IPEndPoint(IPAddress.Parse(mRemoteHost), mRemotePort))
                    'Console.WriteLine("3--- " & BitConverter.ToString(n, 0, n.Length))
                    i = 0
                End If
                aaa.Add(n(i))
                i += 1
                If i = n.Length() Then
                    n = mUdpClient.Receive(New IPEndPoint(IPAddress.Parse(mRemoteHost), mRemotePort))
                    'Console.WriteLine("4--- " & BitConverter.ToString(n, 0, n.Length))
                    i = 0
                End If
                aaa.Add(n(i))
                i += 1
                RaiseEvent DataReceived(aaa, Now)
                'Console.WriteLine("Message received from remote antenna!" & BitConverter.ToString(aaa.ToArray()))
                isEnd = False
                numofDEL = 0
            Catch ex As Exception
                gLogger.Log(ex.Message)
                gLogger.Log(ex.StackTrace)
                gLogger.Log(IIf(ex.InnerException Is Nothing, "No innerException", ex.InnerException.InnerException.Message))
            End Try

        End While
        gLogger.Log("----- Exit from UDPDevice::ReceiveProc ------")
    End Sub
End Class
